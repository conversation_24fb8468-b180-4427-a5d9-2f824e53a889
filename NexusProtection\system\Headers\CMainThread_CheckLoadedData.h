/**
 * @file CMainThread_CheckLoadedData.h
 * @brief Data validation system for CMainThread
 * 
 * Refactored from check_loaded_data function
 * This handles the validation of all loaded game data to ensure integrity
 * and completeness before server startup.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>

// Forward declarations
class CMainThread;
class CRecordData;

/**
 * @enum DataValidationType
 * @brief Enumeration of all data validation types
 */
enum class DataValidationType : uint32_t {
    // Core Data Tables
    ItemData = 0,
    SkillData,
    ForceData,
    ClassSkillData,
    BulletItemEffectData,
    
    // Character Data
    ClassData,
    GradeData,
    PlayerCharacterData,
    MonsterCharacterData,
    NPCharacterData,
    
    // Item Systems
    AnimusItemData,
    ExpData,
    ItemLootingData,
    OreCuttingData,
    ItemMakeData,
    ItemCombineData,
    ItemExchangeData,
    ItemUpgradeData,
    
    // Unit Data
    UnitHeadData,
    UnitUpperData,
    UnitLowerData,
    UnitArmsData,
    UnitShoulderData,
    UnitBackData,
    UnitBulletData,
    UnitFrameData,
    
    // System Data
    EditData,
    MonsterCharacterAIData,
    MobMessageData,
    
    // Manager Systems
    PotionSystem,
    QuestSystem,
    ItemCombineSystem,
    PcBangSystem,
    SUItemSystem,
    MonsterSPGroupTable,
    
    // Configuration Data
    AggroCalculateConfig,
    MonsterSetConfig,
    
    MAX_VALIDATION_TYPES
};

/**
 * @enum DataValidationResult
 * @brief Result codes for data validation operations
 */
enum class DataValidationResult : int32_t {
    Success = 1,
    Failure = 0,
    DataNotLoaded = -1,
    InvalidData = -2,
    CorruptedData = -3,
    MissingRecords = -4,
    ValidationError = -5,
    SecurityError = -6
};

/**
 * @struct DataValidationInfo
 * @brief Information about a data validation check
 */
struct DataValidationInfo {
    std::string name;
    std::string description;
    bool isRequired;
    uint32_t minRecords;
    uint32_t maxRecords;
    std::function<bool(const void*)> customValidator;
    
    DataValidationInfo() = default;
    DataValidationInfo(const std::string& n, const std::string& desc, bool required = true, 
                      uint32_t minRec = 0, uint32_t maxRec = UINT32_MAX)
        : name(n), description(desc), isRequired(required), minRecords(minRec), maxRecords(maxRec) {}
};

/**
 * @struct DataValidationStats
 * @brief Statistics for data validation process
 */
struct DataValidationStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(DataValidationType::MAX_VALIDATION_TYPES)> validationStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(DataValidationType::MAX_VALIDATION_TYPES)> validationTime;
    std::array<uint32_t, static_cast<size_t>(DataValidationType::MAX_VALIDATION_TYPES)> recordCount;
    uint32_t successfulValidations;
    uint32_t failedValidations;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        validationStatus.fill(false);
        validationTime.fill(std::chrono::milliseconds::zero());
        recordCount.fill(0);
        successfulValidations = 0;
        failedValidations = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalValidationTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulValidations + failedValidations;
        return total > 0 ? (static_cast<double>(successfulValidations) / total) * 100.0 : 0.0;
    }
    
    uint32_t GetTotalRecordCount() const {
        uint32_t total = 0;
        for (const auto& count : recordCount) {
            total += count;
        }
        return total;
    }
};

/**
 * @class CMainThreadCheckLoadedData
 * @brief Data validation and integrity checking system
 * 
 * This class handles the validation of all loaded game data to ensure
 * integrity and completeness before server startup. It provides modern C++20 
 * interfaces while maintaining compatibility with the original validation logic.
 * 
 * Original function: CMainThread::check_loaded_data
 * Refactored to modern C++20 with comprehensive error handling and monitoring.
 */
class CMainThreadCheckLoadedData {
public:
    /**
     * @brief Constructor
     */
    CMainThreadCheckLoadedData();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadCheckLoadedData();
    
    // Delete copy constructor and assignment operator
    CMainThreadCheckLoadedData(const CMainThreadCheckLoadedData&) = delete;
    CMainThreadCheckLoadedData& operator=(const CMainThreadCheckLoadedData&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadCheckLoadedData(CMainThreadCheckLoadedData&&) = default;
    CMainThreadCheckLoadedData& operator=(CMainThreadCheckLoadedData&&) = default;
    
    /**
     * @brief Main data validation function
     * 
     * Validates all loaded game data with comprehensive error handling.
     * This is the modern refactored version of the original check_loaded_data function.
     * 
     * @param mainThread Pointer to CMainThread instance for accessing data
     * @return DataValidationResult indicating success or failure
     */
    DataValidationResult ValidateAllLoadedData(CMainThread* mainThread);
    
    /**
     * @brief Legacy check_loaded_data function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: bool __fastcall CMainThread::check_loaded_data(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return bool (true for success, false for failure)
     */
    static bool CheckLoadedData_Legacy(CMainThread* mainThread);
    
    /**
     * @brief Get data validation statistics
     * @return const reference to validation statistics
     */
    const DataValidationStats& GetValidationStats() const;
    
    /**
     * @brief Check if a specific data type is validated
     * @param validationType The data validation type to check
     * @return true if validated, false otherwise
     */
    bool IsDataValidated(DataValidationType validationType) const;
    
    /**
     * @brief Get the last validation error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get information about a specific validation type
     * @param validationType The validation type
     * @return DataValidationInfo structure with validation information
     */
    static DataValidationInfo GetValidationInfo(DataValidationType validationType);
    
    /**
     * @brief Validate all required data is present and valid
     * @return true if all required data is validated
     */
    bool ValidateRequiredData() const;

private:
    // Validation statistics and monitoring
    DataValidationStats m_validationStats;
    mutable std::mutex m_statsMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // Validation registry
    static std::unordered_map<DataValidationType, DataValidationInfo> s_validationRegistry;
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Core validation phases
    DataValidationResult ValidateCoreDataTables(CMainThread* mainThread);
    DataValidationResult ValidateCharacterData(CMainThread* mainThread);
    DataValidationResult ValidateItemSystems(CMainThread* mainThread);
    DataValidationResult ValidateUnitData(CMainThread* mainThread);
    DataValidationResult ValidateSystemData(CMainThread* mainThread);
    DataValidationResult ValidateManagerSystems(CMainThread* mainThread);
    DataValidationResult ValidateConfigurationData(CMainThread* mainThread);
    
    // Individual validation functions
    DataValidationResult ValidateRecordData(CRecordData* recordData, DataValidationType validationType);
    DataValidationResult ValidateItemData(CMainThread* mainThread);
    DataValidationResult ValidateEffectData(CMainThread* mainThread);
    DataValidationResult ValidateClassData(CMainThread* mainThread);
    DataValidationResult ValidatePlayerData(CMainThread* mainThread);
    DataValidationResult ValidateMonsterData(CMainThread* mainThread);
    DataValidationResult ValidateNPCData(CMainThread* mainThread);
    DataValidationResult ValidateAnimusData(CMainThread* mainThread);
    DataValidationResult ValidateExpData(CMainThread* mainThread);
    DataValidationResult ValidateUnitPartData(CMainThread* mainThread);
    DataValidationResult ValidateMobMessageData(CMainThread* mainThread);
    DataValidationResult ValidatePotionSystem(CMainThread* mainThread);
    DataValidationResult ValidateQuestSystem(CMainThread* mainThread);
    DataValidationResult ValidateItemCombineSystem(CMainThread* mainThread);
    
    // Utility functions
    void LogValidation(DataValidationType validationType, bool success, uint32_t recordCount = 0, 
                      const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    bool ValidateRecordCount(uint32_t actualCount, uint32_t minCount, uint32_t maxCount);
    bool ValidateRecordIntegrity(const void* recordData, size_t recordSize);
    
    // Static initialization
    static void InitializeValidationRegistry();
    
    // Legacy compatibility functions
    static bool ValidateDataTable_Legacy(CRecordData* recordData, const char* tableName);
    static bool CheckManagerSystem_Legacy(const void* manager, const char* systemName);
};

/**
 * @brief Convert DataValidationType enum to string for logging
 * @param validationType The validation type
 * @return String representation of the validation type
 */
std::string DataValidationTypeToString(DataValidationType validationType);

/**
 * @brief Convert DataValidationResult enum to string for logging
 * @param result The validation result
 * @return String representation of the result
 */
std::string DataValidationResultToString(DataValidationResult result);

// External function declarations (to be properly linked)
extern uint32_t CRecordData_GetRecordNum(CRecordData* recordData);
extern void* CRecordData_GetRecord(CRecordData* recordData, int index);
extern bool CRecordData_IsValid(CRecordData* recordData);

// External global objects and managers
extern class CPotionMgr g_PotionMgr;
extern class CQuestMgr* g_pQuestMgr;
extern class ItemCombineMgr* g_pItemCombineMgr;
extern class CPcBangFavor* g_pPcBangFavor;
extern class CSUItemSystem* g_pSUItemSystem;
extern class CMonsterSPGroupTable* g_pMonsterSPGroupTable;
