#pragma once

#include <cstdint>
#include <chrono>

// Forward declarations
class CMonster;
struct _effect_parameter;

/**
 * @brief Monster recovery system for handling HP auto-recovery and manual recovery
 * 
 * This class manages the monster's health recovery mechanisms, including:
 * - Automatic HP recovery based on monster configuration
 * - Manual recovery with effect parameters
 * - Recovery timing and validation
 * 
 * Refactored from:
 * - AutoRecoverCMonsterQEAAXXZ_140147440.c (Address: 0x140147440)
 * - CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.c (Address: 0x140143370)
 */
class MonsterRecovery {
public:
    /**
     * @brief Recovery configuration parameters
     */
    struct RecoveryConfig {
        float hpRecoveryDelay = 0.0f;      // Delay between recovery ticks (seconds)
        float hpRecoveryUnit = 0.0f;       // Amount of HP recovered per tick
        bool isRecoveryEnabled = false;    // Whether recovery is enabled
        int32_t minHPThreshold = 0;        // Minimum HP threshold for recovery
        
        RecoveryConfig() = default;
        RecoveryConfig(float delay, float unit, bool enabled, int32_t threshold = 0)
            : hpRecoveryDelay(delay), hpRecoveryUnit(unit), 
              isRecoveryEnabled(enabled), minHPThreshold(threshold) {}
    };

    /**
     * @brief Recovery statistics for monitoring
     */
    struct RecoveryStats {
        uint64_t totalRecoveryTicks = 0;
        uint64_t totalHPRecovered = 0;
        std::chrono::steady_clock::time_point lastRecoveryTime;
        float averageRecoveryRate = 0.0f;
        
        RecoveryStats() : lastRecoveryTime(std::chrono::steady_clock::now()) {}
    };

public:
    MonsterRecovery() = default;
    ~MonsterRecovery() = default;

    // Non-copyable but movable
    MonsterRecovery(const MonsterRecovery&) = delete;
    MonsterRecovery& operator=(const MonsterRecovery&) = delete;
    MonsterRecovery(MonsterRecovery&&) = default;
    MonsterRecovery& operator=(MonsterRecovery&&) = default;

    /**
     * @brief Initialize recovery system for a monster
     * @param monster Pointer to the monster
     * @param config Recovery configuration
     */
    void Initialize(CMonster* monster, const RecoveryConfig& config);

    /**
     * @brief Check and perform automatic HP recovery
     * @param monster Pointer to the monster
     * @return true if recovery was performed, false otherwise
     */
    bool CheckAutoRecoverHP(CMonster* monster);

    /**
     * @brief Perform manual recovery with effect parameters
     * @param monster Pointer to the monster
     * @param recoveryAmount Additional recovery amount (can be negative)
     */
    void AutoRecover(CMonster* monster, float recoveryAmount = 0.0f);

    /**
     * @brief Update recovery configuration
     * @param config New recovery configuration
     */
    void UpdateConfig(const RecoveryConfig& config);

    /**
     * @brief Get current recovery configuration
     * @return Current recovery configuration
     */
    const RecoveryConfig& GetConfig() const { return m_config; }

    /**
     * @brief Get recovery statistics
     * @return Recovery statistics
     */
    const RecoveryStats& GetStats() const { return m_stats; }

    /**
     * @brief Reset recovery statistics
     */
    void ResetStats();

    /**
     * @brief Check if recovery is currently active
     * @return true if recovery is active, false otherwise
     */
    bool IsRecoveryActive() const { return m_isActive; }

    /**
     * @brief Enable or disable recovery system
     * @param enabled Whether to enable recovery
     */
    void SetRecoveryEnabled(bool enabled);

private:
    /**
     * @brief Validate recovery conditions
     * @param monster Pointer to the monster
     * @return true if recovery conditions are met, false otherwise
     */
    bool ValidateRecoveryConditions(CMonster* monster) const;

    /**
     * @brief Calculate recovery amount based on effects
     * @param monster Pointer to the monster
     * @param baseAmount Base recovery amount
     * @return Final recovery amount
     */
    float CalculateRecoveryAmount(CMonster* monster, float baseAmount) const;

    /**
     * @brief Apply HP recovery to monster
     * @param monster Pointer to the monster
     * @param amount Amount to recover
     * @return true if recovery was applied, false otherwise
     */
    bool ApplyHPRecovery(CMonster* monster, float amount);

    /**
     * @brief Update recovery statistics
     * @param recoveredAmount Amount of HP recovered
     */
    void UpdateStats(float recoveredAmount);

    /**
     * @brief Get current time in milliseconds
     * @return Current time in milliseconds
     */
    uint64_t GetCurrentTimeMs() const;

private:
    RecoveryConfig m_config;                    // Recovery configuration
    RecoveryStats m_stats;                      // Recovery statistics
    bool m_isActive = false;                    // Whether recovery is active
    uint64_t m_lastRecoveryTime = 0;           // Last recovery time (milliseconds)
    CMonster* m_associatedMonster = nullptr;    // Associated monster
};

/**
 * @brief Utility functions for monster recovery
 */
namespace MonsterRecoveryUtils {
    /**
     * @brief Create default recovery configuration for monster type
     * @param monsterType Type of monster
     * @return Default recovery configuration
     */
    MonsterRecovery::RecoveryConfig CreateDefaultConfig(int32_t monsterType);

    /**
     * @brief Validate recovery configuration
     * @param config Configuration to validate
     * @return true if configuration is valid, false otherwise
     */
    bool ValidateConfig(const MonsterRecovery::RecoveryConfig& config);

    /**
     * @brief Calculate optimal recovery rate based on monster stats
     * @param maxHP Maximum HP of monster
     * @param level Monster level
     * @return Optimal recovery rate
     */
    float CalculateOptimalRecoveryRate(int32_t maxHP, int32_t level);

    /**
     * @brief Format recovery statistics for logging
     * @param stats Recovery statistics
     * @return Formatted string
     */
    std::string FormatRecoveryStats(const MonsterRecovery::RecoveryStats& stats);
}
