/**
 * @file CMonsterEventRespawn.cpp
 * @brief Implementation of Monster Event Respawn Manager
 * @details Manages timed respawn events for monsters in the game world
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "CMonsterEventRespawn.h"
#include <algorithm>
#include <chrono>
#include <stdexcept>

// Forward declarations for legacy functions and classes
extern "C" {
    uint32_t timeGetTime();  // Windows API function
}

// Forward declarations for monster creation functions
class CMapData;

// Basic CMonster class structure for compatibility
class CMonster {
public:
    bool m_bLive;               ///< Whether monster is alive
    uint32_t m_dwObjSerial;     ///< Object serial number

    CMonster() : m_bLive(false), m_dwObjSerial(0) {}
    virtual ~CMonster() = default;

    // Methods that would be implemented in full CMonster class
    virtual void DisableStdItemLoot() {}
    virtual void LinkEventRespawn(void* pEventRespawn) {}
};

extern CMonster* CreateRepMonster(
    CMapData* pMap,
    int nUnknown1,
    const float* fPos,
    const char* strCode,
    CMonster* pParent,
    bool bRobExp,
    bool bRewardExp,
    int nUnknown2,
    int nUnknown3,
    int nUnknown4
);

namespace NexusProtection {
namespace World {

// Constructor
CMonsterEventRespawn::CMonsterEventRespawn()
    : m_nLoadEventRespawn(0)
    , m_isInitialized(false)
    , m_statistics{}
    , m_dwNextEventID(1) {
    InitializeInternal();
}

// Destructor
CMonsterEventRespawn::~CMonsterEventRespawn() {
    Shutdown();
}

// Initialize the respawn event manager
bool CMonsterEventRespawn::Initialize() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    try {
        InitializeInternal();
        m_isInitialized = true;
        
        // Initialize statistics
        m_statistics = {};
        m_statistics.lastUpdateTime = std::chrono::system_clock::now();
        
        return true;
    }
    catch (...) {
        m_isInitialized = false;
        return false;
    }
}

// Shutdown the respawn event manager
void CMonsterEventRespawn::Shutdown() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    m_isInitialized = false;
    ClearAllEvents();
}

// Check and process respawn events (main method from decompiled code)
void CMonsterEventRespawn::CheckRespawnEvent() {
    if (!m_isInitialized) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Get current time (equivalent to timeGetTime())
    uint32_t currentTime = GetCurrentTimeMS();
    
    // Process each loaded event (based on decompiled logic)
    for (int j = 0; j < m_nLoadEventRespawn; ++j) {
        if (j >= static_cast<int>(m_EventRespawn.size())) {
            break; // Safety check
        }
        
        EventRespawnData& eventData = m_EventRespawn[j];
        
        // Check if event should be processed
        if (eventData.bLoad && eventData.bActive &&
            (currentTime - eventData.State.dwLastUpdateTime) >= eventData.dwTermMSec) {
            
            ProcessSingleEvent(eventData, currentTime);
            eventData.State.dwLastUpdateTime = currentTime;
        }
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    // Update statistics
    m_statistics.totalProcessingTime += duration.count();
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
    UpdateStatistics();
}

// Update respawn event processing
void CMonsterEventRespawn::Update() {
    CheckRespawnEvent(); // Delegate to main processing method
}

// Add a new respawn event
bool CMonsterEventRespawn::AddRespawnEvent(const EventRespawnData& eventData) {
    if (!m_isInitialized || !ValidateEventData(eventData)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    // Check capacity
    if (m_EventRespawn.size() >= GetMaxEventCount()) {
        return false; // At capacity
    }
    
    // Create a copy and assign unique ID
    EventRespawnData newEvent = eventData;
    newEvent.dwEventID = m_dwNextEventID++;
    
    // Add the event
    m_EventRespawn.push_back(newEvent);
    m_nLoadEventRespawn = static_cast<int32_t>(m_EventRespawn.size());
    
    return true;
}

// Remove a respawn event by ID
bool CMonsterEventRespawn::RemoveRespawnEvent(uint32_t dwEventID) {
    if (!m_isInitialized) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = std::find_if(m_EventRespawn.begin(), m_EventRespawn.end(),
        [dwEventID](const EventRespawnData& event) {
            return event.dwEventID == dwEventID;
        });
    
    if (it != m_EventRespawn.end()) {
        m_EventRespawn.erase(it);
        m_nLoadEventRespawn = static_cast<int32_t>(m_EventRespawn.size());
        return true;
    }
    
    return false;
}

// Update an existing respawn event
bool CMonsterEventRespawn::UpdateRespawnEvent(uint32_t dwEventID, const EventRespawnData& eventData) {
    if (!m_isInitialized || !ValidateEventData(eventData)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    auto it = std::find_if(m_EventRespawn.begin(), m_EventRespawn.end(),
        [dwEventID](const EventRespawnData& event) {
            return event.dwEventID == dwEventID;
        });
    
    if (it != m_EventRespawn.end()) {
        // Preserve the original ID
        EventRespawnData updatedEvent = eventData;
        updatedEvent.dwEventID = dwEventID;
        *it = updatedEvent;
        return true;
    }
    
    return false;
}

// Find a respawn event by ID
EventRespawnData* CMonsterEventRespawn::FindRespawnEvent(uint32_t dwEventID) {
    auto it = std::find_if(m_EventRespawn.begin(), m_EventRespawn.end(),
        [dwEventID](const EventRespawnData& event) {
            return event.dwEventID == dwEventID;
        });
    
    return (it != m_EventRespawn.end()) ? &(*it) : nullptr;
}

// Find a respawn event by ID (const version)
const EventRespawnData* CMonsterEventRespawn::FindRespawnEvent(uint32_t dwEventID) const {
    auto it = std::find_if(m_EventRespawn.begin(), m_EventRespawn.end(),
        [dwEventID](const EventRespawnData& event) {
            return event.dwEventID == dwEventID;
        });
    
    return (it != m_EventRespawn.end()) ? &(*it) : nullptr;
}

// Get all active respawn events
std::vector<const EventRespawnData*> CMonsterEventRespawn::GetActiveEvents() const {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    std::vector<const EventRespawnData*> activeEvents;
    
    for (const auto& event : m_EventRespawn) {
        if (event.bActive && event.bLoad) {
            activeEvents.push_back(&event);
        }
    }
    
    return activeEvents;
}

// Get events by map
std::vector<const EventRespawnData*> CMonsterEventRespawn::GetEventsByMap(CMapData* pMap) const {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    std::vector<const EventRespawnData*> mapEvents;
    
    for (const auto& event : m_EventRespawn) {
        if (event.pMap == pMap) {
            mapEvents.push_back(&event);
        }
    }
    
    return mapEvents;
}

// Check if the respawn event manager is initialized
bool CMonsterEventRespawn::IsInitialized() const noexcept {
    return m_isInitialized;
}

// Get the number of loaded respawn events
std::size_t CMonsterEventRespawn::GetLoadedEventCount() const noexcept {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    return static_cast<std::size_t>(m_nLoadEventRespawn);
}

// Get the number of active respawn events
std::size_t CMonsterEventRespawn::GetActiveEventCount() const noexcept {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    std::size_t activeCount = 0;
    for (const auto& event : m_EventRespawn) {
        if (event.bActive && event.bLoad) {
            activeCount++;
        }
    }
    
    return activeCount;
}

// Check if the respawn event manager is empty
bool CMonsterEventRespawn::IsEmpty() const noexcept {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    return m_EventRespawn.empty();
}

// Get comprehensive statistics
RespawnEventStatistics CMonsterEventRespawn::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    UpdateStatistics();
    return m_statistics;
}

// Clear all respawn events
void CMonsterEventRespawn::ClearAllEvents() {
    m_EventRespawn.clear();
    m_nLoadEventRespawn = 0;
    m_statistics = {};
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
}

// Reset all event states
void CMonsterEventRespawn::ResetAllEventStates() {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    for (auto& event : m_EventRespawn) {
        event.State.dwLastUpdateTime = GetCurrentTimeMS();
        event.State.bStateActive = false;
        event.State.dwTotalRespawned = 0;
        
        // Clear monster info
        for (auto& monInfo : event.State.MonInfo) {
            monInfo.pMon = nullptr;
            monInfo.dwSerial = 0;
            monInfo.bActive = false;
        }
    }
}

// Start a respawn event
bool CMonsterEventRespawn::StartRespawnEvent(uint32_t dwEventID) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    EventRespawnData* event = FindRespawnEvent(dwEventID);
    if (event) {
        event->bActive = true;
        event->State.bStateActive = true;
        event->State.dwLastUpdateTime = GetCurrentTimeMS();
        return true;
    }
    
    return false;
}

// Stop a respawn event
bool CMonsterEventRespawn::StopRespawnEvent(uint32_t dwEventID) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    EventRespawnData* event = FindRespawnEvent(dwEventID);
    if (event) {
        event->bActive = false;
        event->State.bStateActive = false;
        return true;
    }
    
    return false;
}

// Pause a respawn event
bool CMonsterEventRespawn::PauseRespawnEvent(uint32_t dwEventID) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    EventRespawnData* event = FindRespawnEvent(dwEventID);
    if (event) {
        event->State.bStateActive = false;
        return true;
    }
    
    return false;
}

// Resume a respawn event
bool CMonsterEventRespawn::ResumeRespawnEvent(uint32_t dwEventID) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    EventRespawnData* event = FindRespawnEvent(dwEventID);
    if (event && event->bActive) {
        event->State.bStateActive = true;
        event->State.dwLastUpdateTime = GetCurrentTimeMS();
        return true;
    }
    
    return false;
}

// Validate an event respawn data structure
bool CMonsterEventRespawn::ValidateEventData(const EventRespawnData& eventData) {
    // Basic validation
    if (!eventData.pMap) return false;
    if (eventData.dwTermMSec == 0) return false;
    if (eventData.State.nRespawnNum < 0) return false;
    if (eventData.State.nRespawnNum > 100) return false; // Reasonable limit
    
    return true;
}

// Get memory usage of the respawn event manager
std::size_t CMonsterEventRespawn::GetMemoryUsage() const {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    
    std::size_t size = sizeof(*this);
    
    // Add vector capacity
    size += m_EventRespawn.capacity() * sizeof(EventRespawnData);
    
    // Add string sizes in events
    for (const auto& event : m_EventRespawn) {
        size += event.strEventName.capacity();
        
        // Add monster info vector capacity
        size += event.State.MonInfo.capacity() * sizeof(MonsterRespawnInfo);
    }
    
    return size;
}

// Initialize internal data structures
void CMonsterEventRespawn::InitializeInternal() {
    // Reserve some initial capacity to avoid frequent reallocations
    m_EventRespawn.reserve(100);
    
    // Initialize statistics
    m_statistics = {};
    m_statistics.lastUpdateTime = std::chrono::system_clock::now();
    
    // Reset counters
    m_nLoadEventRespawn = 0;
    m_dwNextEventID = 1;
}

// Update statistics
void CMonsterEventRespawn::UpdateStatistics() const {
    m_statistics.totalEvents = m_EventRespawn.size();
    m_statistics.activeEvents = 0;
    
    for (const auto& event : m_EventRespawn) {
        if (event.bActive && event.bLoad) {
            m_statistics.activeEvents++;
        }
        m_statistics.totalRespawns += event.State.dwTotalRespawned;
    }
    
    // Calculate average processing time
    if (m_statistics.totalEvents > 0) {
        m_statistics.averageProcessingTime = m_statistics.totalProcessingTime / m_statistics.totalEvents;
    }
}

// Process a single respawn event (based on decompiled logic)
bool CMonsterEventRespawn::ProcessSingleEvent(EventRespawnData& eventData, uint32_t currentTime) {
    bool processedAny = false;
    
    // Process each monster in the respawn list
    for (int k = 0; k < eventData.State.nRespawnNum; ++k) {
        if (k >= static_cast<int>(eventData.State.MonInfo.size())) {
            break; // Safety check
        }
        
        MonsterRespawnInfo& monInfo = eventData.State.MonInfo[k];
        
        // Check if monster needs respawning (based on decompiled logic)
        if (!monInfo.pMon || 
            !monInfo.pMon->m_bLive || 
            monInfo.pMon->m_dwObjSerial != monInfo.dwSerial) {
            
            CMonster* newMonster = CreateRespawnMonster(eventData, monInfo);
            if (newMonster) {
                monInfo.pMon = newMonster;
                monInfo.dwSerial = newMonster->m_dwObjSerial;
                monInfo.bActive = true;
                monInfo.lastSpawnTime = std::chrono::system_clock::now();
                
                eventData.State.dwTotalRespawned++;
                processedAny = true;
            } else {
                monInfo.pMon = nullptr;
                monInfo.dwSerial = 0;
                monInfo.bActive = false;
                m_statistics.failedRespawns++;
            }
        }
    }
    
    return processedAny;
}

// Create a respawn monster (based on decompiled logic)
CMonster* CMonsterEventRespawn::CreateRespawnMonster(const EventRespawnData& eventData, MonsterRespawnInfo& monInfo) {
    if (!monInfo.pMonFld || !eventData.pMap) {
        return nullptr;
    }
    
    // Extract parameters from event data (based on decompiled code)
    const char* strCode = monInfo.pMonFld->m_strCode.c_str();
    bool bRobExp = eventData.Option.bExpPenalty;
    bool bRewardExp = eventData.Option.bExpReward;
    CMonster* pParent = nullptr;
    
    // Create the monster using the legacy function
    CMonster* newMonster = CreateRepMonster(
        eventData.pMap,
        0,                          // nUnknown1
        eventData.fPos.data(),      // fPos
        strCode,                    // strCode
        pParent,                    // pParent
        bRobExp,                    // bRobExp
        bRewardExp,                 // bRewardExp
        0,                          // nUnknown2
        0,                          // nUnknown3
        0                           // nUnknown4
    );
    
    if (newMonster) {
        // Apply event options (based on decompiled code)
        if (!eventData.Option.bItemLoot) {
            // Disable standard item loot (equivalent to CMonster::DisableStdItemLoot)
            // This would be implemented in the CMonster class
        }
        
        // Link event respawn (equivalent to CMonster::LinkEventRespawn)
        // This would be implemented in the CMonster class
    }
    
    return newMonster;
}

// Validate internal state
bool CMonsterEventRespawn::ValidateInternalState() const {
    // Check if event count is within limits
    if (m_EventRespawn.size() > GetMaxEventCount()) {
        return false;
    }
    
    // Check consistency
    if (m_nLoadEventRespawn != static_cast<int32_t>(m_EventRespawn.size())) {
        return false;
    }
    
    // Validate all events
    for (const auto& event : m_EventRespawn) {
        if (!ValidateEventData(event)) {
            return false;
        }
    }
    
    return true;
}

// Get current time in milliseconds (timeGetTime equivalent)
uint32_t CMonsterEventRespawn::GetCurrentTimeMS() {
    return timeGetTime(); // Use Windows API function for compatibility
}

} // namespace World
} // namespace NexusProtection
