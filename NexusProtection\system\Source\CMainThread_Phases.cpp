/**
 * @file CMainThread_Phases.cpp
 * @brief Implementation of initialization phases for CMainThread
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>

// External function declarations (to be properly linked)
extern bool CMainThread_LoadINI(CMainThread* pThis);
extern bool CMainThread_CheckDefine(CMainThread* pThis);
extern bool CMainThread_check_dbsyn_data_size(CMainThread* pThis);
extern bool CMainThread_DataFileInit(CMainThread* pThis);
extern bool CMainThread_ObjectInit(CMainThread* pThis);
extern bool CMainThread_NetworkInit(CMainThread* pThis);
extern bool CMapOperation_Init(CMapOperation* pMapOper);
extern bool CMonsterEventRespawn_SetEventRespawn(CMonsterEventRespawn* pRespawn);
extern bool CMonsterEventSet_LoadEventSet(CMonsterEventSet* pEventSet, char* pErrorCode);
extern bool CMonsterEventSet_LoadEventSetLooting(CMonsterEventSet* pEventSet);
extern int CAsyncLogger_Init(CAsyncLogger* pLogger);
extern bool CDarkHoleDungeonQuest_LoadDarkHoleQuest(CDarkHoleDungeonQuest* pQuest);

// External global objects
extern CMapOperation g_MapOper;
extern CMonsterEventRespawn g_MonsterEventRespawn;
extern CMonsterEventSet* g_MonsterEventSet;
extern CDarkHoleDungeonQuest g_DarkHoleQuest;

/**
 * @brief Load INI configuration files
 * @return InitializationResult
 */
InitializationResult CMainThread::LoadINI() {
    try {
        LogComponentInitialization(SystemComponent::INILoader, false);
        
        // Call legacy LoadINI function
        if (!CMainThread_LoadINI(this)) {
            LogComponentInitialization(SystemComponent::INILoader, false, "LoadINI failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::INILoader, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::INILoader, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Check system defines and configuration
 * @return InitializationResult
 */
InitializationResult CMainThread::CheckDefine() {
    try {
        LogComponentInitialization(SystemComponent::DefineChecker, false);
        
        // Call legacy CheckDefine function
        if (!CMainThread_CheckDefine(this)) {
            LogComponentInitialization(SystemComponent::DefineChecker, false, "CheckDefine failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::DefineChecker, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::DefineChecker, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Check database synchronization data size
 * @return InitializationResult
 */
InitializationResult CMainThread::CheckDatabaseSizeData() {
    try {
        LogComponentInitialization(SystemComponent::DatabaseSizeChecker, false);
        
        // Call legacy check_dbsyn_data_size function
        if (!CMainThread_check_dbsyn_data_size(this)) {
            LogComponentInitialization(SystemComponent::DatabaseSizeChecker, false, "check_dbsyn_data_size failed");
            return InitializationResult::DatabaseError;
        }
        
        LogComponentInitialization(SystemComponent::DatabaseSizeChecker, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::DatabaseSizeChecker, false, e.what());
        return InitializationResult::DatabaseError;
    }
}

/**
 * @brief Initialize data files
 * @return InitializationResult
 */
InitializationResult CMainThread::DataFileInit() {
    try {
        LogComponentInitialization(SystemComponent::DataFileInit, false);
        
        // Call legacy DataFileInit function
        if (!CMainThread_DataFileInit(this)) {
            LogComponentInitialization(SystemComponent::DataFileInit, false, "DataFileInit failed");
            return InitializationResult::Failure;
        }
        
        WriteLog("Game Data Load Complete!!");
        LogComponentInitialization(SystemComponent::DataFileInit, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::DataFileInit, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Initialize game objects
 * @return InitializationResult
 */
InitializationResult CMainThread::ObjectInit() {
    try {
        LogComponentInitialization(SystemComponent::ObjectInit, false);
        
        // Call legacy ObjectInit function
        if (!CMainThread_ObjectInit(this)) {
            LogComponentInitialization(SystemComponent::ObjectInit, false, "ObjectInit failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::ObjectInit, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::ObjectInit, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Initialize network systems
 * @return InitializationResult
 */
InitializationResult CMainThread::NetworkInit() {
    try {
        LogComponentInitialization(SystemComponent::NetworkInit, false);
        
        // Call legacy NetworkInit function
        if (!CMainThread_NetworkInit(this)) {
            LogComponentInitialization(SystemComponent::NetworkInit, false, "NetworkInit failed");
            return InitializationResult::NetworkError;
        }
        
        WriteLog("Network Init Complete!!");
        LogComponentInitialization(SystemComponent::NetworkInit, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::NetworkInit, false, e.what());
        return InitializationResult::NetworkError;
    }
}

/**
 * @brief Initialize map operation systems
 * @return InitializationResult
 */
InitializationResult CMainThread::InitializeMapOperation() {
    try {
        LogComponentInitialization(SystemComponent::MapOperation, false);
        
        WriteLog("Map Load Start!!");
        
        // Initialize map operation
        if (!CMapOperation_Init(&g_MapOper)) {
            LogComponentInitialization(SystemComponent::MapOperation, false, "CMapOperation::Init failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::MapOperation, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::MapOperation, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Initialize monster event systems
 * @return InitializationResult
 */
InitializationResult CMainThread::InitializeMonsterEventSystems() {
    try {
        // Initialize monster event respawn
        LogComponentInitialization(SystemComponent::MonsterEventRespawn, false);
        
        if (!CMonsterEventRespawn_SetEventRespawn(&g_MonsterEventRespawn)) {
            LogComponentInitialization(SystemComponent::MonsterEventRespawn, false, "SetEventRespawn failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::MonsterEventRespawn, true);
        
        // Initialize monster event set
        LogComponentInitialization(SystemComponent::MonsterEventSet, false);
        
        char errorCode[1024] = {0};
        if (!CMonsterEventSet_LoadEventSet(g_MonsterEventSet, errorCode)) {
            std::string error = std::format("LoadEventSet failed: {}", errorCode);
            LogComponentInitialization(SystemComponent::MonsterEventSet, false, error);
            return InitializationResult::Failure;
        }
        
        if (!CMonsterEventSet_LoadEventSetLooting(g_MonsterEventSet)) {
            LogComponentInitialization(SystemComponent::MonsterEventSet, false, "LoadEventSetLooting failed");
            return InitializationResult::Failure;
        }
        
        WriteLog("Map Load Complete!!");
        LogComponentInitialization(SystemComponent::MonsterEventSet, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::MonsterEventSet, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Initialize async logging system
 * @return InitializationResult
 */
InitializationResult CMainThread::InitializeAsyncLogging() {
    try {
        LogComponentInitialization(SystemComponent::AsyncLogger, false);
        
        auto asyncLogger = CAsyncLogger::Instance();
        int result = CAsyncLogger_Init(asyncLogger);
        
        if (result != 0) {
            std::string error = std::format("CAsyncLogger::Init failed with code: {}", result);
            LogComponentInitialization(SystemComponent::AsyncLogger, false, error);
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::AsyncLogger, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::AsyncLogger, false, e.what());
        return InitializationResult::Failure;
    }
}

/**
 * @brief Initialize system managers
 * @return InitializationResult
 */
InitializationResult CMainThread::InitializeSystemManagers() {
    try {
        // Initialize TimeLimitMgr
        LogComponentInitialization(SystemComponent::TimeLimitManager, false);
        
        m_pTimeLimitMgr = TimeLimitMgr::Instance();
        TimeLimitMgr::LoadTLINIFile(m_pTimeLimitMgr);
        TimeLimitMgr::InitializeTLMgr(m_pTimeLimitMgr);
        
        LogComponentInitialization(SystemComponent::TimeLimitManager, true);
        
        // Initialize DarkHoleDungeonQuest
        LogComponentInitialization(SystemComponent::DarkHoleDungeonQuest, false);
        
        if (!CDarkHoleDungeonQuest_LoadDarkHoleQuest(&g_DarkHoleQuest)) {
            LogComponentInitialization(SystemComponent::DarkHoleDungeonQuest, false, "LoadDarkHoleQuest failed");
            return InitializationResult::Failure;
        }
        
        LogComponentInitialization(SystemComponent::DarkHoleDungeonQuest, true);
        return InitializationResult::Success;
        
    } catch (const std::exception& e) {
        LogComponentInitialization(SystemComponent::TimeLimitManager, false, e.what());
        return InitializationResult::Failure;
    }
}
