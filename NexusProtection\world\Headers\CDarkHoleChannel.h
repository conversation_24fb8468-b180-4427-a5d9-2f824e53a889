#pragma once

/**
 * @file CDarkHoleChannel.h
 * @brief Dark Hole Channel management class for NexusProtection
 * @details Manages dark hole dungeon channels, monster spawning, and quest progression
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <memory>
#include <vector>
#include <cstdint>
#include <string>
#include <random>

// Forward declarations
class CMapData;
class CMonster;
class CPlayer;
class CDarkHole;
struct _dh_quest_setup;
struct _dh_mission_setup;
struct _dh_mission_mgr;
struct __add_monster;
struct __change_monster;
struct __respawn_monster;
struct _dummy_position;
struct _monster_fld;
struct _monster_create_setdata;
struct _react_area;
struct _react_obj;
struct _base_fld;
struct _LAYER_SET;
struct __respawn_monster;
struct _respawn_monster_act;

// Nested structure forward declarations
namespace _dh_mission_mgr_ns {
    struct _if_change;
}

namespace NexusProtection {
namespace World {

/**
 * @class CDarkHoleChannel
 * @brief Manages dark hole dungeon channels and monster operations
 * 
 * This class handles the management of dark hole dungeon channels, including:
 * - Monster spawning and management
 * - Quest progression and mission handling
 * - Player management within channels
 * - Channel lifecycle management
 * 
 * Key Features:
 * - Dynamic monster addition based on quest setup
 * - Monster state changes and respawning
 * - Channel-specific quest and mission management
 * - Integration with map data and positioning systems
 * 
 * @note Refactored from decompiled CDarkHoleChannel structure
 */
class CDarkHoleChannel {
public:
    // Constants
    static constexpr std::size_t MAX_OPENER_NAME_LENGTH = 17;
    static constexpr std::size_t MAX_CHANNEL_PLAYERS = 8;
    static constexpr std::size_t MAX_MISSIONS_PER_QUEST = 32;
    
    // Static members
    static std::uint32_t s_channelSerialCounter;

    /**
     * @brief Default constructor
     * Initializes the CDarkHoleChannel with default values
     */
    CDarkHoleChannel();

    /**
     * @brief Destructor
     * Cleans up all allocated resources and channels
     */
    ~CDarkHoleChannel();

    /**
     * @brief Copy constructor (deleted)
     * CDarkHoleChannel objects should not be copied
     */
    CDarkHoleChannel(const CDarkHoleChannel&) = delete;

    /**
     * @brief Copy assignment operator (deleted)
     * CDarkHoleChannel objects should not be copied
     */
    CDarkHoleChannel& operator=(const CDarkHoleChannel&) = delete;

    /**
     * @brief Move constructor
     * @param other The CDarkHoleChannel to move from
     */
    CDarkHoleChannel(CDarkHoleChannel&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The CDarkHoleChannel to move from
     * @return Reference to this object
     */
    CDarkHoleChannel& operator=(CDarkHoleChannel&& other) noexcept;

    /**
     * @brief Initialize the channel
     * @details Sets up default values for all member variables
     */
    void Init();

    /**
     * @brief Add monsters to the channel based on current mission setup
     * @details Spawns monsters according to the mission configuration,
     *          handling different area types and monster configurations
     */
    void AddMonster();

    /**
     * @brief Change monsters in the channel based on mission parameters
     * @details Modifies existing monsters or spawns new ones based on
     *          mission change requirements and probability calculations
     */
    void ChangeMonster();

    /**
     * @brief Create initial monsters for the channel
     * @details Creates the base set of monsters when the channel is first opened
     */
    void CreateMonster();

    /**
     * @brief Check and handle monster respawning
     * @details Monitors monster respawn timers and creates new monsters
     *          when respawn conditions are met
     */
    void CheckRespawnMonster();

    /**
     * @brief Open the dungeon channel
     * @param questSetup Pointer to the quest setup configuration
     * @param layerIndex The layer index for the channel
     * @param opener The player opening the channel
     * @param holeObj The dark hole object associated with this channel
     */
    void OpenDungeon(_dh_quest_setup* questSetup, int layerIndex, 
                     CPlayer* opener, CDarkHole* holeObj);

    /**
     * @brief Send channel close message to all players
     * @details Notifies all players in the channel that it's closing
     */
    void SendChannelCloseMessage();

    /**
     * @brief Get the channel serial number
     * @return The unique serial number for this channel
     */
    [[nodiscard]] std::uint32_t GetChannelSerial() const noexcept { return m_channelSerial; }

    /**
     * @brief Get the opener's serial number
     * @return The serial number of the player who opened this channel
     */
    [[nodiscard]] std::uint32_t GetOpenerSerial() const noexcept { return m_openerSerial; }

    /**
     * @brief Get the layer index
     * @return The layer index for this channel
     */
    [[nodiscard]] std::uint16_t GetLayerIndex() const noexcept { return m_layerIndex; }

    /**
     * @brief Check if the channel is active
     * @return true if the channel is active, false otherwise
     */
    [[nodiscard]] bool IsActive() const noexcept;

private:
    /// Quest setup configuration for this channel
    _dh_quest_setup* m_questSetup;
    
    /// Mission manager for handling quest progression
    _dh_mission_mgr m_missionManager;
    
    /// Layer index for this channel
    std::uint16_t m_layerIndex;
    
    /// Unique serial number for this channel
    std::uint32_t m_channelSerial;
    
    /// Serial number of the player who opened this channel
    std::uint32_t m_openerSerial;
    
    /// Serial number of the associated dark hole object
    std::uint32_t m_holeSerial;
    
    /// Name of the player who opened this channel (wide string)
    std::wstring m_openerNameW;
    
    /// Name of the player who opened this channel (ANSI string)
    std::string m_openerNameA;
    
    /// Opener's degree level
    int m_openerDegree;
    
    /// Opener's sub-degree level
    int m_openerSubDegree;
    
    /// Pointer to the associated dark hole object
    CDarkHole* m_holeObject;
    
    /// Party manager pointer (if party-based)
    void* m_partyManager;
    
    /// Random number generator for monster operations
    mutable std::mt19937 m_randomGenerator;

    /// Layer set for monster management
    _LAYER_SET* m_pLayerSet;

    /**
     * @brief Create a monster at a specific position
     * @param map Pointer to the map data
     * @param position Position to create the monster
     * @param monsterCode Monster type code
     * @param parent Parent monster (if any)
     * @param robExp Whether to rob experience
     * @param rewardExp Whether to reward experience
     * @param dungeon Whether this is a dungeon monster
     * @param withoutFail Whether creation should not fail
     * @param applyRopExpField Whether to apply ROP experience field
     * @return Pointer to the created monster, or nullptr if failed
     */
    CMonster* CreateMonsterAtPosition(CMapData* map, const float* position, 
                                      const char* monsterCode, CMonster* parent = nullptr,
                                      bool robExp = false, bool rewardExp = true,
                                      bool dungeon = true, bool withoutFail = true,
                                      bool applyRopExpField = false);

    /**
     * @brief Get a random position within a dummy area
     * @param dummyPos Pointer to the dummy position data
     * @param outPosition Output position array
     * @return true if a valid position was found, false otherwise
     */
    bool GetRandomPositionInDummy(_dummy_position* dummyPos, float* outPosition) const;

    /**
     * @brief Process area definition for monster spawning
     * @param addMonster Pointer to the add monster configuration
     * @return Pointer to the selected dummy position, or nullptr if none found
     */
    _dummy_position* ProcessAreaDefinition(__add_monster* addMonster) const;

    /**
     * @brief Cleanup internal resources
     * @details Safely releases all allocated memory and resets state
     */
    void Cleanup();

    /**
     * @brief Collect eligible monsters for change operations
     * @param missionSetup Pointer to the mission setup configuration
     * @param eligibleMonsters Vector to store eligible monsters
     */
    void CollectEligibleMonstersForChange(_dh_mission_setup* missionSetup,
                                          std::vector<CMonster*>& eligibleMonsters);

    /**
     * @brief Process monster change operations
     * @param changeMonster Pointer to the change monster configuration
     * @param missionSetup Pointer to the mission setup configuration
     * @param eligibleMonsters Vector of eligible monsters to transform
     */
    void ProcessMonsterChange(__change_monster* changeMonster,
                              _dh_mission_setup* missionSetup,
                              const std::vector<CMonster*>& eligibleMonsters);

    /**
     * @brief Get mission content for change operations
     * @param missionSetup Pointer to the mission setup configuration
     * @return Pointer to the mission change interface, or nullptr if not found
     */
    _dh_mission_mgr_ns::_if_change* GetMissionContent(_dh_mission_setup* missionSetup);

    /**
     * @brief Set the real boss flag for the quest
     * @param isRealBoss Whether this is a real boss encounter
     */
    void SetRealBossFlag(bool isRealBoss);

    /**
     * @brief Transform eligible monsters based on change configuration
     * @param changeMonster Pointer to the change monster configuration
     * @param eligibleMonsters Vector of monsters to transform
     */
    void TransformEligibleMonsters(__change_monster* changeMonster,
                                   const std::vector<CMonster*>& eligibleMonsters);

    /**
     * @brief Initialize monster creation data structure
     * @param createData Reference to the creation data to initialize
     */
    void InitializeMonsterCreateData(_monster_create_setdata& createData);

    /**
     * @brief Destroy and recreate a monster with new configuration
     * @param monster Pointer to the monster to transform
     * @param createData Creation data for the new monster
     */
    void DestroyAndRecreateMonster(CMonster* monster,
                                   const _monster_create_setdata& createData);

    /**
     * @brief Process monster blocks for initial monster creation
     * @param pMap Pointer to the map data containing monster blocks
     */
    void ProcessMonsterBlocks(CMapData* pMap);

    /**
     * @brief Process a single monster block for monster creation
     * @param pMap Pointer to the map data
     * @param blockIndex Index of the monster block to process
     * @param pBlock Pointer to the monster block data
     */
    void ProcessSingleMonsterBlock(CMapData* pMap, int blockIndex, _mon_block* pBlock);

    /**
     * @brief Create monsters for a specific active record
     * @param pMap Pointer to the map data
     * @param pActiveRec Pointer to the active record
     * @param pBlock Pointer to the monster block
     */
    void CreateMonstersForActiveRecord(CMapData* pMap, _mon_active* pActiveRec, _mon_block* pBlock);

    /**
     * @brief Get monster block from map by index
     * @param pMap Pointer to the map data
     * @param blockIndex Index of the monster block
     * @return Pointer to the monster block, or nullptr if not found
     */
    _mon_block* GetMonsterBlockFromMap(CMapData* pMap, int blockIndex);

    /**
     * @brief Get record count for a specific block
     * @param blockIndex Index of the monster block
     * @return Number of records in the block
     */
    int GetRecordCountForBlock(int blockIndex);

    /**
     * @brief Get active record by block and record index
     * @param blockIndex Index of the monster block
     * @param recordIndex Index of the record within the block
     * @return Pointer to the active record, or nullptr if not found
     */
    _mon_active* GetActiveRecord(int blockIndex, int recordIndex);

    /**
     * @brief Create a single monster in a block
     * @param pMap Pointer to the map data
     * @param pActiveRec Pointer to the active record
     * @param pBlock Pointer to the monster block
     */
    void CreateSingleMonsterInBlock(CMapData* pMap, _mon_active* pActiveRec, _mon_block* pBlock);

    /**
     * @brief Process a single respawn action
     * @param currentTime Current time in milliseconds
     * @param actionIndex Index of the respawn action to process
     */
    void ProcessRespawnAction(std::uint32_t currentTime, int actionIndex);

    /**
     * @brief Check and respawn monsters for a specific respawn action
     * @param pRespawnAct Pointer to the respawn action
     * @param pRespawnData Pointer to the respawn data
     */
    void CheckAndRespawnMonsters(_respawn_monster_act* pRespawnAct, __respawn_monster* pRespawnData);

    /**
     * @brief Create a respawn monster at a specific position
     * @param pRespawnAct Pointer to the respawn action
     * @param pRespawnData Pointer to the respawn data
     * @param monsterIndex Index of the monster slot
     * @param position Position to create the monster
     */
    void CreateRespawnMonsterAtPosition(_respawn_monster_act* pRespawnAct,
                                        __respawn_monster* pRespawnData,
                                        int monsterIndex,
                                        const float* position);

    /**
     * @brief Get a random respawn position based on area definition
     * @param pRespawnData Pointer to the respawn data
     * @param outPosition Output position array
     * @return true if a valid position was found, false otherwise
     */
    bool GetRespawnPosition(__respawn_monster* pRespawnData, float* outPosition);
};

} // namespace World
} // namespace NexusProtection
