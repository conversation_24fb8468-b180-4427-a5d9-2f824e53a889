/**
 * @file CMonsterEventRespawn.h
 * @brief Monster Event Respawn Manager for handling monster respawn events
 * @details Manages timed respawn events for monsters in the game world
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#pragma once

#ifndef CMONSTEREVENTRESPAWN_H
#define CMONSTEREVENTRESPAWN_H

#include <cstdint>
#include <memory>
#include <vector>
#include <array>
#include <string>
#include <chrono>
#include <mutex>

namespace NexusProtection {
namespace World {

/**
 * @brief Forward declarations
 */
class CMonster;
class CMapData;

/**
 * @brief Monster field data structure
 */
struct MonsterFieldData {
    std::string m_strCode;          ///< Monster code identifier
    uint32_t m_dwMonsterType;       ///< Monster type identifier
    float m_fPosX;                  ///< X position
    float m_fPosY;                  ///< Y position
    float m_fPosZ;                  ///< Z position
    uint32_t m_dwLevel;             ///< Monster level
    uint32_t m_dwHP;                ///< Monster HP
    bool m_bActive;                 ///< Whether monster is active
    
    MonsterFieldData() noexcept
        : m_dwMonsterType(0), m_fPosX(0.0f), m_fPosY(0.0f), m_fPosZ(0.0f)
        , m_dwLevel(1), m_dwHP(100), m_bActive(false) {}
};

/**
 * @brief Respawn event options
 */
struct RespawnEventOptions {
    bool bExpReward;                ///< Whether to give experience reward
    bool bExpPenalty;               ///< Whether to apply experience penalty
    bool bItemLoot;                 ///< Whether to allow item looting
    bool bApplyDropRate;            ///< Whether to apply drop rate
    uint32_t dwRespawnDelay;        ///< Respawn delay in milliseconds
    uint32_t dwMaxRespawnCount;     ///< Maximum respawn count
    float fRespawnRadius;           ///< Respawn radius
    
    RespawnEventOptions() noexcept
        : bExpReward(true), bExpPenalty(false), bItemLoot(true)
        , bApplyDropRate(true), dwRespawnDelay(30000), dwMaxRespawnCount(1)
        , fRespawnRadius(100.0f) {}
};

/**
 * @brief Monster information for respawn state
 */
struct MonsterRespawnInfo {
    CMonster* pMon;                 ///< Pointer to monster instance
    MonsterFieldData* pMonFld;      ///< Pointer to monster field data
    uint32_t dwSerial;              ///< Monster serial number
    bool bActive;                   ///< Whether this slot is active
    std::chrono::system_clock::time_point lastSpawnTime; ///< Last spawn time
    
    MonsterRespawnInfo() noexcept
        : pMon(nullptr), pMonFld(nullptr), dwSerial(0), bActive(false) {}
};

/**
 * @brief Respawn event state
 */
struct RespawnEventState {
    std::vector<MonsterRespawnInfo> MonInfo;    ///< Monster information array
    uint32_t dwLastUpdateTime;                  ///< Last update time (timeGetTime)
    int32_t nRespawnNum;                        ///< Number of monsters to respawn
    bool bStateActive;                          ///< Whether state is active
    uint32_t dwTotalRespawned;                  ///< Total monsters respawned
    
    RespawnEventState() noexcept
        : dwLastUpdateTime(0), nRespawnNum(0), bStateActive(false), dwTotalRespawned(0) {
        MonInfo.reserve(32); // Reserve space for typical monster count
    }
};

/**
 * @brief Event respawn data structure
 */
struct EventRespawnData {
    RespawnEventState State;        ///< Current state of the event
    RespawnEventOptions Option;     ///< Event options
    CMapData* pMap;                 ///< Pointer to map data
    std::array<float, 3> fPos;      ///< Position array [x, y, z]
    uint32_t dwTermMSec;            ///< Term in milliseconds
    bool bLoad;                     ///< Whether event is loaded
    bool bActive;                   ///< Whether event is active
    int32_t nUseRewardItemNum;      ///< Number of reward items used
    std::string strEventName;       ///< Event name for identification
    uint32_t dwEventID;             ///< Unique event identifier
    
    EventRespawnData() noexcept
        : pMap(nullptr), dwTermMSec(30000), bLoad(false), bActive(false)
        , nUseRewardItemNum(0), dwEventID(0) {
        fPos.fill(0.0f);
    }
};

/**
 * @brief Statistics for respawn event monitoring
 */
struct RespawnEventStatistics {
    std::size_t totalEvents;            ///< Total number of events
    std::size_t activeEvents;           ///< Number of active events
    std::size_t totalRespawns;          ///< Total respawns performed
    std::size_t failedRespawns;         ///< Number of failed respawns
    uint64_t totalProcessingTime;       ///< Total processing time in milliseconds
    uint64_t averageProcessingTime;     ///< Average processing time per update
    std::chrono::system_clock::time_point lastUpdateTime; ///< Last update timestamp
};

/**
 * @brief Monster Event Respawn Manager class
 * 
 * This class manages monster respawn events in the game world. It provides:
 * - Timed respawn event processing
 * - Monster creation and management for respawn events
 * - Event state tracking and monitoring
 * - Performance statistics and monitoring
 * 
 * The class handles automatic respawning of monsters based on configured
 * events with customizable options for experience, loot, and timing.
 */
class CMonsterEventRespawn {
public:
    /**
     * @brief Constructor
     */
    CMonsterEventRespawn();
    
    /**
     * @brief Virtual destructor
     */
    virtual ~CMonsterEventRespawn();
    
    // Delete copy constructor and copy assignment operator
    CMonsterEventRespawn(const CMonsterEventRespawn&) = delete;
    CMonsterEventRespawn& operator=(const CMonsterEventRespawn&) = delete;
    
    // Core respawn event operations
    
    /**
     * @brief Initialize the respawn event manager
     * @return true if initialization was successful, false otherwise
     */
    [[nodiscard]] bool Initialize();
    
    /**
     * @brief Shutdown the respawn event manager
     */
    void Shutdown();
    
    /**
     * @brief Check and process respawn events
     * @details This method should be called periodically to process respawn events
     */
    void CheckRespawnEvent();
    
    /**
     * @brief Update respawn event processing
     * @details Alternative update method for more granular control
     */
    void Update();
    
    // Event management
    
    /**
     * @brief Add a new respawn event
     * @param eventData The event data to add
     * @return true if the event was added successfully, false otherwise
     */
    [[nodiscard]] bool AddRespawnEvent(const EventRespawnData& eventData);
    
    /**
     * @brief Remove a respawn event by ID
     * @param dwEventID The ID of the event to remove
     * @return true if the event was removed, false if not found
     */
    [[nodiscard]] bool RemoveRespawnEvent(uint32_t dwEventID);
    
    /**
     * @brief Update an existing respawn event
     * @param dwEventID The ID of the event to update
     * @param eventData The new event data
     * @return true if the event was updated, false if not found
     */
    [[nodiscard]] bool UpdateRespawnEvent(uint32_t dwEventID, const EventRespawnData& eventData);
    
    /**
     * @brief Find a respawn event by ID
     * @param dwEventID The ID of the event to find
     * @return Pointer to the event data, or nullptr if not found
     */
    [[nodiscard]] EventRespawnData* FindRespawnEvent(uint32_t dwEventID);
    
    /**
     * @brief Find a respawn event by ID (const version)
     * @param dwEventID The ID of the event to find
     * @return Const pointer to the event data, or nullptr if not found
     */
    [[nodiscard]] const EventRespawnData* FindRespawnEvent(uint32_t dwEventID) const;
    
    /**
     * @brief Get all active respawn events
     * @return Vector of pointers to active event data
     */
    [[nodiscard]] std::vector<const EventRespawnData*> GetActiveEvents() const;
    
    /**
     * @brief Get events by map
     * @param pMap Pointer to the map data
     * @return Vector of pointers to events on the specified map
     */
    [[nodiscard]] std::vector<const EventRespawnData*> GetEventsByMap(CMapData* pMap) const;
    
    // Status and monitoring
    
    /**
     * @brief Check if the respawn event manager is initialized
     * @return true if initialized, false otherwise
     */
    [[nodiscard]] bool IsInitialized() const noexcept;
    
    /**
     * @brief Get the number of loaded respawn events
     * @return Number of loaded events
     */
    [[nodiscard]] std::size_t GetLoadedEventCount() const noexcept;
    
    /**
     * @brief Get the number of active respawn events
     * @return Number of active events
     */
    [[nodiscard]] std::size_t GetActiveEventCount() const noexcept;
    
    /**
     * @brief Check if the respawn event manager is empty
     * @return true if no events exist, false otherwise
     */
    [[nodiscard]] bool IsEmpty() const noexcept;
    
    /**
     * @brief Get comprehensive statistics
     * @return Statistics structure with current data
     */
    [[nodiscard]] RespawnEventStatistics GetStatistics() const;
    
    /**
     * @brief Clear all respawn events
     */
    void ClearAllEvents();
    
    /**
     * @brief Reset all event states
     */
    void ResetAllEventStates();
    
    // Event control
    
    /**
     * @brief Start a respawn event
     * @param dwEventID The ID of the event to start
     * @return true if the event was started, false otherwise
     */
    [[nodiscard]] bool StartRespawnEvent(uint32_t dwEventID);
    
    /**
     * @brief Stop a respawn event
     * @param dwEventID The ID of the event to stop
     * @return true if the event was stopped, false otherwise
     */
    [[nodiscard]] bool StopRespawnEvent(uint32_t dwEventID);
    
    /**
     * @brief Pause a respawn event
     * @param dwEventID The ID of the event to pause
     * @return true if the event was paused, false otherwise
     */
    [[nodiscard]] bool PauseRespawnEvent(uint32_t dwEventID);
    
    /**
     * @brief Resume a respawn event
     * @param dwEventID The ID of the event to resume
     * @return true if the event was resumed, false otherwise
     */
    [[nodiscard]] bool ResumeRespawnEvent(uint32_t dwEventID);
    
    // Utility functions
    
    /**
     * @brief Validate an event respawn data structure
     * @param eventData The event data to validate
     * @return true if the event data is valid, false otherwise
     */
    [[nodiscard]] static bool ValidateEventData(const EventRespawnData& eventData);
    
    /**
     * @brief Get the maximum number of respawn events
     * @return Maximum event count
     */
    [[nodiscard]] static constexpr std::size_t GetMaxEventCount() noexcept {
        return 20000; // Based on the decompiled code showing 0x4D78 / sizeof(event_respawn)
    }
    
    /**
     * @brief Get memory usage of the respawn event manager
     * @return Memory usage in bytes
     */
    [[nodiscard]] std::size_t GetMemoryUsage() const;

protected:
    // Protected member variables (legacy compatibility)
    std::vector<EventRespawnData> m_EventRespawn;   ///< Array of respawn events
    int32_t m_nLoadEventRespawn;                    ///< Number of loaded events
    
private:
    // Private member variables
    bool m_isInitialized;                           ///< Initialization status
    mutable RespawnEventStatistics m_statistics;    ///< Statistics tracking
    mutable std::mutex m_eventMutex;                ///< Mutex for thread-safe event access
    uint32_t m_dwNextEventID;                       ///< Next available event ID
    
    // Private helper methods
    
    /**
     * @brief Initialize internal data structures
     */
    void InitializeInternal();
    
    /**
     * @brief Update statistics
     */
    void UpdateStatistics() const;
    
    /**
     * @brief Process a single respawn event
     * @param eventData The event to process
     * @param currentTime Current time in milliseconds
     * @return true if processing was successful, false otherwise
     */
    bool ProcessSingleEvent(EventRespawnData& eventData, uint32_t currentTime);
    
    /**
     * @brief Create a respawn monster
     * @param eventData The event data
     * @param monInfo The monster info to respawn
     * @return Pointer to created monster, or nullptr if failed
     */
    CMonster* CreateRespawnMonster(const EventRespawnData& eventData, MonsterRespawnInfo& monInfo);
    
    /**
     * @brief Validate internal state
     * @return true if internal state is valid, false otherwise
     */
    [[nodiscard]] bool ValidateInternalState() const;
    
    /**
     * @brief Get current time in milliseconds (timeGetTime equivalent)
     * @return Current time in milliseconds
     */
    [[nodiscard]] static uint32_t GetCurrentTimeMS();
};

} // namespace World
} // namespace NexusProtection

#endif // CMONSTEREVENTRESPAWN_H
