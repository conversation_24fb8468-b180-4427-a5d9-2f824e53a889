/**
 * @file BillingManagerLogin.cpp
 * @brief Implementation of billing manager login functionality
 * 
 * Refactored from decompiled source: LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c
 * Original function: CBillingManager::Login
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "BillingManagerLogin.h"
#include <iostream>
#include <format>
#include <cstring>
#include <cassert>

/**
 * @brief BillingLoginInfo constructor
 */
BillingLoginInfo::BillingLoginInfo() {
    Reset();
}

/**
 * @brief Reset login info to default values
 */
void BillingLoginInfo::Reset() {
    userID = 0;
    accountSerial = 0;
    accountName.clear();
    billingType.clear();
    isActive = false;
}

/**
 * @brief Check if login info is valid
 * @return true if valid, false otherwise
 */
bool BillingLoginInfo::IsValid() const {
    return userID != 0 && accountSerial != 0 && !accountName.empty();
}

/**
 * @brief Constructor
 */
BillingManagerLogin::BillingManagerLogin() 
    : m_securityCookie(0) {
}

/**
 * @brief Destructor
 */
BillingManagerLogin::~BillingManagerLogin() = default;

/**
 * @brief Execute billing login
 * 
 * Performs the billing login process for the specified user database.
 * This is the modern interface that provides enhanced error handling
 * and logging capabilities.
 * 
 * @param billingManager Pointer to CBillingManager instance
 * @param userDB Pointer to CUserDB instance
 * @return BillingLoginResult indicating success or failure
 */
BillingLoginResult BillingManagerLogin::ExecuteLogin(CBillingManager* billingManager, CUserDB* userDB) {
    try {
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting billing manager login process..." << std::endl;
        
        // Validate input parameters
        if (!ValidateParameters(billingManager, userDB)) {
            return BillingLoginResult::InvalidManager;
        }
        
        // Extract user information from UserDB
        if (!ExtractUserInfo(userDB)) {
            return BillingLoginResult::InvalidUserDB;
        }
        
        // Validate billing manager state
        if (!ValidateBillingManager(billingManager)) {
            return BillingLoginResult::InvalidBilling;
        }
        
        // Execute billing system login
        if (!ExecuteBillingLogin(billingManager)) {
            return BillingLoginResult::Failure;
        }
        
        // Verify security cookie (equivalent to original stack protection check)
        if (!ValidateSecurityCookie()) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return BillingLoginResult::SecurityError;
        }
        
        // Call registered callback if available
        if (m_loginCallback) {
            try {
                m_loginCallback(BillingLoginResult::Success, m_loginInfo);
            } catch (const std::exception& e) {
                std::cerr << "[WARNING] Exception in login callback: " << e.what() << std::endl;
            }
        }
        
        std::cout << std::format("[INFO] Billing login completed successfully for account: {}", 
                                m_loginInfo.accountName) << std::endl;
        return BillingLoginResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during billing login: {}", e.what()));
        return BillingLoginResult::SystemError;
    }
}

/**
 * @brief Legacy Login function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * 
 * @param billingManager Pointer to CBillingManager instance
 * @param userDB Pointer to CUserDB instance
 */
void BillingManagerLogin::Login_Legacy(CBillingManager* billingManager, CUserDB* userDB) {
    try {
        if (!billingManager || !userDB) {
            std::cerr << "[ERROR] Invalid parameters in Login_Legacy" << std::endl;
            return;
        }
        
        BillingManagerLogin loginHandler;
        BillingLoginResult result = loginHandler.ExecuteLogin(billingManager, userDB);
        
        if (result != BillingLoginResult::Success) {
            std::cerr << "[ERROR] Billing login failed: " 
                      << loginHandler.GetLastError() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in Login_Legacy: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in Login_Legacy" << std::endl;
    }
}

/**
 * @brief Validate input parameters
 * @param billingManager Pointer to CBillingManager instance
 * @param userDB Pointer to CUserDB instance
 * @return true if valid, false otherwise
 */
bool BillingManagerLogin::ValidateParameters(CBillingManager* billingManager, CUserDB* userDB) {
    if (!billingManager) {
        SetLastError("CBillingManager pointer is null");
        return false;
    }
    
    if (!userDB) {
        SetLastError("CUserDB pointer is null");
        return false;
    }
    
    return true;
}

/**
 * @brief Extract user information from UserDB
 * @param userDB Pointer to CUserDB instance
 * @return true if successful, false otherwise
 */
bool BillingManagerLogin::ExtractUserInfo(CUserDB* userDB) {
    try {
        m_loginInfo.Reset();
        
        // Extract account information
        m_loginInfo.accountSerial = userDB->m_dwAccountSerial;
        m_loginInfo.accountName = std::string(userDB->m_szAccountName);
        m_loginInfo.isActive = userDB->m_bActive;
        m_loginInfo.userID = userDB->m_dwAccountSerial; // Use account serial as user ID
        m_loginInfo.billingType = "Standard"; // Default billing type
        
        if (!m_loginInfo.IsValid()) {
            SetLastError("Invalid user information extracted from UserDB");
            return false;
        }
        
        std::cout << std::format("[INFO] Extracted user info - Account: {}, Serial: {}", 
                                m_loginInfo.accountName, m_loginInfo.accountSerial) << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to extract user info: {}", e.what()));
        return false;
    }
}

/**
 * @brief Validate billing manager state
 * @param billingManager Pointer to CBillingManager instance
 * @return true if valid, false otherwise
 */
bool BillingManagerLogin::ValidateBillingManager(CBillingManager* billingManager) {
    if (!billingManager->m_pBill) {
        SetLastError("Billing system pointer is null");
        return false;
    }
    
    if (!billingManager->m_pBill->vfptr) {
        SetLastError("Billing virtual function table is null");
        return false;
    }
    
    if (!billingManager->m_pBill->vfptr->Login) {
        SetLastError("Billing login function is null");
        return false;
    }
    
    return true;
}

/**
 * @brief Execute billing system login
 * @param billingManager Pointer to CBillingManager instance
 * @return true if successful, false otherwise
 */
bool BillingManagerLogin::ExecuteBillingLogin(CBillingManager* billingManager) {
    try {
        // Call the billing system login function (equivalent to original virtual call)
        // ((void (__fastcall *)(CBilling *))v5->m_pBill->vfptr->Login)(v5->m_pBill);
        billingManager->m_pBill->vfptr->Login(billingManager->m_pBill);
        
        std::cout << "[INFO] Billing system login executed successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to execute billing login: {}", e.what()));
        return false;
    }
}

/**
 * @brief Get the last error message
 * @return string containing the last error message
 */
std::string BillingManagerLogin::GetLastError() const {
    return m_lastError;
}

/**
 * @brief Get billing login information
 * @return const reference to billing login info
 */
const BillingLoginInfo& BillingManagerLogin::GetLoginInfo() const {
    return m_loginInfo;
}

/**
 * @brief Set login callback function
 * @param callback Function to call after login completion
 */
void BillingManagerLogin::SetLoginCallback(std::function<void(BillingLoginResult, const BillingLoginInfo&)> callback) {
    m_loginCallback = std::move(callback);
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void BillingManagerLogin::SetLastError(const std::string& error) {
    m_lastError = error;
}

/**
 * @brief Validate security cookie
 * @return true if valid, false if corrupted
 */
bool BillingManagerLogin::ValidateSecurityCookie() const {
    return (reinterpret_cast<uint64_t>(this) ^ _security_cookie) == m_securityCookie;
}

/**
 * @brief Convert BillingLoginResult enum to string for logging
 * @param result The login result
 * @return String representation of the result
 */
std::string BillingLoginResultToString(BillingLoginResult result) {
    switch (result) {
        case BillingLoginResult::Success: return "Success";
        case BillingLoginResult::Failure: return "Failure";
        case BillingLoginResult::InvalidManager: return "InvalidManager";
        case BillingLoginResult::InvalidUserDB: return "InvalidUserDB";
        case BillingLoginResult::InvalidBilling: return "InvalidBilling";
        case BillingLoginResult::SystemError: return "SystemError";
        case BillingLoginResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief CBillingManager::Login implementation for compatibility
 * 
 * This provides the original method implementation that can be called
 * directly on CBillingManager instances.
 */
void CBillingManager::Login(CUserDB* pUserDB) {
    BillingManagerLogin::Login_Legacy(this, pUserDB);
}
