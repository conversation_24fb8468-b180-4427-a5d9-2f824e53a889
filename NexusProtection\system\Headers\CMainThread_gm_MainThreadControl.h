/**
 * @file CMainThread_gm_MainThreadControl.h
 * @brief Main thread control system for CMainThread
 * 
 * Refactored from gm_MainThreadControl function
 * This handles the main thread control and management system that manages
 * thread lifecycle, control commands, and thread synchronization for the main server thread.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <thread>

// Forward declarations
class CMainThread;

/**
 * @enum ThreadControlCommand
 * @brief Enumeration of thread control commands
 */
enum class ThreadControlCommand : uint32_t {
    // Basic Control Commands
    Initialize = 0,
    Start,
    Stop,
    Pause,
    Resume,
    Shutdown,
    
    // Status Commands
    GetStatus,
    GetStatistics,
    CheckHealth,
    
    // Configuration Commands
    SetPriority,
    SetAffinity,
    SetTimeout,
    
    // Synchronization Commands
    WaitForCompletion,
    SignalReady,
    Synchronize,
    
    // Network Commands
    StartNetworking,
    StopNetworking,
    ResetConnections,
    
    // Data Commands
    ReloadData,
    ValidateData,
    BackupData,
    
    // System Commands
    EnableWorldService,
    DisableWorldService,
    OpenWorld,
    CloseWorld,
    
    // Monitoring Commands
    StartMonitoring,
    StopMonitoring,
    ReportStatus,
    
    // Emergency Commands
    EmergencyStop,
    ForceRestart,
    SafeShutdown,
    
    MAX_COMMANDS
};

/**
 * @enum ThreadControlResult
 * @brief Result codes for thread control operations
 */
enum class ThreadControlResult : int32_t {
    Success = 1,
    Failure = 0,
    InvalidCommand = -1,
    InvalidState = -2,
    Timeout = -3,
    AccessDenied = -4,
    SystemError = -5,
    NetworkError = -6,
    DataError = -7,
    SecurityError = -8
};

/**
 * @enum ThreadState
 * @brief Current state of the main thread
 */
enum class ThreadState : uint32_t {
    Uninitialized = 0,
    Initializing,
    Ready,
    Running,
    Paused,
    Stopping,
    Stopped,
    Error,
    ShuttingDown,
    Shutdown
};

/**
 * @struct ThreadControlStats
 * @brief Statistics for thread control operations
 */
struct ThreadControlStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastCommandTime;
    std::array<uint64_t, static_cast<size_t>(ThreadControlCommand::MAX_COMMANDS)> commandCounts;
    std::array<std::chrono::milliseconds, static_cast<size_t>(ThreadControlCommand::MAX_COMMANDS)> commandTimes;
    uint64_t totalCommands;
    uint64_t successfulCommands;
    uint64_t failedCommands;
    ThreadState currentState;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        lastCommandTime = startTime;
        commandCounts.fill(0);
        commandTimes.fill(std::chrono::milliseconds::zero());
        totalCommands = 0;
        successfulCommands = 0;
        failedCommands = 0;
        currentState = ThreadState::Uninitialized;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - startTime);
    }
    
    double GetSuccessRate() const {
        return totalCommands > 0 ? (static_cast<double>(successfulCommands) / totalCommands) * 100.0 : 0.0;
    }
};

/**
 * @struct ThreadControlConfig
 * @brief Configuration for thread control system
 */
struct ThreadControlConfig {
    std::chrono::milliseconds commandTimeout{5000};
    std::chrono::milliseconds statusUpdateInterval{1000};
    std::chrono::milliseconds healthCheckInterval{10000};
    uint32_t maxRetryAttempts{3};
    uint32_t threadPriority{0};
    uint64_t threadAffinity{0};
    bool enableMonitoring{true};
    bool enableLogging{true};
    bool enableSecurity{true};
    std::string logLevel{"INFO"};
};

/**
 * @class CMainThreadControl
 * @brief Main thread control and management system
 * 
 * This class handles the control and management of the main server thread.
 * It provides modern C++20 interfaces while maintaining compatibility with 
 * the original thread control logic.
 * 
 * Original function: CMainThread::gm_MainThreadControl
 * Refactored to modern C++20 with comprehensive thread management and control.
 */
class CMainThreadControl {
public:
    /**
     * @brief Constructor
     */
    CMainThreadControl();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadControl();
    
    // Delete copy constructor and assignment operator
    CMainThreadControl(const CMainThreadControl&) = delete;
    CMainThreadControl& operator=(const CMainThreadControl&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadControl(CMainThreadControl&&) = default;
    CMainThreadControl& operator=(CMainThreadControl&&) = default;
    
    /**
     * @brief Main thread control function
     * 
     * Initializes and starts the main thread control system.
     * This is the modern refactored version of the original gm_MainThreadControl function.
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return ThreadControlResult indicating success or failure
     */
    ThreadControlResult InitializeMainThreadControl(CMainThread* mainThread);
    
    /**
     * @brief Legacy gm_MainThreadControl function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: void __fastcall CMainThread::gm_MainThreadControl(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     */
    static void gm_MainThreadControl_Legacy(CMainThread* mainThread);
    
    /**
     * @brief Execute a thread control command
     * @param command The command to execute
     * @param parameters Optional command parameters
     * @return ThreadControlResult indicating success or failure
     */
    ThreadControlResult ExecuteCommand(ThreadControlCommand command, 
                                     const std::unordered_map<std::string, std::string>& parameters = {});
    
    /**
     * @brief Get current thread state
     * @return Current ThreadState
     */
    ThreadState GetCurrentState() const;
    
    /**
     * @brief Get thread control statistics
     * @return const reference to control statistics
     */
    const ThreadControlStats& GetControlStats() const;
    
    /**
     * @brief Get thread control configuration
     * @return const reference to control configuration
     */
    const ThreadControlConfig& GetControlConfig() const;
    
    /**
     * @brief Update thread control configuration
     * @param config New configuration
     * @return ThreadControlResult indicating success or failure
     */
    ThreadControlResult UpdateConfig(const ThreadControlConfig& config);
    
    /**
     * @brief Check if thread is in a healthy state
     * @return true if healthy, false otherwise
     */
    bool IsHealthy() const;
    
    /**
     * @brief Get the last control error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Wait for thread to reach a specific state
     * @param targetState The state to wait for
     * @param timeout Maximum time to wait
     * @return true if state reached, false if timeout
     */
    bool WaitForState(ThreadState targetState, std::chrono::milliseconds timeout = std::chrono::milliseconds(5000));
    
    /**
     * @brief Register a state change callback
     * @param callback Function to call when state changes
     */
    void RegisterStateChangeCallback(std::function<void(ThreadState, ThreadState)> callback);
    
    /**
     * @brief Start the control monitoring system
     * @return ThreadControlResult indicating success or failure
     */
    ThreadControlResult StartMonitoring();
    
    /**
     * @brief Stop the control monitoring system
     * @return ThreadControlResult indicating success or failure
     */
    ThreadControlResult StopMonitoring();

private:
    // Thread control state
    std::atomic<ThreadState> m_currentState{ThreadState::Uninitialized};
    CMainThread* m_mainThread{nullptr};
    
    // Control statistics and configuration
    ThreadControlStats m_controlStats;
    ThreadControlConfig m_controlConfig;
    mutable std::mutex m_statsMutex;
    mutable std::mutex m_configMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // State management
    std::condition_variable m_stateCondition;
    std::mutex m_stateMutex;
    std::vector<std::function<void(ThreadState, ThreadState)>> m_stateCallbacks;
    
    // Monitoring
    std::atomic<bool> m_monitoringEnabled{false};
    std::thread m_monitoringThread;
    std::atomic<bool> m_shutdownRequested{false};
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Core control functions
    ThreadControlResult InitializeControl();
    ThreadControlResult StartControl();
    ThreadControlResult StopControl();
    ThreadControlResult PauseControl();
    ThreadControlResult ResumeControl();
    ThreadControlResult ShutdownControl();
    
    // Status and monitoring functions
    ThreadControlResult GetStatusInfo();
    ThreadControlResult GetStatisticsInfo();
    ThreadControlResult CheckHealthStatus();
    
    // Configuration functions
    ThreadControlResult SetThreadPriority(uint32_t priority);
    ThreadControlResult SetThreadAffinity(uint64_t affinity);
    ThreadControlResult SetCommandTimeout(std::chrono::milliseconds timeout);
    
    // Synchronization functions
    ThreadControlResult WaitForCompletionInternal();
    ThreadControlResult SignalReadyInternal();
    ThreadControlResult SynchronizeInternal();
    
    // Network control functions
    ThreadControlResult StartNetworkingInternal();
    ThreadControlResult StopNetworkingInternal();
    ThreadControlResult ResetConnectionsInternal();
    
    // Data control functions
    ThreadControlResult ReloadDataInternal();
    ThreadControlResult ValidateDataInternal();
    ThreadControlResult BackupDataInternal();
    
    // System control functions
    ThreadControlResult EnableWorldServiceInternal();
    ThreadControlResult DisableWorldServiceInternal();
    ThreadControlResult OpenWorldInternal();
    ThreadControlResult CloseWorldInternal();
    
    // Emergency functions
    ThreadControlResult EmergencyStopInternal();
    ThreadControlResult ForceRestartInternal();
    ThreadControlResult SafeShutdownInternal();
    
    // Utility functions
    void LogCommand(ThreadControlCommand command, bool success, const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    void ChangeState(ThreadState newState);
    void MonitoringThreadFunction();
    bool ValidateCommand(ThreadControlCommand command) const;
    bool ValidateState(ThreadState requiredState) const;
    
    // Legacy compatibility functions
    static void SetWorldServiceState(CMainThread* mainThread, bool enabled);
    static void SetWorldOpenState(CMainThread* mainThread, bool open);
    static void InitializeThreadFlags(CMainThread* mainThread);
};

/**
 * @brief Convert ThreadControlCommand enum to string for logging
 * @param command The command
 * @return String representation of the command
 */
std::string ThreadControlCommandToString(ThreadControlCommand command);

/**
 * @brief Convert ThreadControlResult enum to string for logging
 * @param result The result
 * @return String representation of the result
 */
std::string ThreadControlResultToString(ThreadControlResult result);

/**
 * @brief Convert ThreadState enum to string for logging
 * @param state The state
 * @return String representation of the state
 */
std::string ThreadStateToString(ThreadState state);

// External thread control storage
extern std::unique_ptr<CMainThreadControl> g_MainThreadControl;
extern std::mutex g_MainThreadControlMutex;
