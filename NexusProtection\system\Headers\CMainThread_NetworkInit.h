/**
 * @file CMainThread_NetworkInit.h
 * @brief Network initialization system for CMainThread
 * 
 * Refactored from NetworkInitCMainThreadAEAA_NXZ_1401EB330.c
 * This handles the initialization of all network systems including client connections,
 * account lines, web lines, and billing connections.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>

// Forward declarations for network classes
class CNetWorking;
struct _NET_TYPE_PARAM;

/**
 * @enum NetworkLineType
 * @brief Enumeration of all network line types for initialization tracking
 */
enum class NetworkLineType : uint32_t {
    ClientLine = 0,
    AccountLine,
    WebLine,
    BillingLine,
    
    MAX_NETWORK_LINES
};

/**
 * @enum NetworkInitResult
 * @brief Result codes for network initialization operations
 */
enum class NetworkInitResult : int32_t {
    Success = 1,
    Failure = 0,
    NetworkSystemFailed = -1,
    InvalidParameters = -2,
    ConfigurationError = -3,
    SecurityError = -4
};

/**
 * @struct NetworkLineConfig
 * @brief Configuration for a network line
 */
struct NetworkLineConfig {
    std::string name;
    std::string description;
    uint16_t port;
    uint16_t maxConnections;
    uint32_t sendBufferSize;
    uint32_t recvBufferSize;
    uint8_t recvThreadNum;
    uint8_t recvSleepTime;
    bool isServer;
    bool realSockCheck;
    bool systemLogFile;
    bool svrToS;
    bool oddMsgWriteLog;
    bool anSyncConnect;
    
    NetworkLineConfig() = default;
    NetworkLineConfig(const std::string& n, const std::string& desc, uint16_t p, uint16_t maxConn = 1)
        : name(n), description(desc), port(p), maxConnections(maxConn),
          sendBufferSize(1000000), recvBufferSize(1000000),
          recvThreadNum(1), recvSleepTime(1), isServer(true),
          realSockCheck(false), systemLogFile(true), svrToS(true),
          oddMsgWriteLog(true), anSyncConnect(true) {}
};

/**
 * @struct NetworkInitStats
 * @brief Statistics for network initialization process
 */
struct NetworkInitStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(NetworkLineType::MAX_NETWORK_LINES)> lineStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(NetworkLineType::MAX_NETWORK_LINES)> lineInitTime;
    uint32_t successfulLines;
    uint32_t failedLines;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        lineStatus.fill(false);
        lineInitTime.fill(std::chrono::milliseconds::zero());
        successfulLines = 0;
        failedLines = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalInitTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulLines + failedLines;
        return total > 0 ? (static_cast<double>(successfulLines) / total) * 100.0 : 0.0;
    }
};

/**
 * @class CMainThreadNetworkInit
 * @brief Network initialization and management system
 * 
 * This class handles the initialization of all network systems including
 * client connections, account management, web services, and billing systems.
 * It provides modern C++20 interfaces while maintaining compatibility with
 * the original network structures.
 * 
 * Original function: CMainThread::NetworkInit (Address: 0x1401EB330)
 * Refactored to modern C++20 with comprehensive error handling and monitoring.
 */
class CMainThreadNetworkInit {
public:
    /**
     * @brief Constructor
     */
    CMainThreadNetworkInit();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadNetworkInit();
    
    // Delete copy constructor and assignment operator
    CMainThreadNetworkInit(const CMainThreadNetworkInit&) = delete;
    CMainThreadNetworkInit& operator=(const CMainThreadNetworkInit&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadNetworkInit(CMainThreadNetworkInit&&) = default;
    CMainThreadNetworkInit& operator=(CMainThreadNetworkInit&&) = default;
    
    /**
     * @brief Main network initialization function
     * 
     * Initializes all network systems in the correct order with comprehensive error handling.
     * This is the modern refactored version of the original NetworkInit function.
     * 
     * @param mainThread Pointer to CMainThread instance for accessing network systems
     * @return NetworkInitResult indicating success or failure
     */
    NetworkInitResult InitializeNetworkSystems(class CMainThread* mainThread);
    
    /**
     * @brief Legacy NetworkInit function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: char __fastcall CMainThread::NetworkInit(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return char (1 for success, 0 for failure)
     */
    static char NetworkInit_Legacy(class CMainThread* mainThread);
    
    /**
     * @brief Get network initialization statistics
     * @return const reference to initialization statistics
     */
    const NetworkInitStats& GetInitializationStats() const;
    
    /**
     * @brief Check if a specific network line is initialized
     * @param lineType The network line type to check
     * @return true if initialized, false otherwise
     */
    bool IsLineInitialized(NetworkLineType lineType) const;
    
    /**
     * @brief Get the last initialization error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get configuration for a specific network line type
     * @param lineType The network line type
     * @return NetworkLineConfig structure with line configuration
     */
    static NetworkLineConfig GetLineConfig(NetworkLineType lineType);
    
    /**
     * @brief Validate all initialized network lines
     * @return true if all required lines are initialized and valid
     */
    bool ValidateInitializedLines() const;

private:
    // Initialization statistics and monitoring
    NetworkInitStats m_initStats;
    mutable std::mutex m_statsMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // Network line configuration registry
    static std::unordered_map<NetworkLineType, NetworkLineConfig> s_lineRegistry;
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Network line initialization functions
    NetworkInitResult InitializeClientLine(class CMainThread* mainThread);
    NetworkInitResult InitializeAccountLine(class CMainThread* mainThread);
    NetworkInitResult InitializeWebLine(class CMainThread* mainThread);
    NetworkInitResult InitializeBillingLine(class CMainThread* mainThread);
    
    // Network system setup functions
    NetworkInitResult SetupNetworkSystem(class CMainThread* mainThread);
    NetworkInitResult ConfigureNetworkParameters(class CMainThread* mainThread);
    NetworkInitResult AddPassablePackets(class CMainThread* mainThread);
    
    // Utility functions
    void LogLineInitialization(NetworkLineType lineType, bool success, const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    
    // Network parameter creation helpers
    _NET_TYPE_PARAM CreateNetTypeParam(const NetworkLineConfig& config);
    void ConfigureServiceMode(class CMainThread* mainThread, _NET_TYPE_PARAM& param);
    
    // Static initialization
    static void InitializeLineRegistry();
    
    // Legacy compatibility functions
    static bool SetNetSystem_Legacy(CNetWorking* networking, uint32_t lineCount, 
                                   _NET_TYPE_PARAM* params, const char* serverName, 
                                   const char* logPath);
    static void AddPassablePacket_Legacy(class CMainThread* mainThread);
    static bool IsReleaseServiceMode_Legacy(class CMainThread* mainThread);
};

/**
 * @brief Convert NetworkLineType enum to string for logging
 * @param lineType The network line type
 * @return String representation of the line type
 */
std::string NetworkLineTypeToString(NetworkLineType lineType);

/**
 * @brief Convert NetworkInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string NetworkInitResultToString(NetworkInitResult result);

/**
 * @brief Network parameter structure for legacy compatibility
 * This mirrors the original _NET_TYPE_PARAM structure
 */
struct _NET_TYPE_PARAM {
    char m_szModuleName[128];
    uint16_t m_wSocketMaxNum;
    uint8_t m_byRecvThreadNum;
    uint8_t m_byRecvSleepTime;
    uint32_t m_dwSendBufferSize;
    uint32_t m_dwRecvBufferSize;
    bool m_bServer;
    bool m_bRealSockCheck;
    bool m_bSystemLogFile;
    bool m_bSvrToS;
    bool m_bOddMsgWriteLog;
    bool m_bAnSyncConnect;
    
    // Constructor
    _NET_TYPE_PARAM();
    
    // Copy constructor
    _NET_TYPE_PARAM(const _NET_TYPE_PARAM& other);
    
    // Assignment operator
    _NET_TYPE_PARAM& operator=(const _NET_TYPE_PARAM& other);
};

// External function declarations (to be properly linked)
extern bool CNetWorking_SetNetSystem(CNetWorking* networking, uint32_t lineCount, 
                                     _NET_TYPE_PARAM* params, const char* serverName, 
                                     const char* logPath);
extern void CMainThread_AddPassablePacket(class CMainThread* mainThread);
extern bool CMainThread_IsReleaseServiceMode(class CMainThread* mainThread);

// External global objects
extern CNetWorking g_Network;
