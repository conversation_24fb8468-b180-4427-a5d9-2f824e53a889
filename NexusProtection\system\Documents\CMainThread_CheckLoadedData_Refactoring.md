# CMainThread CheckLoadedData System Refactoring

## Overview

This document describes the refactoring of the `CMainThread::check_loaded_data` function from a simple boolean check into a comprehensive data validation system. The refactored system provides modern C++20 interfaces while maintaining full backward compatibility.

## Original Function

The original `check_loaded_data` function was extremely simple:
```cpp
bool __fastcall CMainThread::check_loaded_data(CMainThread *this)
{
    return true;  // Always returned true
}
```

The actual data validation was handled implicitly by other systems during the data loading process.

## Refactored Architecture

### Core Components

1. **CMainThreadCheckLoadedData Class** - Main validation system
2. **DataValidationType Enum** - 32 different validation types
3. **DataValidationResult Enum** - Comprehensive result codes
4. **DataValidationStats Structure** - Real-time statistics tracking
5. **Validation Registry** - Metadata for all validation types

### Key Features

- **Comprehensive Validation**: Validates 32 different data types including items, skills, characters, units, and manager systems
- **Real-time Statistics**: Tracks validation time, success rates, and record counts
- **Error Handling**: Detailed error messages and exception handling
- **Security**: Stack protection with security cookies
- **Backward Compatibility**: Multiple compatibility layers for existing code

## Validation Types (32 Total)

### Core Data Tables (5 types)
- ItemData - Core item database
- SkillData - Skill database  
- ForceData - Force power database
- ClassSkillData - Class-specific skills
- BulletItemEffectData - Bullet and item effects

### Character Data (5 types)
- ClassData - Character classes
- GradeData - Character grades
- PlayerCharacterData - Player characters
- MonsterCharacterData - Monster characters
- NPCharacterData - NPC characters

### Item Systems (8 types)
- AnimusItemData - Animus items
- ExpData - Experience points
- ItemLootingData - Item looting
- OreCuttingData - Ore cutting
- ItemMakeData - Item crafting
- ItemCombineData - Item combination
- ItemExchangeData - Item exchange
- ItemUpgradeData - Item upgrades

### Unit Data (8 types)
- UnitHeadData - Head parts
- UnitUpperData - Upper body parts
- UnitLowerData - Lower body parts
- UnitArmsData - Arms parts
- UnitShoulderData - Shoulder parts
- UnitBackData - Back parts
- UnitBulletData - Bullet parts
- UnitFrameData - Frame parts

### System Data (3 types)
- EditData - Edit system
- MonsterCharacterAIData - Monster AI
- MobMessageData - Monster messages

### Manager Systems (6 types)
- PotionSystem - Potion manager
- QuestSystem - Quest manager
- ItemCombineSystem - Item combine manager
- PcBangSystem - PC Bang favor (optional)
- SUItemSystem - Special upgrade items (optional)
- MonsterSPGroupTable - Monster special groups (optional)

### Configuration Data (2 types)
- AggroCalculateConfig - Aggro calculation
- MonsterSetConfig - Monster sets

## Usage Examples

### Modern Interface
```cpp
CMainThreadCheckLoadedData validator;
DataValidationResult result = validator.ValidateAllLoadedData(mainThread);

if (result == DataValidationResult::Success) {
    std::cout << "All data validated successfully!" << std::endl;
    
    // Get statistics
    const auto& stats = validator.GetValidationStats();
    std::cout << "Validation time: " << stats.GetTotalValidationTime().count() << "ms" << std::endl;
    std::cout << "Success rate: " << stats.GetSuccessRate() << "%" << std::endl;
} else {
    std::cout << "Validation failed: " << validator.GetLastError() << std::endl;
}
```

### Legacy Compatibility
```cpp
// Original function signature
bool success = CMainThreadCheckLoadedData::CheckLoadedData_Legacy(mainThread);

// Enhanced legacy wrapper
bool success = CMainThread_CheckLoadedData_Enhanced(mainThread);

// Simple validation
bool success = CMainThread_CheckLoadedData_Simple(mainThread);

// Comprehensive validation with logging
bool success = CMainThread_CheckLoadedData_Comprehensive(mainThread);
```

### Individual Data Type Validation
```cpp
CMainThreadCheckLoadedData validator;

// Check if specific data is validated
bool itemDataValid = validator.IsDataValidated(DataValidationType::ItemData);

// Get validation info
auto info = validator.GetValidationInfo(DataValidationType::SkillData);
std::cout << "Skill Data: " << info.description << std::endl;
```

## Validation Process

### Phase 1: Core Data Tables
Validates the fundamental game data including items, skills, forces, and effects.

### Phase 2: Character Data  
Validates all character-related data including classes, grades, players, monsters, and NPCs.

### Phase 3: Item Systems
Validates all item-related systems including crafting, combining, upgrading, and looting.

### Phase 4: Unit Data
Validates all unit part data for character customization and equipment.

### Phase 5: System Data
Validates system-level data including edit data, AI data, and messaging.

### Phase 6: Manager Systems
Validates all manager systems that control game mechanics.

### Phase 7: Configuration Data
Validates configuration data for various game systems.

## Error Handling

### Result Codes
- **Success** (1) - Validation completed successfully
- **Failure** (0) - General validation failure
- **DataNotLoaded** (-1) - Required data not loaded
- **InvalidData** (-2) - Data is invalid or corrupted
- **CorruptedData** (-3) - Data integrity check failed
- **MissingRecords** (-4) - Insufficient records in data table
- **ValidationError** (-5) - Exception during validation
- **SecurityError** (-6) - Security cookie verification failed

### Error Messages
Detailed error messages are provided for all validation failures, including:
- Specific data type that failed
- Reason for failure
- Expected vs actual record counts
- Exception details

## Performance Considerations

### Timing
- Individual validation timing for each data type
- Total validation time tracking
- Performance statistics and success rates

### Memory Usage
- Minimal memory overhead
- RAII principles for automatic cleanup
- Efficient data structure usage

### Threading
- Thread-safe statistics tracking
- Mutex protection for shared data
- Atomic operations where appropriate

## Security Features

### Stack Protection
- Security cookie verification (equivalent to original /GS protection)
- Stack corruption detection
- Buffer overflow protection

### Data Integrity
- Record count validation
- Data corruption detection
- Integrity verification for critical data

## Migration Guide

### For Existing Code
1. **No Changes Required** - Legacy wrappers maintain exact compatibility
2. **Enhanced Validation** - Use `CMainThread_CheckLoadedData_Enhanced()` for better error handling
3. **Modern Interface** - Migrate to `CMainThreadCheckLoadedData` class for full features

### Recommended Migration Path
1. Start with legacy wrappers for immediate compatibility
2. Gradually migrate to enhanced wrappers for better error handling
3. Eventually adopt modern interface for full feature access

## Testing Strategy

### Unit Tests
- Individual validation function tests
- Error condition testing
- Performance benchmarking
- Memory leak detection

### Integration Tests
- Full validation process testing
- Compatibility testing with existing code
- Performance regression testing
- Security vulnerability testing

### Load Testing
- Large dataset validation
- Concurrent validation testing
- Memory usage under load
- Performance under stress

## Future Enhancements

### Planned Features
- Custom validation rules per data type
- Validation result caching
- Parallel validation processing
- Configuration-driven validation rules

### Extensibility
- Plugin architecture for custom validators
- Dynamic validation rule loading
- External validation service integration
- Real-time validation monitoring

## Compatibility Matrix

| Interface | Original Behavior | Enhanced Features | Performance | Recommended Use |
|-----------|------------------|-------------------|-------------|-----------------|
| Legacy Wrapper | ✅ Exact | ❌ None | ⚡ Fast | Existing code |
| Enhanced Wrapper | ✅ Compatible | ✅ Error handling | ⚡ Fast | Migration |
| Simple Wrapper | ✅ Compatible | ✅ Basic validation | ⚡ Fastest | Quick checks |
| Modern Interface | ❌ Different | ✅ Full features | 🐌 Comprehensive | New code |

## Conclusion

The refactored CheckLoadedData system provides a robust, comprehensive data validation framework while maintaining full backward compatibility. The system offers multiple interfaces to suit different use cases, from simple legacy compatibility to full-featured modern validation with detailed statistics and error handling.

The modular design allows for easy extension and customization while providing the security and performance characteristics required for a production game server environment.
