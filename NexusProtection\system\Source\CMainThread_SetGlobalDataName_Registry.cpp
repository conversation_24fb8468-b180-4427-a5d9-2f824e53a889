/**
 * @file CMainThread_SetGlobalDataName_Registry.cpp
 * @brief Name registry initialization and string conversion functions
 * 
 * Contains the initialization of the name registry and utility
 * functions for converting enums to strings.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_SetGlobalDataName.h"
#include <unordered_map>

/**
 * @brief Initialize the name registry with all name types
 */
void CMainThreadSetGlobalDataName::InitializeNameRegistry() {
    if (!s_nameRegistry.empty()) {
        return; // Already initialized
    }
    
    // Core Data Names
    s_nameRegistry[GlobalDataNameType::ItemDataName] = 
        GlobalDataNameInfo("Item Data Name", "Core item database name", "ItemData", true, 128);
    s_nameRegistry[GlobalDataNameType::SkillDataName] = 
        GlobalDataNameInfo("Skill Data Name", "Skill database name", "SkillData", true, 128);
    s_nameRegistry[GlobalDataNameType::ForceDataName] = 
        GlobalDataNameInfo("Force Data Name", "Force power database name", "ForceData", true, 128);
    s_nameRegistry[GlobalDataNameType::ClassSkillDataName] = 
        GlobalDataNameInfo("Class Skill Data Name", "Class-specific skill database name", "ClassSkillData", true, 128);
    s_nameRegistry[GlobalDataNameType::BulletItemEffectDataName] = 
        GlobalDataNameInfo("Bullet Item Effect Data Name", "Bullet and item effect database name", "BulletItemEffectData", true, 128);
    
    // Character Data Names
    s_nameRegistry[GlobalDataNameType::ClassDataName] = 
        GlobalDataNameInfo("Class Data Name", "Character class database name", "ClassData", true, 128);
    s_nameRegistry[GlobalDataNameType::GradeDataName] = 
        GlobalDataNameInfo("Grade Data Name", "Character grade database name", "GradeData", true, 128);
    s_nameRegistry[GlobalDataNameType::PlayerCharacterDataName] = 
        GlobalDataNameInfo("Player Character Data Name", "Player character database name", "PlayerCharacterData", true, 128);
    s_nameRegistry[GlobalDataNameType::MonsterCharacterDataName] = 
        GlobalDataNameInfo("Monster Character Data Name", "Monster character database name", "MonsterCharacterData", true, 128);
    s_nameRegistry[GlobalDataNameType::NPCharacterDataName] = 
        GlobalDataNameInfo("NPC Character Data Name", "NPC character database name", "NPCharacterData", true, 128);
    
    // Item System Names
    s_nameRegistry[GlobalDataNameType::AnimusItemDataName] = 
        GlobalDataNameInfo("Animus Item Data Name", "Animus item database name", "AnimusItemData", true, 128);
    s_nameRegistry[GlobalDataNameType::ExpDataName] = 
        GlobalDataNameInfo("Experience Data Name", "Experience point database name", "ExpData", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemLootingDataName] = 
        GlobalDataNameInfo("Item Looting Data Name", "Item looting database name", "ItemLootingData", true, 128);
    s_nameRegistry[GlobalDataNameType::OreCuttingDataName] = 
        GlobalDataNameInfo("Ore Cutting Data Name", "Ore cutting database name", "OreCuttingData", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemMakeDataName] = 
        GlobalDataNameInfo("Item Make Data Name", "Item crafting database name", "ItemMakeData", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemCombineDataName] = 
        GlobalDataNameInfo("Item Combine Data Name", "Item combination database name", "ItemCombineData", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemExchangeDataName] = 
        GlobalDataNameInfo("Item Exchange Data Name", "Item exchange database name", "ItemExchangeData", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemUpgradeDataName] = 
        GlobalDataNameInfo("Item Upgrade Data Name", "Item upgrade database name", "ItemUpgradeData", true, 128);
    
    // Unit Data Names
    s_nameRegistry[GlobalDataNameType::UnitHeadDataName] = 
        GlobalDataNameInfo("Unit Head Data Name", "Unit head part database name", "UnitHeadData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitUpperDataName] = 
        GlobalDataNameInfo("Unit Upper Data Name", "Unit upper body part database name", "UnitUpperData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitLowerDataName] = 
        GlobalDataNameInfo("Unit Lower Data Name", "Unit lower body part database name", "UnitLowerData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitArmsDataName] = 
        GlobalDataNameInfo("Unit Arms Data Name", "Unit arms part database name", "UnitArmsData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitShoulderDataName] = 
        GlobalDataNameInfo("Unit Shoulder Data Name", "Unit shoulder part database name", "UnitShoulderData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitBackDataName] = 
        GlobalDataNameInfo("Unit Back Data Name", "Unit back part database name", "UnitBackData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitBulletDataName] = 
        GlobalDataNameInfo("Unit Bullet Data Name", "Unit bullet part database name", "UnitBulletData", true, 128);
    s_nameRegistry[GlobalDataNameType::UnitFrameDataName] = 
        GlobalDataNameInfo("Unit Frame Data Name", "Unit frame part database name", "UnitFrameData", true, 128);
    
    // System Data Names
    s_nameRegistry[GlobalDataNameType::EditDataName] = 
        GlobalDataNameInfo("Edit Data Name", "Edit system database name", "EditData", true, 128);
    s_nameRegistry[GlobalDataNameType::MonsterCharacterAIDataName] = 
        GlobalDataNameInfo("Monster Character AI Data Name", "Monster AI database name", "MonsterCharacterAIData", true, 128);
    s_nameRegistry[GlobalDataNameType::MobMessageDataName] = 
        GlobalDataNameInfo("Mob Message Data Name", "Monster message database name", "MobMessageData", true, 128);
    
    // Manager System Names
    s_nameRegistry[GlobalDataNameType::PotionManagerName] = 
        GlobalDataNameInfo("Potion Manager Name", "Potion manager system name", "PotionManager", true, 128);
    s_nameRegistry[GlobalDataNameType::QuestManagerName] = 
        GlobalDataNameInfo("Quest Manager Name", "Quest manager system name", "QuestManager", true, 128);
    s_nameRegistry[GlobalDataNameType::ItemCombineManagerName] = 
        GlobalDataNameInfo("Item Combine Manager Name", "Item combine manager system name", "ItemCombineManager", true, 128);
    s_nameRegistry[GlobalDataNameType::PcBangSystemName] = 
        GlobalDataNameInfo("PC Bang System Name", "PC Bang favor system name", "PcBangSystem", false, 128);
    s_nameRegistry[GlobalDataNameType::SUItemSystemName] = 
        GlobalDataNameInfo("SU Item System Name", "Special upgrade item system name", "SUItemSystem", false, 128);
    s_nameRegistry[GlobalDataNameType::MonsterSPGroupTableName] = 
        GlobalDataNameInfo("Monster SP Group Table Name", "Monster special group table name", "MonsterSPGroupTable", false, 128);
    
    // Configuration Names
    s_nameRegistry[GlobalDataNameType::AggroCalculateConfigName] = 
        GlobalDataNameInfo("Aggro Calculate Config Name", "Aggro calculation configuration name", "AggroCalculateConfig", true, 128);
    s_nameRegistry[GlobalDataNameType::MonsterSetConfigName] = 
        GlobalDataNameInfo("Monster Set Config Name", "Monster set configuration name", "MonsterSetConfig", true, 128);
    
    // Special Global Names
    s_nameRegistry[GlobalDataNameType::ServerInstanceName] = 
        GlobalDataNameInfo("Server Instance Name", "Server instance identifier", "NexusProtectionServer", true, 256);
    s_nameRegistry[GlobalDataNameType::DatabaseConnectionName] = 
        GlobalDataNameInfo("Database Connection Name", "Main database connection identifier", "MainDatabaseConnection", true, 256);
    s_nameRegistry[GlobalDataNameType::NetworkConfigName] = 
        GlobalDataNameInfo("Network Config Name", "Network configuration identifier", "NetworkConfiguration", true, 256);
    s_nameRegistry[GlobalDataNameType::SecurityConfigName] = 
        GlobalDataNameInfo("Security Config Name", "Security configuration identifier", "SecurityConfiguration", true, 256);
    s_nameRegistry[GlobalDataNameType::LoggingConfigName] = 
        GlobalDataNameInfo("Logging Config Name", "Logging configuration identifier", "LoggingConfiguration", true, 256);
}

/**
 * @brief Convert GlobalDataNameType enum to string for logging
 * @param nameType The name type
 * @return String representation of the name type
 */
std::string GlobalDataNameTypeToString(GlobalDataNameType nameType) {
    static const std::unordered_map<GlobalDataNameType, std::string> typeNames = {
        // Core Data Names
        {GlobalDataNameType::ItemDataName, "ItemDataName"},
        {GlobalDataNameType::SkillDataName, "SkillDataName"},
        {GlobalDataNameType::ForceDataName, "ForceDataName"},
        {GlobalDataNameType::ClassSkillDataName, "ClassSkillDataName"},
        {GlobalDataNameType::BulletItemEffectDataName, "BulletItemEffectDataName"},
        
        // Character Data Names
        {GlobalDataNameType::ClassDataName, "ClassDataName"},
        {GlobalDataNameType::GradeDataName, "GradeDataName"},
        {GlobalDataNameType::PlayerCharacterDataName, "PlayerCharacterDataName"},
        {GlobalDataNameType::MonsterCharacterDataName, "MonsterCharacterDataName"},
        {GlobalDataNameType::NPCharacterDataName, "NPCharacterDataName"},
        
        // Item System Names
        {GlobalDataNameType::AnimusItemDataName, "AnimusItemDataName"},
        {GlobalDataNameType::ExpDataName, "ExpDataName"},
        {GlobalDataNameType::ItemLootingDataName, "ItemLootingDataName"},
        {GlobalDataNameType::OreCuttingDataName, "OreCuttingDataName"},
        {GlobalDataNameType::ItemMakeDataName, "ItemMakeDataName"},
        {GlobalDataNameType::ItemCombineDataName, "ItemCombineDataName"},
        {GlobalDataNameType::ItemExchangeDataName, "ItemExchangeDataName"},
        {GlobalDataNameType::ItemUpgradeDataName, "ItemUpgradeDataName"},
        
        // Unit Data Names
        {GlobalDataNameType::UnitHeadDataName, "UnitHeadDataName"},
        {GlobalDataNameType::UnitUpperDataName, "UnitUpperDataName"},
        {GlobalDataNameType::UnitLowerDataName, "UnitLowerDataName"},
        {GlobalDataNameType::UnitArmsDataName, "UnitArmsDataName"},
        {GlobalDataNameType::UnitShoulderDataName, "UnitShoulderDataName"},
        {GlobalDataNameType::UnitBackDataName, "UnitBackDataName"},
        {GlobalDataNameType::UnitBulletDataName, "UnitBulletDataName"},
        {GlobalDataNameType::UnitFrameDataName, "UnitFrameDataName"},
        
        // System Data Names
        {GlobalDataNameType::EditDataName, "EditDataName"},
        {GlobalDataNameType::MonsterCharacterAIDataName, "MonsterCharacterAIDataName"},
        {GlobalDataNameType::MobMessageDataName, "MobMessageDataName"},
        
        // Manager System Names
        {GlobalDataNameType::PotionManagerName, "PotionManagerName"},
        {GlobalDataNameType::QuestManagerName, "QuestManagerName"},
        {GlobalDataNameType::ItemCombineManagerName, "ItemCombineManagerName"},
        {GlobalDataNameType::PcBangSystemName, "PcBangSystemName"},
        {GlobalDataNameType::SUItemSystemName, "SUItemSystemName"},
        {GlobalDataNameType::MonsterSPGroupTableName, "MonsterSPGroupTableName"},
        
        // Configuration Names
        {GlobalDataNameType::AggroCalculateConfigName, "AggroCalculateConfigName"},
        {GlobalDataNameType::MonsterSetConfigName, "MonsterSetConfigName"},
        
        // Special Global Names
        {GlobalDataNameType::ServerInstanceName, "ServerInstanceName"},
        {GlobalDataNameType::DatabaseConnectionName, "DatabaseConnectionName"},
        {GlobalDataNameType::NetworkConfigName, "NetworkConfigName"},
        {GlobalDataNameType::SecurityConfigName, "SecurityConfigName"},
        {GlobalDataNameType::LoggingConfigName, "LoggingConfigName"}
    };
    
    auto it = typeNames.find(nameType);
    if (it != typeNames.end()) {
        return it->second;
    }
    return "Unknown";
}

/**
 * @brief Convert GlobalDataNameResult enum to string for logging
 * @param result The assignment result
 * @return String representation of the result
 */
std::string GlobalDataNameResultToString(GlobalDataNameResult result) {
    switch (result) {
        case GlobalDataNameResult::Success:
            return "Success";
        case GlobalDataNameResult::Failure:
            return "Failure";
        case GlobalDataNameResult::InvalidName:
            return "InvalidName";
        case GlobalDataNameResult::DuplicateName:
            return "DuplicateName";
        case GlobalDataNameResult::NameTooLong:
            return "NameTooLong";
        case GlobalDataNameResult::InvalidCharacters:
            return "InvalidCharacters";
        case GlobalDataNameResult::SystemError:
            return "SystemError";
        case GlobalDataNameResult::SecurityError:
            return "SecurityError";
        default:
            return "Unknown";
    }
}
