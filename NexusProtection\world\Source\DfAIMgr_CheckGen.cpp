/*
 * DfAIMgr_CheckGen.cpp - AI Attack Generation Implementation
 * Refactored from CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/DfAIMgr.h"
#include "../Headers/CMonsterAI.h"
#include "../Headers/CMonster.h"
#include "../Headers/CCharacter.h"
#include "../Headers/CMonsterSkill.h"
#include "../Headers/CMonsterSkillPool.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cmath>

// External dependencies (to be properly linked)
extern float GetSqrt(const float pos1[3], const float pos2[3]);
extern float R3GetLoopTime();
extern uint32_t GetLoopTime();

// Static member initialization
AIAttackParams DfAIMgr::s_attackParams;
bool DfAIMgr::s_debuggingEnabled = false;
uint64_t DfAIMgr::s_totalAttackChecks = 0;
uint64_t DfAIMgr::s_successfulAttacks = 0;
uint64_t DfAIMgr::s_failedAttacks = 0;
uint64_t DfAIMgr::s_targetsNotFound = 0;
uint64_t DfAIMgr::s_outOfRangeAttacks = 0;
uint64_t DfAIMgr::s_skillsNotReady = 0;

/**
 * Check if monster can generate an attack
 * Refactored from original CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.c
 * 
 * @param pAI Pointer to monster AI
 * @param pMonster Pointer to monster
 * @param distanceThreshold Distance threshold for attack consideration
 * @return AIAttackResult indicating the result of attack generation
 */
AIAttackResult DfAIMgr::CheckGen(CMonsterAI* pAI, CMonster* pMonster, float distanceThreshold) {
    try {
        s_totalAttackChecks++;
        
        LogDebug("DfAIMgr::CheckGen - Starting attack generation check");
        
        // Validate input parameters
        if (!ValidateInputParameters(pAI, pMonster)) {
            LogAIOperation("CheckGen - Invalid parameters", pMonster, AIAttackResult::InvalidParameters);
            UpdateStatistics(AIAttackResult::InvalidParameters);
            return AIAttackResult::InvalidParameters;
        }
        
        // Get attack target
        CCharacter* target = CMonster::GetAttackTarget(pMonster);
        if (!target) {
            LogDebug("CheckGen - No attack target found");
            UpdateStatistics(AIAttackResult::TargetNotFound);
            return AIAttackResult::TargetNotFound;
        }
        
        // Initialize attack context
        AIAttackContext context;
        if (!InitializeAttackContext(pMonster, target, distanceThreshold, context)) {
            LogAIOperation("CheckGen - Failed to initialize attack context", pMonster, AIAttackResult::OutOfRange);
            UpdateStatistics(AIAttackResult::OutOfRange);
            return AIAttackResult::OutOfRange;
        }
        
        // Check if target is in valid range
        if (!IsTargetInValidRange(context)) {
            LogDebug("CheckGen - Target not in valid range", &context);
            UpdateStatistics(AIAttackResult::OutOfRange);
            return AIAttackResult::OutOfRange;
        }
        
        // Apply movement prediction if enabled
        if (s_attackParams.enableMovementPrediction) {
            ApplyMovementPrediction(pMonster, context);
        }
        
        // Evaluate skills for attack
        AIAttackResult result = EvaluateSkillsForAttack(pMonster, context);
        
        LogAIOperation("CheckGen completed", pMonster, result);
        UpdateStatistics(result);
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("DfAIMgr::CheckGen - Exception occurred: %s", e.what());
        UpdateStatistics(AIAttackResult::NoAttack);
        return AIAttackResult::NoAttack;
    } catch (...) {
        Logger::Error("DfAIMgr::CheckGen - Unknown exception occurred");
        UpdateStatistics(AIAttackResult::NoAttack);
        return AIAttackResult::NoAttack;
    }
}

/**
 * Validate input parameters for attack generation
 */
bool DfAIMgr::ValidateInputParameters(CMonsterAI* pAI, CMonster* pMonster) {
    if (!pAI) {
        Logger::Error("ValidateInputParameters - Monster AI is null");
        return false;
    }
    
    if (!pMonster) {
        Logger::Error("ValidateInputParameters - Monster is null");
        return false;
    }
    
    // Additional validation can be added here
    // For example: check if monster is alive, not stunned, etc.
    
    LogDebug("ValidateInputParameters - All parameters validated successfully");
    return true;
}

/**
 * Initialize attack context
 */
bool DfAIMgr::InitializeAttackContext(CMonster* pMonster, CCharacter* target,
                                     float distanceThreshold, AIAttackContext& context) {
    try {
        context.Reset();
        context.target = target;
        context.currentTime = GetLoopTime();
        
        // Calculate distance to target using GetSqrt (equivalent to original)
        context.targetDistance = GetSqrt(pMonster->m_fCurPos, target->m_fCurPos);
        
        // Calculate height difference (equivalent to original v6 calculation)
        context.heightDifference = std::abs(pMonster->m_fCurPos[1] - target->m_fCurPos[1]);
        
        // Check if target is moving
        context.targetIsMoving = target->m_bMove;
        
        // Set initial adjusted distance
        context.adjustedDistance = distanceThreshold;
        
        // Check if target is in attack range
        context.inAttackRange = (context.heightDifference <= s_attackParams.heightThreshold);
        
        LogDebug("InitializeAttackContext - Context initialized", &context);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("InitializeAttackContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if target is in valid range
 */
bool DfAIMgr::IsTargetInValidRange(const AIAttackContext& context) {
    // Check height difference (equivalent to original v6 <= 200.0 check)
    if (context.heightDifference > s_attackParams.heightThreshold) {
        LogDebug("IsTargetInValidRange - Height difference too large: %.2f", context.heightDifference);
        return false;
    }
    
    LogDebug("IsTargetInValidRange - Target is in valid range");
    return true;
}

/**
 * Apply movement prediction to distance calculation
 */
void DfAIMgr::ApplyMovementPrediction(CMonster* pMonster, AIAttackContext& context) {
    try {
        // Check if target is moving and conditions are met (equivalent to original logic)
        bool shouldApplyPrediction = false;
        
        if (context.targetIsMoving) {
            // Get attack range (equivalent to original GetAttackRange call)
            float attackRange = CMonster::GetAttackRange(pMonster);
            if (context.targetDistance < DEFAULT_CLOSE_RANGE) {
                shouldApplyPrediction = true;
            }
        } else if (context.adjustedDistance < DEFAULT_CLOSE_RANGE) {
            shouldApplyPrediction = true;
        }
        
        if (shouldApplyPrediction) {
            // Apply movement prediction (equivalent to original calculation)
            float loopTime = R3GetLoopTime() * s_attackParams.skillDelayMultiplier;
            float moveSpeed = CMonster::GetMoveSpeed(pMonster);
            
            float predictionAdjustment = (loopTime * loopTime) * s_attackParams.movementPrediction;
            context.adjustedDistance = context.adjustedDistance - predictionAdjustment;
            
            LogDebug("ApplyMovementPrediction - Applied prediction, adjusted distance: %.2f", 
                    context.adjustedDistance);
        }
        
    } catch (const std::exception& e) {
        Logger::Error("ApplyMovementPrediction - Exception: %s", e.what());
    }
}

/**
 * Evaluate skills for attack
 */
AIAttackResult DfAIMgr::EvaluateSkillsForAttack(CMonster* pMonster, const AIAttackContext& context) {
    try {
        LogDebug("EvaluateSkillsForAttack - Starting skill evaluation");
        
        // Iterate through monster skills (equivalent to original for loop)
        for (int skillIndex = 0; skillIndex < MAX_MONSTER_SKILLS; ++skillIndex) {
            CMonsterSkill* skill = CMonsterSkillPool::GetMonSkill(&pMonster->m_MonsterSkillPool, skillIndex);
            if (!skill) {
                continue; // Skip empty skill slots
            }
            
            // Evaluate this skill
            AISkillEvaluation evaluation;
            if (!EvaluateSkill(pMonster, skill, context, evaluation)) {
                continue; // Skip skills that can't be evaluated
            }
            
            // Check if skill meets attack conditions
            if (!SkillMeetsConditions(skill, evaluation)) {
                continue; // Skip skills that don't meet conditions
            }
            
            // Check timing conditions (equivalent to original timing checks)
            if (!CheckSkillTiming(evaluation, context)) {
                continue; // Skip skills with timing issues
            }
            
            // Execute attack with this skill
            AIAttackResult result = ExecuteAttack(pMonster, context.target, skill);
            if (result == AIAttackResult::AttackExecuted) {
                LogDebug("EvaluateSkillsForAttack - Attack executed with skill index: %d", skillIndex);
                return result;
            }
        }
        
        LogDebug("EvaluateSkillsForAttack - No suitable skills found for attack");
        return AIAttackResult::SkillNotReady;
        
    } catch (const std::exception& e) {
        Logger::Error("EvaluateSkillsForAttack - Exception: %s", e.what());
        return AIAttackResult::NoAttack;
    }
}

/**
 * Evaluate monster skill for attack
 */
bool DfAIMgr::EvaluateSkill(CMonster* pMonster, CMonsterSkill* skill,
                           const AIAttackContext& context, AISkillEvaluation& evaluation) {
    try {
        evaluation.skill = skill;
        
        // Check if skill exists and is valid (equivalent to original IsExit check)
        if (!CMonsterSkill::IsExit(skill)) {
            LogDebug("EvaluateSkill - Skill does not exist");
            return false;
        }
        
        // Check skill type and use type (equivalent to original type checks)
        if (CMonsterSkill::GetType(skill) != 0 || CMonsterSkill::GetUseType(skill) != 0) {
            LogDebug("EvaluateSkill - Skill type or use type not suitable for attack");
            return false;
        }
        
        // Get skill delay time
        evaluation.skillDelay = CMonster::GetSkillDelayTime(pMonster, skill);
        
        // Calculate time since last use (equivalent to original timing calculation)
        float currentTime = static_cast<float>(context.currentTime);
        uint32_t beforeTime = CMonsterSkill::GetBeforeTime(skill);
        evaluation.timeSinceLastUse = currentTime - static_cast<float>(beforeTime);
        
        // Get attack distance for skill
        evaluation.attackDistance = CMonsterSkill::GetAttackDist(skill);
        
        // Determine if skill is ready
        evaluation.isReady = (evaluation.timeSinceLastUse >= evaluation.skillDelay);
        
        LogDebug("EvaluateSkill - Skill evaluated: delay=%.2f, timeSince=%.2f, ready=%s",
                evaluation.skillDelay, evaluation.timeSinceLastUse, 
                evaluation.isReady ? "true" : "false");
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("EvaluateSkill - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if skill meets attack conditions
 */
bool DfAIMgr::SkillMeetsConditions(CMonsterSkill* skill, const AISkillEvaluation& evaluation) {
    // Basic condition: skill must be ready
    if (!evaluation.isReady) {
        LogDebug("SkillMeetsConditions - Skill not ready");
        return false;
    }
    
    // Additional conditions can be added here
    // For example: mana cost, cooldowns, special requirements, etc.
    
    LogDebug("SkillMeetsConditions - All conditions met");
    return true;
}

/**
 * Check skill timing conditions
 */
bool DfAIMgr::CheckSkillTiming(const AISkillEvaluation& evaluation, const AIAttackContext& context) {
    // Check if enough time has passed since last use (equivalent to original v10 >= v17 check)
    if (evaluation.timeSinceLastUse < evaluation.skillDelay) {
        LogDebug("CheckSkillTiming - Skill delay not satisfied: %.2f < %.2f",
                evaluation.timeSinceLastUse, evaluation.skillDelay);
        return false;
    }
    
    // Check if timing meets distance requirements (equivalent to original v10 >= v13 check)
    if (evaluation.timeSinceLastUse < context.adjustedDistance) {
        LogDebug("CheckSkillTiming - Distance timing not satisfied: %.2f < %.2f",
                evaluation.timeSinceLastUse, context.adjustedDistance);
        return false;
    }
    
    LogDebug("CheckSkillTiming - Timing conditions satisfied");
    return true;
}

/**
 * Execute attack with skill
 */
AIAttackResult DfAIMgr::ExecuteAttack(CMonster* pMonster, CCharacter* target, CMonsterSkill* skill) {
    try {
        LogDebug("ExecuteAttack - Executing attack");
        
        // Execute attack (equivalent to original CMonster::Attack call)
        int attackResult = CMonster::Attack(pMonster, target, skill);
        
        if (attackResult != 0) {
            LogDebug("ExecuteAttack - Attack executed successfully");
            s_successfulAttacks++;
            return AIAttackResult::AttackExecuted;
        } else {
            LogDebug("ExecuteAttack - Attack execution failed");
            s_failedAttacks++;
            return AIAttackResult::NoAttack;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("ExecuteAttack - Exception: %s", e.what());
        s_failedAttacks++;
        return AIAttackResult::NoAttack;
    }
}



/**
 * Check skill timing conditions
 */
bool DfAIMgr::CheckSkillTiming(const AISkillEvaluation& evaluation, const AIAttackContext& context) {
    // Check if enough time has passed since last use (equivalent to original v10 >= v17 check)
    if (evaluation.timeSinceLastUse < evaluation.skillDelay) {
        LogDebug("CheckSkillTiming - Skill delay not satisfied: %.2f < %.2f",
                evaluation.timeSinceLastUse, evaluation.skillDelay);
        return false;
    }

    // Check if timing meets distance requirements (equivalent to original v10 >= v13 check)
    if (evaluation.timeSinceLastUse < context.adjustedDistance) {
        LogDebug("CheckSkillTiming - Distance timing not satisfied: %.2f < %.2f",
                evaluation.timeSinceLastUse, context.adjustedDistance);
        return false;
    }

    LogDebug("CheckSkillTiming - Timing conditions satisfied");
    return true;
}
