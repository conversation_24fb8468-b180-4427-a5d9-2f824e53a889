/**
 * @file MonsterAICreator.cpp
 * @brief Implementation of Monster AI Creation System
 * 
 * This file contains the implementation of the MonsterAICreator class,
 * refactored from decompiled C source files to modern C++20.
 * 
 * Original file refactored:
 * - CreateAICMonsterQEAAHHZ_1401423D0.c (Address: 0x1401423D0)
 * 
 * The original function signature was:
 * __int64 __fastcall CMonster::CreateAI(CMonster *this, int nType)
 */

#include "../Headers/MonsterAICreator.h"
#include "../Headers/CMonster.h"
#include "../Headers/CRFMonsterAIMgr.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cassert>

// Forward declarations for external dependencies
// These would be replaced with actual headers in a real implementation
class UsStateTBL {
public:
    int SetHFSM(Us_HFSM* hfsm, CMonster* monster) { return 1; }
};

class Us_HFSM {
public:
    void* vfptr = nullptr;
};

template<typename T>
class UsPoint {
public:
    UsPoint() : ptr(nullptr) {}
    ~UsPoint() { /* cleanup */ }
    T* operator->() { return ptr; }
    operator T*() { return ptr; }
    bool operator!() const { return ptr == nullptr; }
private:
    T* ptr;
};

MonsterAICreator::AICreationResult MonsterAICreator::CreateAI(CMonster* monster, AIType aiType) {
    AICreationConfig config(aiType);
    return CreateAI(monster, config);
}

MonsterAICreator::AICreationResult MonsterAICreator::CreateAI(CMonster* monster, const AICreationConfig& config) {
    if (!monster) {
        std::cerr << "[ERROR] MonsterAICreator::CreateAI - Invalid monster pointer" << std::endl;
        UpdateStats(AICreationResult::InvalidMonster, config.aiType);
        return AICreationResult::InvalidMonster;
    }

    if (!IsValidAIType(config.aiType)) {
        std::cerr << "[ERROR] MonsterAICreator::CreateAI - Invalid AI type: " 
                  << static_cast<int32_t>(config.aiType) << std::endl;
        UpdateStats(AICreationResult::InvalidType, config.aiType);
        return AICreationResult::InvalidType;
    }

    try {
        // Equivalent to original stack initialization with debug pattern
        // Original: for ( i = 24i64; i; --i ) { *(_DWORD *)v2 = -858993460; }
        std::cout << "[DEBUG] Starting AI creation for monster, type: " 
                  << static_cast<int32_t>(config.aiType) << std::endl;

        // Get AI manager instance
        // Original: v7 = CRFMonsterAIMgr::Instance();
        CRFMonsterAIMgr* aiManager = GetAIManager();
        if (!aiManager) {
            std::cerr << "[ERROR] AI Manager not available" << std::endl;
            UpdateStats(AICreationResult::ManagerNotAvailable, config.aiType);
            
            if (config.enableFallback) {
                return ApplyFallback(monster, config);
            }
            return AICreationResult::ManagerNotAvailable;
        }

        // Get state table for the AI type
        // Original: CRFMonsterAIMgr::GetStateTBL(v7, &result, nIndex);
        UsStateTBL* stateTable = GetStateTable(aiManager, config.aiType);
        if (!stateTable) {
            std::cerr << "[ERROR] State table not found for AI type: " 
                      << static_cast<int32_t>(config.aiType) << std::endl;
            UpdateStats(AICreationResult::StateTableNotFound, config.aiType);
            
            if (config.enableFallback) {
                return ApplyFallback(monster, config);
            }
            return AICreationResult::StateTableNotFound;
        }

        // Setup HFSM (Hierarchical Finite State Machine)
        // Original: pHFSM = (Us_HFSM *)&pObject->m_AI.vfptr;
        //           v4 = UsPoint<UsStateTBL>::operator->(&result);
        //           v9 = UsStateTBL::SetHFSM(v4, pHFSM, pObject);
        if (!SetupHFSM(monster, stateTable)) {
            std::cerr << "[ERROR] HFSM setup failed" << std::endl;
            UpdateStats(AICreationResult::HFSMSetupFailed, config.aiType);
            
            if (config.enableFallback) {
                return ApplyFallback(monster, config);
            }
            return AICreationResult::HFSMSetupFailed;
        }

        // AI creation successful
        std::cout << "[DEBUG] AI creation successful for type: " 
                  << static_cast<int32_t>(config.aiType) << std::endl;
        
        UpdateStats(AICreationResult::Success, config.aiType);
        LogCreationAttempt(monster, config.aiType, AICreationResult::Success);
        
        return AICreationResult::Success;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] MonsterAICreator::CreateAI - Exception: " << e.what() << std::endl;
        UpdateStats(AICreationResult::Failed, config.aiType);
        
        if (config.enableFallback) {
            return ApplyFallback(monster, config);
        }
        return AICreationResult::Failed;
    }
}

bool MonsterAICreator::IsValidAIType(AIType aiType) {
    return aiType >= AIType::None && aiType <= AIType::Custom;
}

int32_t MonsterAICreator::AITypeToInt(AIType aiType) {
    return static_cast<int32_t>(aiType);
}

MonsterAICreator::AIType MonsterAICreator::IntToAIType(int32_t value) {
    if (value >= static_cast<int32_t>(AIType::None) && 
        value <= static_cast<int32_t>(AIType::Custom)) {
        return static_cast<AIType>(value);
    }
    return AIType::None;
}

void MonsterAICreator::ResetStats() {
    m_stats = AICreationStats();
    std::cout << "[DEBUG] AI creation statistics reset" << std::endl;
}

void MonsterAICreator::SetDefaultFallbackConfig(const AICreationConfig& config) {
    if (MonsterAICreatorUtils::ValidateAICreationConfig(config)) {
        m_defaultFallbackConfig = config;
        std::cout << "[DEBUG] Default fallback configuration updated" << std::endl;
    } else {
        std::cerr << "[ERROR] Invalid fallback configuration provided" << std::endl;
    }
}

bool MonsterAICreator::ValidateMonster(CMonster* monster) const {
    return monster != nullptr;
}

CRFMonsterAIMgr* MonsterAICreator::GetAIManager() const {
    // Original: v7 = CRFMonsterAIMgr::Instance();
    return CRFMonsterAIMgr::Instance();
}

UsStateTBL* MonsterAICreator::GetStateTable(CRFMonsterAIMgr* aiManager, AIType aiType) const {
    if (!aiManager) {
        return nullptr;
    }

    // Original: CRFMonsterAIMgr::GetStateTBL(v7, &result, nIndex);
    // This would call the actual GetStateTBL method
    // For now, we'll return a placeholder
    
    // In the real implementation, this would:
    // 1. Call aiManager->GetStateTBL() with the AI type
    // 2. Handle the UsPoint<UsStateTBL> result
    // 3. Return the actual state table pointer
    
    std::cout << "[DEBUG] Retrieving state table for AI type: " 
              << static_cast<int32_t>(aiType) << std::endl;
    
    // Placeholder - in real implementation this would return actual state table
    return nullptr; // This would be replaced with actual state table retrieval
}

bool MonsterAICreator::SetupHFSM(CMonster* monster, UsStateTBL* stateTable) const {
    if (!monster || !stateTable) {
        return false;
    }

    try {
        // Original: pHFSM = (Us_HFSM *)&pObject->m_AI.vfptr;
        //           v4 = UsPoint<UsStateTBL>::operator->(&result);
        //           v9 = UsStateTBL::SetHFSM(v4, pHFSM, pObject);
        
        // Get the HFSM from the monster's AI system
        // In the original code, this was a cast to the AI's virtual function pointer
        Us_HFSM* hfsm = reinterpret_cast<Us_HFSM*>(&monster->m_AI.vfptr);
        
        // Setup the HFSM with the state table
        int result = stateTable->SetHFSM(hfsm, monster);
        
        if (result > 0) {
            std::cout << "[DEBUG] HFSM setup successful" << std::endl;
            return true;
        } else {
            std::cerr << "[ERROR] HFSM setup failed with result: " << result << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception during HFSM setup: " << e.what() << std::endl;
        return false;
    }
}

MonsterAICreator::AICreationResult MonsterAICreator::ApplyFallback(CMonster* monster, const AICreationConfig& config) {
    if (!monster) {
        return AICreationResult::InvalidMonster;
    }

    try {
        std::cout << "[DEBUG] Applying fallback configuration for monster" << std::endl;
        
        // Original fallback behavior:
        // CMonster::SetEmotionState(pObject, 0);
        // CMonster::SetCombatState(pObject, 0);
        // return 0i64;
        
        if (config.resetEmotionState) {
            monster->SetEmotionState(0);
            std::cout << "[DEBUG] Emotion state reset to 0" << std::endl;
        }
        
        if (config.resetCombatState) {
            monster->SetCombatState(0);
            std::cout << "[DEBUG] Combat state reset to 0" << std::endl;
        }
        
        // Update statistics for fallback activation
        m_stats.fallbackActivations++;
        
        std::cout << "[DEBUG] Fallback configuration applied successfully" << std::endl;
        return AICreationResult::Success; // Fallback considered successful
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception during fallback application: " << e.what() << std::endl;
        return AICreationResult::Failed;
    }
}

void MonsterAICreator::UpdateStats(AICreationResult result, AIType aiType) {
    m_stats.totalCreationAttempts++;
    
    if (result == AICreationResult::Success) {
        m_stats.successfulCreations++;
    } else {
        m_stats.failedCreations++;
    }
    
    m_stats.typeUsageCount[aiType]++;
}

void MonsterAICreator::LogCreationAttempt(CMonster* monster, AIType aiType, AICreationResult result) const {
    std::cout << "[LOG] AI Creation - Monster: " << monster 
              << ", Type: " << static_cast<int32_t>(aiType)
              << ", Result: " << static_cast<int32_t>(result) << std::endl;
}

// Utility functions implementation
namespace MonsterAICreatorUtils {

std::string AICreationResultToString(MonsterAICreator::AICreationResult result) {
    switch (result) {
        case MonsterAICreator::AICreationResult::Success: return "Success";
        case MonsterAICreator::AICreationResult::Failed: return "Failed";
        case MonsterAICreator::AICreationResult::InvalidType: return "InvalidType";
        case MonsterAICreator::AICreationResult::InvalidMonster: return "InvalidMonster";
        case MonsterAICreator::AICreationResult::ManagerNotAvailable: return "ManagerNotAvailable";
        case MonsterAICreator::AICreationResult::StateTableNotFound: return "StateTableNotFound";
        case MonsterAICreator::AICreationResult::HFSMSetupFailed: return "HFSMSetupFailed";
        default: return "Unknown";
    }
}

std::string AITypeToString(MonsterAICreator::AIType aiType) {
    switch (aiType) {
        case MonsterAICreator::AIType::None: return "None";
        case MonsterAICreator::AIType::Basic: return "Basic";
        case MonsterAICreator::AIType::Aggressive: return "Aggressive";
        case MonsterAICreator::AIType::Defensive: return "Defensive";
        case MonsterAICreator::AIType::Patrol: return "Patrol";
        case MonsterAICreator::AIType::Boss: return "Boss";
        case MonsterAICreator::AIType::Elite: return "Elite";
        case MonsterAICreator::AIType::Support: return "Support";
        case MonsterAICreator::AIType::Custom: return "Custom";
        default: return "Unknown";
    }
}

MonsterAICreator::AIType GetRecommendedAIType(CMonster* monster) {
    if (!monster) {
        return MonsterAICreator::AIType::None;
    }
    
    // This would analyze monster properties to recommend appropriate AI type
    // For now, return Basic as default
    return MonsterAICreator::AIType::Basic;
}

bool ValidateAICreationConfig(const MonsterAICreator::AICreationConfig& config) {
    return MonsterAICreator::IsValidAIType(config.aiType) && 
           config.timeoutMs > 0 && config.timeoutMs <= 60000; // Max 1 minute timeout
}

std::string FormatAICreationStats(const MonsterAICreator::AICreationStats& stats) {
    std::ostringstream oss;
    oss << "AICreationStats[Total:" << stats.totalCreationAttempts
        << ", Success:" << stats.successfulCreations
        << ", Failed:" << stats.failedCreations
        << ", Fallbacks:" << stats.fallbackActivations
        << ", SuccessRate:" << (stats.GetSuccessRate() * 100.0) << "%]";
    return oss.str();
}

MonsterAICreator::AICreationConfig CreateDefaultConfigForMonsterType(int32_t monsterType) {
    MonsterAICreator::AICreationConfig config;
    
    switch (monsterType) {
        case 1: // Normal monster
            config.aiType = MonsterAICreator::AIType::Basic;
            break;
        case 2: // Aggressive monster
            config.aiType = MonsterAICreator::AIType::Aggressive;
            break;
        case 3: // Boss monster
            config.aiType = MonsterAICreator::AIType::Boss;
            break;
        default:
            config.aiType = MonsterAICreator::AIType::Basic;
            break;
    }
    
    return config;
}

} // namespace MonsterAICreatorUtils
