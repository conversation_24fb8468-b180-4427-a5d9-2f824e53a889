/**
 * @file MonsterAICreator_Test_Example.cpp
 * @brief Test examples for the refactored MonsterAICreator class
 * 
 * This file demonstrates how to use the MonsterAICreator class and provides
 * test cases to verify the functionality works correctly.
 * 
 * @note This is a documentation file showing usage examples.
 *       In a real implementation, these would be proper unit tests.
 */

#include "../Headers/MonsterAICreator.h"
#include "../Headers/CMonster.h"
#include <iostream>
#include <cassert>
#include <vector>

/**
 * @brief Example of basic MonsterAICreator usage
 */
void ExampleBasicUsage() {
    std::cout << "\n=== Basic MonsterAICreator Usage Example ===" << std::endl;
    
    // Create a monster (placeholder - would use actual monster creation)
    CMonster* monster = nullptr; // In real code: new CMonster();
    
    // Create AI creator
    MonsterAICreator aiCreator;
    
    // Create basic AI
    auto result = aiCreator.CreateAI(monster, MonsterAICreator::AIType::Basic);
    
    std::cout << "AI Creation Result: " 
              << MonsterAICreatorUtils::AICreationResultToString(result) << std::endl;
    
    // Get statistics
    const auto& stats = aiCreator.GetStats();
    std::cout << "Creation Statistics: " 
              << MonsterAICreatorUtils::FormatAICreationStats(stats) << std::endl;
    
    // Clean up (in real code)
    // delete monster;
}

/**
 * @brief Example of AI creation with configuration
 */
void ExampleConfiguredAICreation() {
    std::cout << "\n=== Configured AI Creation Example ===" << std::endl;
    
    CMonster* monster = nullptr; // Placeholder
    MonsterAICreator aiCreator;
    
    // Create configuration for aggressive AI
    MonsterAICreator::AICreationConfig config;
    config.aiType = MonsterAICreator::AIType::Aggressive;
    config.enableFallback = true;
    config.resetEmotionState = true;
    config.resetCombatState = true;
    config.timeoutMs = 3000; // 3 second timeout
    
    std::cout << "Creating AI with configuration:" << std::endl;
    std::cout << "  Type: " << MonsterAICreatorUtils::AITypeToString(config.aiType) << std::endl;
    std::cout << "  Fallback: " << (config.enableFallback ? "Enabled" : "Disabled") << std::endl;
    std::cout << "  Timeout: " << config.timeoutMs << "ms" << std::endl;
    
    // Create AI with configuration
    auto result = aiCreator.CreateAI(monster, config);
    
    std::cout << "Result: " 
              << MonsterAICreatorUtils::AICreationResultToString(result) << std::endl;
}

/**
 * @brief Example of different AI types
 */
void ExampleDifferentAITypes() {
    std::cout << "\n=== Different AI Types Example ===" << std::endl;
    
    CMonster* monster = nullptr; // Placeholder
    MonsterAICreator aiCreator;
    
    // Test different AI types
    std::vector<MonsterAICreator::AIType> aiTypes = {
        MonsterAICreator::AIType::Basic,
        MonsterAICreator::AIType::Aggressive,
        MonsterAICreator::AIType::Defensive,
        MonsterAICreator::AIType::Patrol,
        MonsterAICreator::AIType::Boss,
        MonsterAICreator::AIType::Elite,
        MonsterAICreator::AIType::Support
    };
    
    for (auto aiType : aiTypes) {
        std::cout << "Testing AI Type: " 
                  << MonsterAICreatorUtils::AITypeToString(aiType) << std::endl;
        
        auto result = aiCreator.CreateAI(monster, aiType);
        std::cout << "  Result: " 
                  << MonsterAICreatorUtils::AICreationResultToString(result) << std::endl;
    }
    
    // Display final statistics
    const auto& stats = aiCreator.GetStats();
    std::cout << "\nFinal Statistics:" << std::endl;
    std::cout << "  Total Attempts: " << stats.totalCreationAttempts << std::endl;
    std::cout << "  Successful: " << stats.successfulCreations << std::endl;
    std::cout << "  Failed: " << stats.failedCreations << std::endl;
    std::cout << "  Success Rate: " << (stats.GetSuccessRate() * 100.0) << "%" << std::endl;
}

/**
 * @brief Example of fallback configuration
 */
void ExampleFallbackConfiguration() {
    std::cout << "\n=== Fallback Configuration Example ===" << std::endl;
    
    MonsterAICreator aiCreator;
    
    // Create custom fallback configuration
    MonsterAICreator::AICreationConfig fallbackConfig;
    fallbackConfig.aiType = MonsterAICreator::AIType::Basic;
    fallbackConfig.enableFallback = true;
    fallbackConfig.resetEmotionState = true;
    fallbackConfig.resetCombatState = false; // Don't reset combat state
    fallbackConfig.timeoutMs = 1000; // Short timeout
    
    // Set as default fallback
    aiCreator.SetDefaultFallbackConfig(fallbackConfig);
    
    std::cout << "Default fallback configuration set:" << std::endl;
    const auto& config = aiCreator.GetDefaultFallbackConfig();
    std::cout << "  Type: " << MonsterAICreatorUtils::AITypeToString(config.aiType) << std::endl;
    std::cout << "  Reset Emotion: " << (config.resetEmotionState ? "Yes" : "No") << std::endl;
    std::cout << "  Reset Combat: " << (config.resetCombatState ? "Yes" : "No") << std::endl;
    
    // Test fallback with invalid monster (should trigger fallback)
    CMonster* invalidMonster = nullptr;
    auto result = aiCreator.CreateAI(invalidMonster, MonsterAICreator::AIType::Boss);
    
    std::cout << "Creation with invalid monster: " 
              << MonsterAICreatorUtils::AICreationResultToString(result) << std::endl;
    
    // Check if fallback was activated
    const auto& stats = aiCreator.GetStats();
    std::cout << "Fallback activations: " << stats.fallbackActivations << std::endl;
}

/**
 * @brief Example of AI type validation
 */
void ExampleAITypeValidation() {
    std::cout << "\n=== AI Type Validation Example ===" << std::endl;
    
    // Test valid AI types
    std::vector<MonsterAICreator::AIType> validTypes = {
        MonsterAICreator::AIType::None,
        MonsterAICreator::AIType::Basic,
        MonsterAICreator::AIType::Custom
    };
    
    std::cout << "Valid AI Types:" << std::endl;
    for (auto aiType : validTypes) {
        bool isValid = MonsterAICreator::IsValidAIType(aiType);
        std::cout << "  " << MonsterAICreatorUtils::AITypeToString(aiType) 
                  << ": " << (isValid ? "Valid" : "Invalid") << std::endl;
    }
    
    // Test AI type conversion
    std::cout << "\nAI Type Conversion:" << std::endl;
    for (int i = 0; i <= 10; ++i) {
        auto aiType = MonsterAICreator::IntToAIType(i);
        int backToInt = MonsterAICreator::AITypeToInt(aiType);
        
        std::cout << "  " << i << " -> " 
                  << MonsterAICreatorUtils::AITypeToString(aiType) 
                  << " -> " << backToInt << std::endl;
    }
}

/**
 * @brief Example of configuration validation
 */
void ExampleConfigurationValidation() {
    std::cout << "\n=== Configuration Validation Example ===" << std::endl;
    
    // Test valid configurations
    std::vector<MonsterAICreator::AICreationConfig> configs = {
        {MonsterAICreator::AIType::Basic, true},     // Valid basic config
        {MonsterAICreator::AIType::Boss, false},     // Valid boss config without fallback
        {MonsterAICreator::AIType::Custom, true}     // Valid custom config
    };
    
    // Modify timeout values for testing
    configs[0].timeoutMs = 5000;   // Valid timeout
    configs[1].timeoutMs = 100000; // Invalid timeout (too long)
    configs[2].timeoutMs = 0;      // Invalid timeout (zero)
    
    std::cout << "Configuration Validation Results:" << std::endl;
    for (size_t i = 0; i < configs.size(); ++i) {
        bool isValid = MonsterAICreatorUtils::ValidateAICreationConfig(configs[i]);
        
        std::cout << "  Config " << (i + 1) << ": " << (isValid ? "Valid" : "Invalid") << std::endl;
        std::cout << "    Type: " << MonsterAICreatorUtils::AITypeToString(configs[i].aiType) << std::endl;
        std::cout << "    Timeout: " << configs[i].timeoutMs << "ms" << std::endl;
        std::cout << "    Fallback: " << (configs[i].enableFallback ? "Enabled" : "Disabled") << std::endl;
    }
}

/**
 * @brief Example of recommended AI type selection
 */
void ExampleRecommendedAIType() {
    std::cout << "\n=== Recommended AI Type Example ===" << std::endl;
    
    // Test with different monster scenarios
    std::vector<CMonster*> monsters = {
        nullptr,  // Invalid monster
        // In real implementation, these would be actual monster instances
        // with different properties that would influence AI type selection
    };
    
    for (size_t i = 0; i < monsters.size(); ++i) {
        auto recommendedType = MonsterAICreatorUtils::GetRecommendedAIType(monsters[i]);
        
        std::cout << "Monster " << (i + 1) << " recommended AI type: " 
                  << MonsterAICreatorUtils::AITypeToString(recommendedType) << std::endl;
    }
    
    // Test default configurations for different monster types
    std::cout << "\nDefault configurations for monster types:" << std::endl;
    for (int monsterType = 1; monsterType <= 5; ++monsterType) {
        auto config = MonsterAICreatorUtils::CreateDefaultConfigForMonsterType(monsterType);
        
        std::cout << "  Monster Type " << monsterType << ": " 
                  << MonsterAICreatorUtils::AITypeToString(config.aiType) << std::endl;
    }
}

/**
 * @brief Example of error handling
 */
void ExampleErrorHandling() {
    std::cout << "\n=== Error Handling Example ===" << std::endl;
    
    MonsterAICreator aiCreator;
    
    // Test various error conditions
    std::cout << "Testing error conditions:" << std::endl;
    
    // Invalid monster
    auto result1 = aiCreator.CreateAI(nullptr, MonsterAICreator::AIType::Basic);
    std::cout << "  Null monster: " 
              << MonsterAICreatorUtils::AICreationResultToString(result1) << std::endl;
    
    // Invalid AI type (using direct cast to test boundary)
    auto invalidType = static_cast<MonsterAICreator::AIType>(999);
    CMonster* monster = nullptr; // Placeholder
    auto result2 = aiCreator.CreateAI(monster, invalidType);
    std::cout << "  Invalid AI type: " 
              << MonsterAICreatorUtils::AICreationResultToString(result2) << std::endl;
    
    // Test with invalid configuration
    MonsterAICreator::AICreationConfig invalidConfig;
    invalidConfig.aiType = MonsterAICreator::AIType::Basic;
    invalidConfig.timeoutMs = 0; // Invalid timeout
    
    auto result3 = aiCreator.CreateAI(monster, invalidConfig);
    std::cout << "  Invalid config: " 
              << MonsterAICreatorUtils::AICreationResultToString(result3) << std::endl;
    
    // Display error statistics
    const auto& stats = aiCreator.GetStats();
    std::cout << "\nError Statistics:" << std::endl;
    std::cout << "  Total attempts: " << stats.totalCreationAttempts << std::endl;
    std::cout << "  Failed attempts: " << stats.failedCreations << std::endl;
    std::cout << "  Failure rate: " << ((1.0 - stats.GetSuccessRate()) * 100.0) << "%" << std::endl;
}

/**
 * @brief Main function for testing (if this were a standalone test)
 * @note This main function is commented out since this is a documentation file
 */
/*
int main() {
    std::cout << "MonsterAICreator Refactoring Test Examples" << std::endl;
    std::cout << "===========================================" << std::endl;
    
    try {
        ExampleBasicUsage();
        ExampleConfiguredAICreation();
        ExampleDifferentAITypes();
        ExampleFallbackConfiguration();
        ExampleAITypeValidation();
        ExampleConfigurationValidation();
        ExampleRecommendedAIType();
        ExampleErrorHandling();
        
        std::cout << "\n✓ All examples completed successfully!" << std::endl;
        std::cout << "The refactored MonsterAICreator class is working correctly." << std::endl;
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Error during testing: " << e.what() << std::endl;
        return 1;
    }
}
*/
