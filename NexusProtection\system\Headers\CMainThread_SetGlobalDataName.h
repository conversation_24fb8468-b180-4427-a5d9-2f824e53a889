/**
 * @file CMainThread_SetGlobalDataName.h
 * @brief Global data name assignment system for CMainThread
 * 
 * Refactored from SetGlobalDataName function
 * This handles the assignment of global data names and identifiers
 * used throughout the server for data table references and naming conventions.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>

// Forward declarations
class CMainThread;
class CRecordData;

/**
 * @enum GlobalDataNameType
 * @brief Enumeration of all global data name types
 */
enum class GlobalDataNameType : uint32_t {
    // Core Data Names
    ItemDataName = 0,
    SkillDataName,
    ForceDataName,
    ClassSkillDataName,
    BulletItemEffectDataName,
    
    // Character Data Names
    ClassDataName,
    GradeDataName,
    PlayerCharacterDataName,
    MonsterCharacterDataName,
    NPCharacterDataName,
    
    // Item System Names
    AnimusItemDataName,
    ExpDataName,
    ItemLootingDataName,
    OreCuttingDataName,
    ItemMakeDataName,
    ItemCombineDataName,
    ItemExchangeDataName,
    ItemUpgradeDataName,
    
    // Unit Data Names
    UnitHeadDataName,
    UnitUpperDataName,
    UnitLowerDataName,
    UnitArmsDataName,
    UnitShoulderDataName,
    UnitBackDataName,
    UnitBulletDataName,
    UnitFrameDataName,
    
    // System Data Names
    EditDataName,
    MonsterCharacterAIDataName,
    MobMessageDataName,
    
    // Manager System Names
    PotionManagerName,
    QuestManagerName,
    ItemCombineManagerName,
    PcBangSystemName,
    SUItemSystemName,
    MonsterSPGroupTableName,
    
    // Configuration Names
    AggroCalculateConfigName,
    MonsterSetConfigName,
    
    // Special Global Names
    ServerInstanceName,
    DatabaseConnectionName,
    NetworkConfigName,
    SecurityConfigName,
    LoggingConfigName,
    
    MAX_GLOBAL_DATA_NAMES
};

/**
 * @enum GlobalDataNameResult
 * @brief Result codes for global data name operations
 */
enum class GlobalDataNameResult : int32_t {
    Success = 1,
    Failure = 0,
    InvalidName = -1,
    DuplicateName = -2,
    NameTooLong = -3,
    InvalidCharacters = -4,
    SystemError = -5,
    SecurityError = -6
};

/**
 * @struct GlobalDataNameInfo
 * @brief Information about a global data name
 */
struct GlobalDataNameInfo {
    std::string name;
    std::string description;
    std::string defaultValue;
    bool isRequired;
    size_t maxLength;
    std::function<bool(const std::string&)> validator;
    
    GlobalDataNameInfo() = default;
    GlobalDataNameInfo(const std::string& n, const std::string& desc, 
                      const std::string& defaultVal = "", bool required = true, 
                      size_t maxLen = 256)
        : name(n), description(desc), defaultValue(defaultVal), 
          isRequired(required), maxLength(maxLen) {}
};

/**
 * @struct GlobalDataNameStats
 * @brief Statistics for global data name assignment process
 */
struct GlobalDataNameStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(GlobalDataNameType::MAX_GLOBAL_DATA_NAMES)> nameStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(GlobalDataNameType::MAX_GLOBAL_DATA_NAMES)> assignmentTime;
    std::array<std::string, static_cast<size_t>(GlobalDataNameType::MAX_GLOBAL_DATA_NAMES)> assignedNames;
    uint32_t successfulAssignments;
    uint32_t failedAssignments;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        nameStatus.fill(false);
        assignmentTime.fill(std::chrono::milliseconds::zero());
        assignedNames.fill("");
        successfulAssignments = 0;
        failedAssignments = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalAssignmentTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulAssignments + failedAssignments;
        return total > 0 ? (static_cast<double>(successfulAssignments) / total) * 100.0 : 0.0;
    }
    
    uint32_t GetTotalAssignments() const {
        return successfulAssignments + failedAssignments;
    }
};

/**
 * @class CMainThreadSetGlobalDataName
 * @brief Global data name assignment and management system
 * 
 * This class handles the assignment of global data names and identifiers
 * used throughout the server. It provides modern C++20 interfaces while 
 * maintaining compatibility with the original naming logic.
 * 
 * Original function: CMainThread::SetGlobalDataName
 * Refactored to modern C++20 with comprehensive name management and validation.
 */
class CMainThreadSetGlobalDataName {
public:
    /**
     * @brief Constructor
     */
    CMainThreadSetGlobalDataName();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadSetGlobalDataName();
    
    // Delete copy constructor and assignment operator
    CMainThreadSetGlobalDataName(const CMainThreadSetGlobalDataName&) = delete;
    CMainThreadSetGlobalDataName& operator=(const CMainThreadSetGlobalDataName&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadSetGlobalDataName(CMainThreadSetGlobalDataName&&) = default;
    CMainThreadSetGlobalDataName& operator=(CMainThreadSetGlobalDataName&&) = default;
    
    /**
     * @brief Main global data name assignment function
     * 
     * Assigns all global data names with comprehensive validation and error handling.
     * This is the modern refactored version of the original SetGlobalDataName function.
     * 
     * @param mainThread Pointer to CMainThread instance for accessing data
     * @return GlobalDataNameResult indicating success or failure
     */
    GlobalDataNameResult AssignAllGlobalDataNames(CMainThread* mainThread);
    
    /**
     * @brief Legacy SetGlobalDataName function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: bool __fastcall CMainThread::SetGlobalDataName(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return bool (true for success, false for failure)
     */
    static bool SetGlobalDataName_Legacy(CMainThread* mainThread);
    
    /**
     * @brief Get global data name assignment statistics
     * @return const reference to assignment statistics
     */
    const GlobalDataNameStats& GetAssignmentStats() const;
    
    /**
     * @brief Check if a specific data name is assigned
     * @param nameType The global data name type to check
     * @return true if assigned, false otherwise
     */
    bool IsDataNameAssigned(GlobalDataNameType nameType) const;
    
    /**
     * @brief Get the assigned name for a specific data type
     * @param nameType The global data name type
     * @return string containing the assigned name
     */
    std::string GetAssignedName(GlobalDataNameType nameType) const;
    
    /**
     * @brief Get the last assignment error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get information about a specific name type
     * @param nameType The name type
     * @return GlobalDataNameInfo structure with name information
     */
    static GlobalDataNameInfo GetNameInfo(GlobalDataNameType nameType);
    
    /**
     * @brief Validate all required names are assigned
     * @return true if all required names are assigned
     */
    bool ValidateRequiredNames() const;
    
    /**
     * @brief Assign a specific global data name
     * @param nameType The type of name to assign
     * @param name The name to assign
     * @return GlobalDataNameResult indicating success or failure
     */
    GlobalDataNameResult AssignGlobalDataName(GlobalDataNameType nameType, const std::string& name);
    
    /**
     * @brief Get all assigned global data names
     * @return unordered_map of name types to assigned names
     */
    std::unordered_map<GlobalDataNameType, std::string> GetAllAssignedNames() const;

private:
    // Assignment statistics and monitoring
    GlobalDataNameStats m_assignmentStats;
    mutable std::mutex m_statsMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // Name registry
    static std::unordered_map<GlobalDataNameType, GlobalDataNameInfo> s_nameRegistry;
    
    // Assigned names storage
    std::unordered_map<GlobalDataNameType, std::string> m_assignedNames;
    mutable std::mutex m_namesMutex;
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Core assignment phases
    GlobalDataNameResult AssignCoreDataNames(CMainThread* mainThread);
    GlobalDataNameResult AssignCharacterDataNames(CMainThread* mainThread);
    GlobalDataNameResult AssignItemSystemNames(CMainThread* mainThread);
    GlobalDataNameResult AssignUnitDataNames(CMainThread* mainThread);
    GlobalDataNameResult AssignSystemDataNames(CMainThread* mainThread);
    GlobalDataNameResult AssignManagerSystemNames(CMainThread* mainThread);
    GlobalDataNameResult AssignConfigurationNames(CMainThread* mainThread);
    GlobalDataNameResult AssignSpecialGlobalNames(CMainThread* mainThread);
    
    // Individual assignment functions
    GlobalDataNameResult AssignDataTableName(GlobalDataNameType nameType, const std::string& tableName);
    GlobalDataNameResult AssignManagerName(GlobalDataNameType nameType, const std::string& managerName);
    GlobalDataNameResult AssignConfigName(GlobalDataNameType nameType, const std::string& configName);
    GlobalDataNameResult AssignSystemName(GlobalDataNameType nameType, const std::string& systemName);
    
    // Validation functions
    bool ValidateNameFormat(const std::string& name);
    bool ValidateNameLength(const std::string& name, size_t maxLength);
    bool ValidateNameCharacters(const std::string& name);
    bool ValidateNameUniqueness(const std::string& name, GlobalDataNameType excludeType);
    
    // Utility functions
    void LogAssignment(GlobalDataNameType nameType, bool success, const std::string& name = "", 
                      const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    std::string GenerateDefaultName(GlobalDataNameType nameType);
    std::string SanitizeName(const std::string& name);
    
    // Static initialization
    static void InitializeNameRegistry();
    
    // Legacy compatibility functions
    static bool AssignDataTableName_Legacy(const char* tableName, const char* nameType);
    static bool AssignManagerName_Legacy(const char* managerName, const char* nameType);
};

/**
 * @brief Convert GlobalDataNameType enum to string for logging
 * @param nameType The name type
 * @return String representation of the name type
 */
std::string GlobalDataNameTypeToString(GlobalDataNameType nameType);

/**
 * @brief Convert GlobalDataNameResult enum to string for logging
 * @param result The assignment result
 * @return String representation of the result
 */
std::string GlobalDataNameResultToString(GlobalDataNameResult result);

// External global name storage
extern std::unordered_map<std::string, std::string> g_GlobalDataNames;
extern std::mutex g_GlobalDataNamesMutex;
