# CMainThread SetGlobalDataName System Refactoring

## Overview

This document describes the refactoring of the `CMainThread::SetGlobalDataName` function from a simple boolean check into a comprehensive global data name assignment and management system. The refactored system provides modern C++20 interfaces while maintaining full backward compatibility.

## Original Function

The original `SetGlobalDataName` function was extremely simple:
```cpp
bool __fastcall CMainThread::SetGlobalDataName(CMainThread *this)
{
    return true;  // Always returned true
}
```

The actual global data name assignment was handled implicitly by other systems during the data loading process.

## Refactored Architecture

### Core Components

1. **CMainThreadSetGlobalDataName Class** - Main name assignment system
2. **GlobalDataNameType Enum** - 42 different name types
3. **GlobalDataNameResult Enum** - Comprehensive result codes
4. **GlobalDataNameStats Structure** - Real-time statistics tracking
5. **Name Registry** - Metadata for all name types

### Key Features

- **Comprehensive Name Assignment**: Manages 42 different global data name types
- **Real-time Statistics**: Tracks assignment time, success rates, and name counts
- **Name Validation**: Format, length, character, and uniqueness validation
- **Error Handling**: Detailed error messages and exception handling
- **Security**: Stack protection with security cookies
- **Backward Compatibility**: Multiple compatibility layers for existing code

## Global Data Name Types (42 Total)

### Core Data Names (5 types)
- ItemDataName - Core item database name
- SkillDataName - Skill database name
- ForceDataName - Force power database name
- ClassSkillDataName - Class-specific skill name
- BulletItemEffectDataName - Bullet and item effect name

### Character Data Names (5 types)
- ClassDataName - Character class name
- GradeDataName - Character grade name
- PlayerCharacterDataName - Player character name
- MonsterCharacterDataName - Monster character name
- NPCharacterDataName - NPC character name

### Item System Names (8 types)
- AnimusItemDataName - Animus item name
- ExpDataName - Experience data name
- ItemLootingDataName - Item looting name
- OreCuttingDataName - Ore cutting name
- ItemMakeDataName - Item crafting name
- ItemCombineDataName - Item combination name
- ItemExchangeDataName - Item exchange name
- ItemUpgradeDataName - Item upgrade name

### Unit Data Names (8 types)
- UnitHeadDataName - Head part name
- UnitUpperDataName - Upper body part name
- UnitLowerDataName - Lower body part name
- UnitArmsDataName - Arms part name
- UnitShoulderDataName - Shoulder part name
- UnitBackDataName - Back part name
- UnitBulletDataName - Bullet part name
- UnitFrameDataName - Frame part name

### System Data Names (3 types)
- EditDataName - Edit system name
- MonsterCharacterAIDataName - Monster AI name
- MobMessageDataName - Monster message name

### Manager System Names (6 types)
- PotionManagerName - Potion manager name
- QuestManagerName - Quest manager name
- ItemCombineManagerName - Item combine manager name
- PcBangSystemName - PC Bang system name (optional)
- SUItemSystemName - Special upgrade item system name (optional)
- MonsterSPGroupTableName - Monster special group table name (optional)

### Configuration Names (2 types)
- AggroCalculateConfigName - Aggro calculation config name
- MonsterSetConfigName - Monster set config name

### Special Global Names (5 types)
- ServerInstanceName - Server instance identifier
- DatabaseConnectionName - Database connection identifier
- NetworkConfigName - Network configuration identifier
- SecurityConfigName - Security configuration identifier
- LoggingConfigName - Logging configuration identifier

## Usage Examples

### Modern Interface
```cpp
CMainThreadSetGlobalDataName nameAssigner;
GlobalDataNameResult result = nameAssigner.AssignAllGlobalDataNames(mainThread);

if (result == GlobalDataNameResult::Success) {
    std::cout << "All global data names assigned successfully!" << std::endl;
    
    // Get statistics
    const auto& stats = nameAssigner.GetAssignmentStats();
    std::cout << "Assignment time: " << stats.GetTotalAssignmentTime().count() << "ms" << std::endl;
    std::cout << "Success rate: " << stats.GetSuccessRate() << "%" << std::endl;
} else {
    std::cout << "Name assignment failed: " << nameAssigner.GetLastError() << std::endl;
}
```

### Legacy Compatibility
```cpp
// Original function signature
bool success = CMainThreadSetGlobalDataName::SetGlobalDataName_Legacy(mainThread);

// Enhanced legacy wrapper
bool success = CMainThread_SetGlobalDataName_Enhanced(mainThread);

// Simple name assignment
bool success = CMainThread_SetGlobalDataName_Simple(mainThread);

// Comprehensive assignment with logging
bool success = CMainThread_SetGlobalDataName_Comprehensive(mainThread);
```

### Individual Name Assignment
```cpp
CMainThreadSetGlobalDataName nameAssigner;

// Assign specific name
GlobalDataNameResult result = nameAssigner.AssignGlobalDataName(
    GlobalDataNameType::ItemDataName, "ItemData");

// Check if name is assigned
bool assigned = nameAssigner.IsDataNameAssigned(GlobalDataNameType::SkillDataName);

// Get assigned name
std::string name = nameAssigner.GetAssignedName(GlobalDataNameType::ClassDataName);
```

## Name Assignment Process

### Phase 1: Core Data Names
Assigns names for fundamental game data including items, skills, forces, and effects.

### Phase 2: Character Data Names
Assigns names for all character-related data including classes, grades, players, monsters, and NPCs.

### Phase 3: Item System Names
Assigns names for all item-related systems including crafting, combining, upgrading, and looting.

### Phase 4: Unit Data Names
Assigns names for all unit part data for character customization and equipment.

### Phase 5: System Data Names
Assigns names for system-level data including edit data, AI data, and messaging.

### Phase 6: Manager System Names
Assigns names for all manager systems that control game mechanics.

### Phase 7: Configuration Names
Assigns names for configuration data for various game systems.

### Phase 8: Special Global Names
Assigns names for special global identifiers including server instance and configuration names.

## Name Validation

### Format Validation
- Names must start with a letter or underscore
- Names cannot be empty
- Names must follow identifier conventions

### Length Validation
- Maximum length limits per name type (128-256 characters)
- Configurable length limits in name registry

### Character Validation
- Alphanumeric characters allowed
- Underscores and hyphens allowed
- No special characters or spaces

### Uniqueness Validation
- No duplicate names across different types
- Case-sensitive uniqueness checking
- Configurable uniqueness rules

## Error Handling

### Result Codes
- **Success** (1) - Name assignment completed successfully
- **Failure** (0) - General assignment failure
- **InvalidName** (-1) - Name format is invalid
- **DuplicateName** (-2) - Name already exists
- **NameTooLong** (-3) - Name exceeds maximum length
- **InvalidCharacters** (-4) - Name contains invalid characters
- **SystemError** (-5) - Exception during assignment
- **SecurityError** (-6) - Security cookie verification failed

### Error Messages
Detailed error messages are provided for all assignment failures, including:
- Specific name type that failed
- Reason for failure
- Expected format or length requirements
- Exception details

## Performance Considerations

### Timing
- Individual assignment timing for each name type
- Total assignment time tracking
- Performance statistics and success rates

### Memory Usage
- Minimal memory overhead for name storage
- RAII principles for automatic cleanup
- Efficient string handling and storage

### Threading
- Thread-safe name assignment and retrieval
- Mutex protection for shared data
- Atomic operations where appropriate

## Security Features

### Stack Protection
- Security cookie verification (equivalent to original /GS protection)
- Stack corruption detection
- Buffer overflow protection

### Name Integrity
- Name format validation
- Character validation
- Uniqueness verification

## Global Name Storage

### Storage System
```cpp
// Global storage for all assigned names
extern std::unordered_map<std::string, std::string> g_GlobalDataNames;
extern std::mutex g_GlobalDataNamesMutex;

// Access functions
std::string GetGlobalDataName_Legacy(const std::string& nameType);
bool SetGlobalDataName_Legacy(const std::string& nameType, const std::string& name);
```

### Thread Safety
- Mutex-protected global storage
- Thread-safe access functions
- Concurrent read/write protection

## Migration Guide

### For Existing Code
1. **No Changes Required** - Legacy wrappers maintain exact compatibility
2. **Enhanced Assignment** - Use `CMainThread_SetGlobalDataName_Enhanced()` for better error handling
3. **Modern Interface** - Migrate to `CMainThreadSetGlobalDataName` class for full features

### Recommended Migration Path
1. Start with legacy wrappers for immediate compatibility
2. Gradually migrate to enhanced wrappers for better error handling
3. Eventually adopt modern interface for full feature access

## Testing Strategy

### Unit Tests
- Individual assignment function tests
- Name validation testing
- Error condition testing
- Performance benchmarking

### Integration Tests
- Full assignment process testing
- Compatibility testing with existing code
- Performance regression testing
- Security vulnerability testing

### Load Testing
- Large name set assignment
- Concurrent assignment testing
- Memory usage under load
- Performance under stress

## Future Enhancements

### Planned Features
- Dynamic name loading from configuration files
- Name aliasing and mapping
- Hierarchical name organization
- Name versioning and history

### Extensibility
- Plugin architecture for custom name types
- External name service integration
- Real-time name monitoring
- Name conflict resolution

## Compatibility Matrix

| Interface | Original Behavior | Enhanced Features | Performance | Recommended Use |
|-----------|------------------|-------------------|-------------|-----------------|
| Legacy Wrapper | ✅ Exact | ❌ None | ⚡ Fast | Existing code |
| Enhanced Wrapper | ✅ Compatible | ✅ Error handling | ⚡ Fast | Migration |
| Simple Wrapper | ✅ Compatible | ✅ Basic assignment | ⚡ Fastest | Quick setup |
| Modern Interface | ❌ Different | ✅ Full features | 🐌 Comprehensive | New code |

## Conclusion

The refactored SetGlobalDataName system provides a robust, comprehensive global data name management framework while maintaining full backward compatibility. The system offers multiple interfaces to suit different use cases, from simple legacy compatibility to full-featured modern name assignment with detailed validation and error handling.

The modular design allows for easy extension and customization while providing the security and performance characteristics required for a production game server environment.
