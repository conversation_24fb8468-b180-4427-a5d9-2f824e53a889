/**
 * @file CMainThread_PlayerManagement.h
 * @brief Player management system for CMainThread
 * 
 * Refactored player management system that handles player initialization,
 * character management, player state tracking, and related operations.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>
#include <mutex>
#include <optional>

// Forward declarations
class CMainThread;
class CUserDB;
class CPartyPlayer;
class CPlayer;
struct _object_id;
struct _AVATOR_DATA;

/**
 * @enum PlayerObjectType
 * @brief Types of player-related objects
 */
enum class PlayerObjectType : uint32_t {
    UserDatabase = 0,
    PartyPlayer,
    Player,
    MAX_PLAYER_OBJECT_TYPES
};

/**
 * @enum PlayerInitResult
 * @brief Result codes for player initialization operations
 */
enum class PlayerInitResult : int32_t {
    Success = 1,
    Failure = 0,
    InvalidParameter = -1,
    AllocationError = -2,
    DatabaseError = -3,
    InitializationError = -4,
    SystemError = -5
};

/**
 * @enum PlayerState
 * @brief Player state tracking
 */
enum class PlayerState : uint32_t {
    Inactive = 0,
    LoggingIn,
    CharacterSelect,
    Loading,
    Active,
    Disconnecting,
    Disconnected
};

/**
 * @enum CharacterLoadResult
 * @brief Result codes for character loading operations
 */
enum class CharacterLoadResult : int32_t {
    Success = 1,
    Failure = 0,
    NotFound = -1,
    DatabaseError = -2,
    InvalidData = -3,
    SystemError = -4
};

/**
 * @struct PlayerObjectInfo
 * @brief Information about a player object type
 */
struct PlayerObjectInfo {
    std::string name;
    std::string description;
    uint32_t maxCount;
    size_t objectSize;
    bool isRequired;
    
    PlayerObjectInfo() = default;
    PlayerObjectInfo(const std::string& n, const std::string& desc, 
                    uint32_t count, size_t size, bool required = true)
        : name(n), description(desc), maxCount(count), objectSize(size), isRequired(required) {}
};

/**
 * @struct PlayerInitStats
 * @brief Statistics for player initialization
 */
struct PlayerInitStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(PlayerObjectType::MAX_PLAYER_OBJECT_TYPES)> initStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(PlayerObjectType::MAX_PLAYER_OBJECT_TYPES)> initTime;
    uint32_t successfulInits;
    uint32_t failedInits;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        initStatus.fill(false);
        initTime.fill(std::chrono::milliseconds::zero());
        successfulInits = 0;
        failedInits = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalInitTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulInits + failedInits;
        return total > 0 ? (static_cast<double>(successfulInits) / total) * 100.0 : 0.0;
    }
};

/**
 * @class CMainThreadPlayerManagement
 * @brief Player management system for CMainThread
 * 
 * This class handles player initialization, character management,
 * player state tracking, and related operations.
 */
class CMainThreadPlayerManagement {
public:
    /**
     * @brief Constructor
     */
    CMainThreadPlayerManagement();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadPlayerManagement();
    
    // Delete copy constructor and assignment operator
    CMainThreadPlayerManagement(const CMainThreadPlayerManagement&) = delete;
    CMainThreadPlayerManagement& operator=(const CMainThreadPlayerManagement&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadPlayerManagement(CMainThreadPlayerManagement&&) = default;
    CMainThreadPlayerManagement& operator=(CMainThreadPlayerManagement&&) = default;
    
    /**
     * @brief Initialize player objects
     * 
     * Initializes all player-related objects including UserDB, PartyPlayer, and Player.
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return PlayerInitResult indicating success or failure
     */
    PlayerInitResult InitializePlayerObjects(CMainThread* mainThread);
    
    /**
     * @brief Legacy ObjectInit function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return bool (true for success, false for failure)
     */
    static bool ObjectInit_Legacy(CMainThread* mainThread);
    
    /**
     * @brief Load character data
     * 
     * Loads character data from the database.
     * 
     * @param accountSerial Account serial number
     * @param characterSerial Character serial number
     * @param avatorData Pointer to avatar data structure
     * @param firstLogin Whether this is the first login
     * @return CharacterLoadResult indicating success or failure
     */
    CharacterLoadResult LoadCharacterData(uint32_t accountSerial, uint32_t characterSerial, 
                                        _AVATOR_DATA* avatorData, bool firstLogin = false);
    
    /**
     * @brief Get player initialization statistics
     * @return const reference to initialization statistics
     */
    const PlayerInitStats& GetInitStats() const;
    
    /**
     * @brief Get the last initialization error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get player object information
     * @param objectType The player object type
     * @return PlayerObjectInfo structure with object information
     */
    static PlayerObjectInfo GetObjectInfo(PlayerObjectType objectType);
    
    /**
     * @brief Find available player slot
     * 
     * Finds an available player slot for a new connection.
     * 
     * @return Optional containing the available slot index, or empty if none available
     */
    std::optional<uint32_t> FindAvailablePlayerSlot() const;
    
    /**
     * @brief Get player by index
     * 
     * Gets a player object by its index.
     * 
     * @param index Player index
     * @return Pointer to CPlayer object, or nullptr if invalid
     */
    CPlayer* GetPlayerByIndex(uint32_t index) const;
    
    /**
     * @brief Get player by account serial
     * 
     * Gets a player object by its account serial number.
     * 
     * @param accountSerial Account serial number
     * @return Pointer to CPlayer object, or nullptr if not found
     */
    CPlayer* GetPlayerByAccountSerial(uint32_t accountSerial) const;
    
    /**
     * @brief Get player by character serial
     * 
     * Gets a player object by its character serial number.
     * 
     * @param characterSerial Character serial number
     * @return Pointer to CPlayer object, or nullptr if not found
     */
    CPlayer* GetPlayerByCharacterSerial(uint32_t characterSerial) const;
    
    /**
     * @brief Get user database by index
     * 
     * Gets a user database object by its index.
     * 
     * @param index User database index
     * @return Pointer to CUserDB object, or nullptr if invalid
     */
    CUserDB* GetUserDBByIndex(uint32_t index) const;
    
    /**
     * @brief Get party player by index
     * 
     * Gets a party player object by its index.
     * 
     * @param index Party player index
     * @return Pointer to CPartyPlayer object, or nullptr if invalid
     */
    CPartyPlayer* GetPartyPlayerByIndex(uint32_t index) const;
    
    /**
     * @brief Get active player count
     * @return Number of active players
     */
    uint32_t GetActivePlayerCount() const;
    
    /**
     * @brief Register player state change callback
     * @param callback Function to call when player state changes
     */
    void RegisterPlayerStateChangeCallback(std::function<void(uint32_t, PlayerState, PlayerState)> callback);

private:
    // Player object initialization
    PlayerInitResult InitializeUserDatabase();
    PlayerInitResult InitializePartyPlayers();
    PlayerInitResult InitializePlayers();
    
    // Helper functions
    void CreateObjectID(_object_id* pID, uint8_t objectType, uint32_t index);
    void LogInitialization(PlayerObjectType objectType, bool success, uint32_t count = 0, 
                          size_t memoryUsage = 0, const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    
    // Player state tracking
    void UpdatePlayerState(uint32_t playerIndex, PlayerState newState);
    
    // Statistics and error handling
    PlayerInitStats m_initStats;
    mutable std::mutex m_statsMutex;
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // Player state callbacks
    std::vector<std::function<void(uint32_t, PlayerState, PlayerState)>> m_stateCallbacks;
    mutable std::mutex m_callbackMutex;
    
    // Player state tracking
    std::vector<PlayerState> m_playerStates;
    mutable std::mutex m_statesMutex;
    
    // Security
    uint64_t m_securityCookie{0};
    
    // Main thread reference
    CMainThread* m_mainThread{nullptr};
};

/**
 * @brief Convert PlayerObjectType enum to string for logging
 * @param objectType The object type
 * @return String representation of the object type
 */
std::string PlayerObjectTypeToString(PlayerObjectType objectType);

/**
 * @brief Convert PlayerInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string PlayerInitResultToString(PlayerInitResult result);

/**
 * @brief Convert PlayerState enum to string for logging
 * @param state The player state
 * @return String representation of the state
 */
std::string PlayerStateToString(PlayerState state);

/**
 * @brief Convert CharacterLoadResult enum to string for logging
 * @param result The character load result
 * @return String representation of the result
 */
std::string CharacterLoadResultToString(CharacterLoadResult result);

// External player management instance
extern std::unique_ptr<CMainThreadPlayerManagement> g_PlayerManagement;
extern std::mutex g_PlayerManagementMutex;
