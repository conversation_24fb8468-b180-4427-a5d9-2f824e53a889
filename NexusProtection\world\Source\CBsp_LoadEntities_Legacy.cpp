/**
 * @file CBsp_LoadEntities_Legacy.cpp
 * @brief Legacy compatibility wrapper for CBsp::LoadEntities
 * 
 * This file provides the exact original function signature for backward compatibility
 * with existing code that calls the original decompiled function.
 * 
 * Original function: LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 * Address: 0x1404F96C0
 * Signature: void __fastcall CBsp::LoadEntities(CBsp *this, struct _READ_MAP_ENTITIES_LIST *a2)
 */

#include "../Headers/CBsp.h"
#include <cstdint>

// External function declarations (to be properly linked)
extern void* Dmalloc(size_t size);
extern void memset_0(void* ptr, int value, size_t size);
extern void memcpy_0(void* dest, const void* src, size_t size);
extern void SetMergeFileManager(CMergeFileManager* mfm);
extern void Warning(const char* filename, const char* message);
extern uint32_t dword_184A797AC;
extern const char byte_184A790F0[];
extern uint64_t _security_cookie;

/**
 * Legacy LoadEntities function with exact original signature
 * This maintains 100% compatibility with the original decompiled function
 * 
 * @param this_ptr Pointer to CBsp instance (original 'this' parameter)
 * @param a2 Pointer to _READ_MAP_ENTITIES_LIST structure
 */
extern "C" void __fastcall CBsp_LoadEntities_Original(CBsp* this_ptr, struct _READ_MAP_ENTITIES_LIST* a2) {
    // Security cookie setup (equivalent to original stack protection)
    uint64_t v32 = 0;
    uint64_t v36 = reinterpret_cast<uint64_t>(&v32) ^ _security_cookie;
    
    // Call the modern refactored implementation
    if (this_ptr && a2) {
        this_ptr->LoadEntities_Legacy(a2);
    }
    
    // Verify security cookie (equivalent to original stack protection check)
    if ((reinterpret_cast<uint64_t>(&v32) ^ _security_cookie) != v36) {
        // Stack corruption detected - this would trigger security handler in original
        // For now, we'll just log an error
        Logger::Error("CBsp_LoadEntities_Original - Stack corruption detected");
    }
}

/**
 * Alternative legacy wrapper that matches the exact original calling convention
 * This can be used as a drop-in replacement for the original function
 */
void CBsp::LoadEntities_OriginalSignature(struct _READ_MAP_ENTITIES_LIST* a2) {
    // Buffer for path building (equivalent to original v35[256])
    char pathBuffer[256];
    
    // Local variables (equivalent to original local variables)
    struct _READ_MAP_ENTITIES_LIST* v2 = a2;
    CBsp* v3 = this;
    uint32_t v4 = this->mEntityListNum;
    
    // Early exit if no entities
    if (v4 == 0) {
        return;
    }
    
    // Allocate memory for entities (equivalent to original Dmalloc calls)
    CEntity* v5 = static_cast<CEntity*>(Dmalloc(244 * v4));
    v3->mEntity = v5;
    memset_0(v5, 0, 244 * v3->mEntityListNum);
    
    // Allocate memory for particles
    CParticle* v6 = static_cast<CParticle*>(Dmalloc(1168 * v3->mEntityListNum));
    v3->mParticle = v6;
    memset_0(v6, 0, 1168 * v3->mEntityListNum);
    
    // Update total allocation size
    v3->mTotalAllocSize += 1412 * v3->mEntityListNum;
    
    // Set merge file manager
    SetMergeFileManager(&v3->mMapEntityMFM);
    
    // Process entities (equivalent to original entity loading loop)
    uint32_t v7 = 0;
    if (v3->mEntityListNum > 0) {
        uint64_t v8 = 0;
        uint64_t v9 = 0;
        
        do {
            // Build path (equivalent to original path building logic)
            uint64_t v10 = 0;
            char v11;
            do {
                v11 = byte_184A790F0[v10];
                pathBuffer[v10++] = v11;
            } while (v11);
            
            _ENTITY_LIST* v12 = v3->mEntityList;
            
            // Handle path building based on first character
            if (v12[v8].Name[0] == 92) { // Backslash
                // Copy name without first character
                char* v15 = &pathBuffer[strlen(pathBuffer) + 1];
                uint64_t v14 = 0;
                char v16;
                do {
                    v16 = v12[v7].Name[v14 + 1];
                    v15[v14++ - 1] = v16;
                } while (v16);
            } else {
                // Copy full name
                char* v18 = &pathBuffer[strlen(pathBuffer) + 1];
                uint64_t v17 = 0;
                char v19;
                do {
                    v19 = v12[v7].Name[v17];
                    v18[v17++ - 1] = v19;
                } while (v19);
            }
            
            // Initialize file existence flag
            v12[v8].IsFileExist = 0;
            
            // Set shader ID if global flag is not set
            if (!dword_184A797AC) {
                v3->mEntityList[v8].ShaderID = 0;
            }
            
            _ENTITY_LIST* v20 = v3->mEntityList;
            
            // Process particle or regular entity
            if (v20[v8].IsParticle) {
                // Load particle
                if (CParticle::LoadParticleSPT(&v3->mParticle[v7], pathBuffer, 0)) {
                    v3->mEntityList[v8].IsFileExist = 1;
                    CParticle::InitParticle(&v3->mParticle[v7]);
                    CParticle::SetParticleState(&v3->mParticle[v7], 1);
                } else {
                    Warning(pathBuffer, " <- 파티클 로딩, 파티클 spt가 아닙니다.");
                }
            } else {
                // Load regular entity
                int v22 = 0;
                if (v20[v8].ShaderID) {
                    v22 = 2;
                }
                
                if (CEntity::LoadEntity(&v3->mEntity[v7], pathBuffer, v22 | 0x20)) {
                    if (v3->mEntityList[v8].Flag & 0x40) {
                        v3->mEntity[v9].mFlag |= 0x40;
                    }
                    CEntity::RestoreTexMem(&v3->mEntity[v7]);
                    v3->mEntityList[v8].IsFileExist = 1;
                }
            }
            
            ++v7;
            ++v9;
            ++v8;
        } while (v7 < v3->mEntityListNum);
    }
    
    // Process map entities (equivalent to original map entity processing)
    uint32_t v24 = 0;
    if (v3->mMapEntitiesListNum > 0) {
        uint64_t v25 = 0;
        int64_t v26 = reinterpret_cast<int64_t>(&v2->Pos[1]);
        
        do {
            if (v3->mEntityList[*reinterpret_cast<uint16_t*>(v26 - 10)].IsFileExist) {
                // Copy entity data
                v3->mMapEntitiesList[v25].ID = *reinterpret_cast<uint16_t*>(v26 - 10);
                v3->mMapEntitiesList[v25].Pos[0] = *reinterpret_cast<float*>(v26 - 4);
                v3->mMapEntitiesList[v25].Pos[1] = *reinterpret_cast<float*>(v26);
                v3->mMapEntitiesList[v25].Pos[2] = *reinterpret_cast<float*>(v26 + 4);
                v3->mMapEntitiesList[v25].RotX = *reinterpret_cast<float*>(v26 + 8);
                v3->mMapEntitiesList[v25].RotY = *reinterpret_cast<float*>(v26 + 12);
                v3->mMapEntitiesList[v25].Scale = *reinterpret_cast<float*>(v26 - 8);
                v3->mMapEntitiesList[v25].BBMin[0] = *reinterpret_cast<int16_t*>(v26 + 16);
                v3->mMapEntitiesList[v25].BBMin[1] = *reinterpret_cast<int16_t*>(v26 + 18);
                v3->mMapEntitiesList[v25].BBMin[2] = *reinterpret_cast<int16_t*>(v26 + 20);
                v3->mMapEntitiesList[v25].BBMax[0] = *reinterpret_cast<int16_t*>(v26 + 22);
                v3->mMapEntitiesList[v25].BBMax[1] = *reinterpret_cast<int16_t*>(v26 + 24);
                v3->mMapEntitiesList[v25].BBMax[2] = *reinterpret_cast<int16_t*>(v26 + 26);
                
                // Generate random frame offset
                int64_t v28 = rand();
                v3->mMapEntitiesList[v25].AddFrame = static_cast<float>((static_cast<uint8_t>((v28 >> 32) + v28) - (v28 >> 32)) * 0.25);
                
                // Initialize particle pointer
                v3->mMapEntitiesList[v25].Particle = nullptr;
                
                // Create particle instance if needed
                if (v3->mEntityList[v3->mMapEntitiesList[v25].ID].IsParticle) {
                    struct _READ_MAP_ENTITIES_LIST* v29 = static_cast<struct _READ_MAP_ENTITIES_LIST*>(operator new(0x490));
                    CParticle* v31;
                    if (v29) {
                        v31 = reinterpret_cast<CParticle*>(CParticle::CParticle(v29));
                    } else {
                        v31 = nullptr;
                    }
                    
                    v3->mMapEntitiesList[v25].Particle = v31;
                    memcpy_0(v31, &v3->mParticle[v3->mMapEntitiesList[v25].ID], 0x490);
                    CParticle::InitParticle(v31);
                }
            } else {
                // Clear map entity data
                int64_t v27 = reinterpret_cast<int64_t>(&v3->mMapEntitiesList[v24]);
                *reinterpret_cast<uint64_t*>(v27) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 8) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 16) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 24) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 32) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 40) = 0;
                *reinterpret_cast<uint64_t*>(v27 + 48) = 0;
                *reinterpret_cast<uint16_t*>(v27 + 56) = 0;
            }
            
            ++v24;
            v26 += 38;
            ++v25;
        } while (v24 < v3->mMapEntitiesListNum);
    }
}
