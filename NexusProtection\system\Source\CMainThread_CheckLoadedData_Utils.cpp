/**
 * @file CMainThread_CheckLoadedData_Utils.cpp
 * @brief Utility functions for data validation system
 * 
 * Contains helper functions for data validation, error handling,
 * and statistics tracking.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_CheckLoadedData.h"
#include "CMainThread.h"
#include "CRecordData.h"
#include <iostream>
#include <format>
#include <algorithm>

/**
 * @brief Get data validation statistics
 * @return const reference to validation statistics
 */
const DataValidationStats& CMainThreadCheckLoadedData::GetValidationStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_validationStats;
}

/**
 * @brief Check if a specific data type is validated
 * @param validationType The data validation type to check
 * @return true if validated, false otherwise
 */
bool CMainThreadCheckLoadedData::IsDataValidated(DataValidationType validationType) const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    size_t index = static_cast<size_t>(validationType);
    if (index >= m_validationStats.validationStatus.size()) {
        return false;
    }
    return m_validationStats.validationStatus[index];
}

/**
 * @brief Get the last validation error message
 * @return string containing the last error message
 */
std::string CMainThreadCheckLoadedData::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get information about a specific validation type
 * @param validationType The validation type
 * @return DataValidationInfo structure with validation information
 */
DataValidationInfo CMainThreadCheckLoadedData::GetValidationInfo(DataValidationType validationType) {
    auto it = s_validationRegistry.find(validationType);
    if (it != s_validationRegistry.end()) {
        return it->second;
    }
    return DataValidationInfo("Unknown", "Unknown validation type", false);
}

/**
 * @brief Validate all required data is present and valid
 * @return true if all required data is validated
 */
bool CMainThreadCheckLoadedData::ValidateRequiredData() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    // Check all required validations
    for (const auto& [validationType, validationInfo] : s_validationRegistry) {
        if (validationInfo.isRequired) {
            size_t index = static_cast<size_t>(validationType);
            if (index >= m_validationStats.validationStatus.size() || !m_validationStats.validationStatus[index]) {
                return false;
            }
        }
    }
    
    return true;
}

/**
 * @brief Validate a record data table
 * @param recordData Pointer to record data
 * @param validationType Type of validation
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateRecordData(CRecordData* recordData, DataValidationType validationType) {
    try {
        auto startTime = std::chrono::steady_clock::now();
        
        if (!recordData) {
            LogValidation(validationType, false, 0, "Record data is null");
            return DataValidationResult::DataNotLoaded;
        }
        
        if (!CRecordData_IsValid(recordData)) {
            LogValidation(validationType, false, 0, "Record data is invalid");
            return DataValidationResult::InvalidData;
        }
        
        uint32_t recordCount = CRecordData_GetRecordNum(recordData);
        auto validationInfo = GetValidationInfo(validationType);
        
        if (!ValidateRecordCount(recordCount, validationInfo.minRecords, validationInfo.maxRecords)) {
            LogValidation(validationType, false, recordCount, 
                         std::format("Record count {} outside valid range [{}, {}]", 
                                   recordCount, validationInfo.minRecords, validationInfo.maxRecords));
            return DataValidationResult::MissingRecords;
        }
        
        // Validate record integrity if custom validator is provided
        if (validationInfo.customValidator) {
            if (!validationInfo.customValidator(recordData)) {
                LogValidation(validationType, false, recordCount, "Custom validation failed");
                return DataValidationResult::CorruptedData;
            }
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        LogValidation(validationType, true, recordCount);
        
        // Update timing statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t index = static_cast<size_t>(validationType);
            if (index < m_validationStats.validationTime.size()) {
                m_validationStats.validationTime[index] = duration;
            }
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        LogValidation(validationType, false, 0, std::format("Exception: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate item data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateItemData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetItemDataTable(), DataValidationType::ItemData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating item data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate effect data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateEffectData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetBulletItemEffectDataTable(), DataValidationType::BulletItemEffectData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating effect data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate class data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateClassData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetClassTable(), DataValidationType::ClassData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating class data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate player data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidatePlayerData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetPlayerTable(), DataValidationType::PlayerCharacterData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating player data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate monster data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateMonsterData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetMonsterTable(), DataValidationType::MonsterCharacterData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating monster data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate NPC data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateNPCData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetNPCharacterDataTable(), DataValidationType::NPCharacterData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating NPC data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate animus data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateAnimusData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetAnimusItemDataTable(), DataValidationType::AnimusItemData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating animus data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate experience data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateExpData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetExpDataTable(), DataValidationType::ExpData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating experience data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate unit part data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateUnitPartData(CMainThread* mainThread) {
    try {
        // Validate all unit part data tables
        const std::array<std::pair<DataValidationType, CRecordData*(CMainThread::*)()>, 8> unitTables = {{
            {DataValidationType::UnitHeadData, &CMainThread::GetUnitHeadDataTable},
            {DataValidationType::UnitUpperData, &CMainThread::GetUnitUpperDataTable},
            {DataValidationType::UnitLowerData, &CMainThread::GetUnitLowerDataTable},
            {DataValidationType::UnitArmsData, &CMainThread::GetUnitArmsDataTable},
            {DataValidationType::UnitShoulderData, &CMainThread::GetUnitShoulderDataTable},
            {DataValidationType::UnitBackData, &CMainThread::GetUnitBackDataTable},
            {DataValidationType::UnitBulletData, &CMainThread::GetUnitBulletDataTable},
            {DataValidationType::UnitFrameData, &CMainThread::GetUnitFrameDataTable}
        }};
        
        for (const auto& [validationType, getTableFunc] : unitTables) {
            auto result = ValidateRecordData((mainThread->*getTableFunc)(), validationType);
            if (result != DataValidationResult::Success) {
                return result;
            }
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating unit part data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate mob message data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateMobMessageData(CMainThread* mainThread) {
    try {
        return ValidateRecordData(mainThread->GetMobMessageDataTable(), DataValidationType::MobMessageData);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating mob message data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate potion system
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidatePotionSystem(CMainThread* mainThread) {
    try {
        if (!CPotionMgr_IsValid(&g_PotionMgr)) {
            LogValidation(DataValidationType::PotionSystem, false, 0, "Potion manager is invalid");
            return DataValidationResult::InvalidData;
        }
        
        LogValidation(DataValidationType::PotionSystem, true, 1);
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating potion system: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate quest system
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateQuestSystem(CMainThread* mainThread) {
    try {
        if (!g_pQuestMgr || !CQuestMgr_IsValid(g_pQuestMgr)) {
            LogValidation(DataValidationType::QuestSystem, false, 0, "Quest manager is invalid");
            return DataValidationResult::InvalidData;
        }
        
        LogValidation(DataValidationType::QuestSystem, true, 1);
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating quest system: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate item combine system
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateItemCombineSystem(CMainThread* mainThread) {
    try {
        if (!ItemCombineMgr_CheckLoadData()) {
            LogValidation(DataValidationType::ItemCombineSystem, false, 0, "Item combine manager data check failed");
            return DataValidationResult::InvalidData;
        }
        
        LogValidation(DataValidationType::ItemCombineSystem, true, 1);
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating item combine system: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Log validation result
 * @param validationType Type of validation
 * @param success Whether validation succeeded
 * @param recordCount Number of records validated
 * @param errorMsg Error message if validation failed
 */
void CMainThreadCheckLoadedData::LogValidation(DataValidationType validationType, bool success, 
                                              uint32_t recordCount, const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(validationType);
    if (index < m_validationStats.validationStatus.size()) {
        m_validationStats.validationStatus[index] = success;
        m_validationStats.recordCount[index] = recordCount;
        
        if (success) {
            m_validationStats.successfulValidations++;
        } else {
            m_validationStats.failedValidations++;
            if (!errorMsg.empty()) {
                m_validationStats.lastError = errorMsg;
            }
        }
    }
    
    // Log to console
    auto validationInfo = GetValidationInfo(validationType);
    if (success) {
        std::cout << std::format("[INFO] Validated {}: {} records", validationInfo.name, recordCount) << std::endl;
    } else {
        std::cout << std::format("[ERROR] Failed to validate {}: {}", validationInfo.name, errorMsg) << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void CMainThreadCheckLoadedData::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
}

/**
 * @brief Validate record count is within acceptable range
 * @param actualCount Actual number of records
 * @param minCount Minimum expected records
 * @param maxCount Maximum expected records
 * @return true if count is valid
 */
bool CMainThreadCheckLoadedData::ValidateRecordCount(uint32_t actualCount, uint32_t minCount, uint32_t maxCount) {
    return actualCount >= minCount && actualCount <= maxCount;
}

/**
 * @brief Validate record data integrity
 * @param recordData Pointer to record data
 * @param recordSize Size of record data
 * @return true if data integrity is valid
 */
bool CMainThreadCheckLoadedData::ValidateRecordIntegrity(const void* recordData, size_t recordSize) {
    if (!recordData || recordSize == 0) {
        return false;
    }
    
    // Basic integrity check - ensure data is not all zeros or all 0xFF
    const uint8_t* data = static_cast<const uint8_t*>(recordData);
    bool allZeros = true;
    bool allOnes = true;
    
    for (size_t i = 0; i < recordSize; ++i) {
        if (data[i] != 0) allZeros = false;
        if (data[i] != 0xFF) allOnes = false;
        if (!allZeros && !allOnes) break;
    }
    
    return !allZeros && !allOnes;
}
