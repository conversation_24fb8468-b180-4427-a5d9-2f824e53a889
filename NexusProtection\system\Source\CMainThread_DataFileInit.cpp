/**
 * @file CMainThread_DataFileInit.cpp
 * @brief Data file initialization implementation for CMainThread
 * 
 * Refactored from DataFileInitCMainThreadAEAA_NXZ_1401E5BF0.c
 * This implements the data file loading and initialization system.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_DataFileInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>
#include <fstream>
#include <Windows.h>

// External function declarations (to be properly linked)
extern bool WriteTableData(int tableId, void* tableData, int param, char* errorCode);
extern void MyMessageBox(const char* title, const char* format, ...);
extern void* operator new[](size_t size);
extern void operator delete[](void* ptr);
extern uint32_t saturated_mul(uint32_t a, uint32_t b);
extern int atoi(const char* str);
extern uint64_t _security_cookie;

// External global objects
extern CPotionMgr g_PotionMgr;
extern AggroCaculateData g_AggroCaculateData;
extern MonsterSetInfoData g_MonsterSetInfoData;

// External class methods (to be properly linked)
extern bool CRecordData_ReadRecord(CRecordData* recordData, const char* filename, uint32_t structSize, char* errorCode);
extern bool CRecordData_ReadRecord_Ex(CRecordData* recordData, const char* filename1, const char* filename2, uint32_t structSize, char* errorCode);
extern void* CRecordData_GetRecord(CRecordData* recordData, const char* code);
extern void* CRecordData_GetRecord_Index(CRecordData* recordData, int index);
extern uint32_t CRecordData_GetRecordNum(CRecordData* recordData);
extern void CRecordData_Constructor(CRecordData* recordData);
extern void CRecordData_Destructor(CRecordData* recordData);

extern bool CItemLootTable_ReadRecord(CItemLootTable* table, const char* filename, void* itemData, char* errorCode);
extern bool COreCuttingTable_ReadRecord(COreCuttingTable* table, const char* filename, void* itemData1, void* itemData2, char* errorCode);
extern bool CItemUpgradeTable_ReadRecord(CItemUpgradeTable* table, const char* filename, void* itemData, char* errorCode);
extern bool CMonsterSPGroupTable_Create(CMonsterSPGroupTable* table, void* monsterData, void* baseSPData, void* effectData1, void* effectData2, void* effectData3);

extern bool CPotionMgr_DatafileInit(CPotionMgr* mgr);
extern bool CQuestMgr_LoadQuestData();
extern bool ItemCombineMgr_LoadData();
extern bool CPcBangFavor_LoadPcBangData(CPcBangFavor* favor);
extern bool CSUItemSystem_SUItemSystem_Init(CSUItemSystem* system);
extern bool AggroCaculateData_Load(AggroCaculateData* data, const char* filename);
extern bool MonsterSetInfoData_Load(MonsterSetInfoData* data, const char* filename);

extern CPcBangFavor* CPcBangFavor_Instance();
extern CSUItemSystem* CSUItemSystem_Instance();

extern bool CMainThread_SetGlobalDataName(CMainThread* mainThread);
extern bool CMainThread_check_loaded_data(CMainThread* mainThread);
extern void CMainThread_gm_MainThreadControl(CMainThread* mainThread);

// Static file registry initialization
std::unordered_map<DataFileType, DataFileInfo> CMainThreadDataFileInit::s_fileRegistry;

/**
 * @brief Constructor
 */
CMainThreadDataFileInit::CMainThreadDataFileInit() {
    m_loadStats.Reset();
    InitializeFileRegistry();
}

/**
 * @brief Destructor
 */
CMainThreadDataFileInit::~CMainThreadDataFileInit() {
    // Cleanup if needed
}

/**
 * @brief Initialize the file registry with all data file information
 */
void CMainThreadDataFileInit::InitializeFileRegistry() {
    if (!s_fileRegistry.empty()) {
        return; // Already initialized
    }
    
    // Core Data Files
    s_fileRegistry[DataFileType::ItemData] = DataFileInfo("WriteTableData(37)", "Item data table", 0, true);
    s_fileRegistry[DataFileType::SkillData] = DataFileInfo(".\\script\\skill.dat", "Skill data", 1168, true);
    s_fileRegistry[DataFileType::ForceData] = DataFileInfo(".\\script\\force.dat", "Force data", 1088, true);
    s_fileRegistry[DataFileType::ClassSkillData] = DataFileInfo(".\\script\\ClassSkill.dat", "Class skill data", 1168, true);
    s_fileRegistry[DataFileType::BulletItemEffectData] = DataFileInfo(".\\script\\BulletItemEffect.dat", "Bullet item effect data", 1168, true);
    
    // Character Data
    s_fileRegistry[DataFileType::ClassData] = DataFileInfo(".\\Script\\Class.dat", "Class data", 0x8D0, true);
    s_fileRegistry[DataFileType::GradeData] = DataFileInfo(".\\Script\\Grade.dat", "Grade data", 0x4C, true);
    s_fileRegistry[DataFileType::PlayerCharacterData] = DataFileInfo(".\\Script\\PlayerCharacter.dat", "Player character data", 0xA8, true);
    s_fileRegistry[DataFileType::MonsterCharacterData] = DataFileInfo(".\\Script\\MonsterCharacter.dat", "Monster character data", 0x9B4, true);
    s_fileRegistry[DataFileType::NPCharacterData] = DataFileInfo(".\\Script\\NPCharacter.dat", "NPC character data", 0x1E8, true);
    
    // Message Data
    s_fileRegistry[DataFileType::MobMessageData] = DataFileInfo(".\\Script\\MobMessage_str.dat", "Mob message data", 0x44C48, true);
    
    // Item Systems
    s_fileRegistry[DataFileType::AnimusItemData] = DataFileInfo(".\\Script\\AnimusItem.dat", "Animus item data", 0x188, true);
    s_fileRegistry[DataFileType::ExpData] = DataFileInfo(".\\Script\\Exp.dat", "Experience data", 0x104, true);
    s_fileRegistry[DataFileType::ItemLootingData] = DataFileInfo(".\\Script\\ItemLooting.dat", "Item looting data", 0, true);
    s_fileRegistry[DataFileType::OreCuttingData] = DataFileInfo(".\\Script\\OreCutting.dat", "Ore cutting data", 0, true);
    s_fileRegistry[DataFileType::ItemMakeData] = DataFileInfo(".\\Script\\ItemMakeData.dat", "Item make data", 0x22C, true);
    s_fileRegistry[DataFileType::ItemCombineData] = DataFileInfo(".\\Script\\ItemCombine.dat", "Item combine data", 0xD0, true);
    s_fileRegistry[DataFileType::ItemExchangeData] = DataFileInfo(".\\Script\\BoxItemOut.dat", "Item exchange data", 0x414, true);
    s_fileRegistry[DataFileType::ItemUpgradeData] = DataFileInfo(".\\Script\\ItemUpgrade.dat", "Item upgrade data", 0, true);
    
    // Unit Data
    s_fileRegistry[DataFileType::UnitHeadData] = DataFileInfo(".\\script\\UnitHead.dat", "Unit head data", 0x204, true);
    s_fileRegistry[DataFileType::UnitUpperData] = DataFileInfo(".\\script\\UnitUpper.dat", "Unit upper data", 0x204, true);
    s_fileRegistry[DataFileType::UnitLowerData] = DataFileInfo(".\\script\\UnitLower.dat", "Unit lower data", 0x204, true);
    s_fileRegistry[DataFileType::UnitArmsData] = DataFileInfo(".\\script\\UnitArms.dat", "Unit arms data", 0x204, true);
    s_fileRegistry[DataFileType::UnitShoulderData] = DataFileInfo(".\\script\\UnitShoulder.dat", "Unit shoulder data", 0x204, true);
    s_fileRegistry[DataFileType::UnitBackData] = DataFileInfo(".\\script\\UnitBack.dat", "Unit back data", 0x204, true);
    s_fileRegistry[DataFileType::UnitBulletData] = DataFileInfo(".\\script\\UnitBullet.dat", "Unit bullet data", 0x164, true);
    s_fileRegistry[DataFileType::UnitFrameData] = DataFileInfo(".\\script\\UnitFrame.dat", "Unit frame data", 0x288, true);
    
    // System Data
    s_fileRegistry[DataFileType::EditData] = DataFileInfo(".\\script\\EditData.dat", "Edit data", 0x83C, true);
    s_fileRegistry[DataFileType::MonsterCharacterAIData] = DataFileInfo(".\\script\\MonsterCharacterAI.dat", "Monster AI data", 0xB0, true);
    
    // Configuration Files
    s_fileRegistry[DataFileType::AggroCalculateConfig] = DataFileInfo(".\\Initialize\\AP.ini", "Aggro calculate configuration", 0, true);
    s_fileRegistry[DataFileType::MonsterSetConfig] = DataFileInfo(".\\Initialize\\MonsterSet.ini", "Monster set configuration", 0, true);
}

/**
 * @brief Main data file initialization function
 * 
 * Modern C++20 implementation of the original DataFileInit function.
 * Loads all game data files in the correct order with comprehensive error handling.
 * 
 * @param mainThread Pointer to CMainThread instance for accessing data tables
 * @return DataFileLoadResult indicating success or failure
 */
DataFileLoadResult CMainThreadDataFileInit::InitializeDataFiles(CMainThread* mainThread) {
    try {
        m_loadStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting data file initialization..." << std::endl;
        
        // Phase 1: Load core item data
        if (auto result = LoadItemData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 2: Load effect data
        if (auto result = LoadEffectData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 3: Load character data
        if (auto result = LoadCharacterData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 4: Load message data
        if (auto result = LoadMessageData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 5: Load item systems
        if (auto result = LoadItemSystems(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 6: Load unit data
        if (auto result = LoadUnitData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 7: Load system data
        if (auto result = LoadSystemData(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 8: Load manager systems
        if (auto result = LoadManagerSystems(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 9: Load configuration files
        if (auto result = LoadConfigurationFiles(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Phase 10: Finalize data loading
        if (auto result = FinalizeDataLoading(mainThread); result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Finalize loading statistics
        m_loadStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return DataFileLoadResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Data file initialization completed successfully in {}ms", 
                                m_loadStats.GetTotalLoadTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} files, {} MB)", 
                                m_loadStats.GetSuccessRate(), 
                                m_loadStats.successfulFiles,
                                m_loadStats.GetTotalDataSize() / (1024 * 1024)) << std::endl;
        
        return DataFileLoadResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during data file initialization: {}", e.what()));
        m_loadStats.endTime = std::chrono::steady_clock::now();
        return DataFileLoadResult::Failure;
    }
}

/**
 * @brief Legacy DataFileInit function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: char __fastcall CMainThread::DataFileInit(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return char (1 for success, 0 for failure)
 */
char CMainThreadDataFileInit::DataFileInit_Legacy(CMainThread* mainThread) {
    try {
        CMainThreadDataFileInit dataFileInit;
        DataFileLoadResult result = dataFileInit.InitializeDataFiles(mainThread);
        return (result == DataFileLoadResult::Success) ? 1 : 0;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in DataFileInit_Legacy: " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Load core item data
 * @param mainThread Pointer to CMainThread instance
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::LoadItemData(CMainThread* mainThread) {
    try {
        LogFileLoading(DataFileType::ItemData, false);

        char errorCode[512] = {0};

        // Call legacy WriteTableData function (equivalent to original WriteTableData(37, v61->m_tblItemData, 1, &szErrCode))
        if (!WriteTableData_Legacy(37, mainThread->GetItemDataTable(), 1, errorCode)) {
            LogFileLoading(DataFileType::ItemData, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            return DataFileLoadResult::Failure;
        }

        LogFileLoading(DataFileType::ItemData, true);
        return DataFileLoadResult::Success;

    } catch (const std::exception& e) {
        LogFileLoading(DataFileType::ItemData, false, e.what());
        return DataFileLoadResult::Failure;
    }
}

/**
 * @brief Load effect data files
 * @param mainThread Pointer to CMainThread instance
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::LoadEffectData(CMainThread* mainThread) {
    try {
        return LoadEffectFiles(mainThread);

    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during effect data loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}

/**
 * @brief Load effect files (skill, force, class skill, bullet item effect)
 * @param mainThread Pointer to CMainThread instance
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::LoadEffectFiles(CMainThread* mainThread) {
    try {
        // Define effect file information
        const std::array<DataFileType, 4> effectTypes = {
            DataFileType::SkillData,
            DataFileType::ForceData,
            DataFileType::ClassSkillData,
            DataFileType::BulletItemEffectData
        };

        char errorCode[512] = {0};

        // Load each effect file
        for (size_t i = 0; i < effectTypes.size(); ++i) {
            const auto& fileType = effectTypes[i];
            const auto& fileInfo = GetFileInfo(fileType);

            LogFileLoading(fileType, false);

            // Call legacy CRecordData::ReadRecord function
            if (!CRecordData_ReadRecord(
                    &mainThread->GetEffectDataTable()[i],
                    fileInfo.filename.c_str(),
                    fileInfo.structSize,
                    errorCode)) {

                LogFileLoading(fileType, false, errorCode);
                MyMessageBox("DatafileInit", errorCode);
                return DataFileLoadResult::Failure;
            }

            LogFileLoading(fileType, true);
        }

        // Initialize potion manager
        if (auto result = InitializePotionManager(); result != DataFileLoadResult::Success) {
            return result;
        }

        // Setup Xmas effects
        SetupXmasEffects(mainThread);

        return DataFileLoadResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during effect files loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}

/**
 * @brief Initialize potion manager
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::InitializePotionManager() {
    try {
        LogFileLoading(DataFileType::PotionSystem, false);

        if (!CPotionMgr_DatafileInit(&g_PotionMgr)) {
            LogFileLoading(DataFileType::PotionSystem, false, "CPotionMgr::DatafileInit failed");
            return DataFileLoadResult::Failure;
        }

        LogFileLoading(DataFileType::PotionSystem, true);
        return DataFileLoadResult::Success;

    } catch (const std::exception& e) {
        LogFileLoading(DataFileType::PotionSystem, false, e.what());
        return DataFileLoadResult::Failure;
    }
}

/**
 * @brief Setup Xmas effects
 * @param mainThread Pointer to CMainThread instance
 */
void CMainThreadDataFileInit::SetupXmasEffects(CMainThread* mainThread) {
    // This is a placeholder for the actual implementation
    // In the original code, this sets up CPlayer::ms_pXmas_Snow_Effect and CPlayer::ms_pXmas_Snow_Bullet_Effect

    // Example implementation:
    // CPlayer::ms_pXmas_Snow_Effect = (struct _skill_fld *)CRecordData::GetRecord(&mainThread->GetEffectDataTable()[3], "16");
    // CPlayer::ms_pXmas_Snow_Bullet_Effect = (struct _skill_fld *)CRecordData::GetRecord(&mainThread->GetEffectDataTable()[3], "15");

    // Check if effects are valid
    // if (!CPlayer::ms_pXmas_Snow_Effect || !CPlayer::ms_pXmas_Snow_Bullet_Effect) {
    //     MyMessageBox("MILKSIK_X_MAS_2006 Error", "m_tblEffectData[effect_code_throw].GetRecord( '%s' ) == NULL", "16");
    // }
}

/**
 * @brief Load character data
 * @param mainThread Pointer to CMainThread instance
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::LoadCharacterData(CMainThread* mainThread) {
    try {
        // Define character data files
        const std::array<DataFileType, 4> characterTypes = {
            DataFileType::ClassData,
            DataFileType::GradeData,
            DataFileType::PlayerCharacterData,
            DataFileType::MonsterCharacterData
        };

        char errorCode[512] = {0};

        // Load each character data file
        for (const auto& fileType : characterTypes) {
            const auto& fileInfo = GetFileInfo(fileType);

            LogFileLoading(fileType, false);

            CRecordData* recordData = nullptr;

            // Select the appropriate record data table based on file type
            switch (fileType) {
                case DataFileType::ClassData:
                    recordData = mainThread->GetClassTable();
                    break;
                case DataFileType::GradeData:
                    recordData = mainThread->GetGradeTable();
                    break;
                case DataFileType::PlayerCharacterData:
                    recordData = mainThread->GetPlayerTable();
                    break;
                case DataFileType::MonsterCharacterData:
                    recordData = mainThread->GetMonsterTable();
                    break;
                default:
                    SetLastError("Invalid character data file type");
                    return DataFileLoadResult::Failure;
            }

            // Call legacy CRecordData::ReadRecord function
            if (!CRecordData_ReadRecord(
                    recordData,
                    fileInfo.filename.c_str(),
                    fileInfo.structSize,
                    errorCode)) {

                LogFileLoading(fileType, false, errorCode);
                MyMessageBox("DatafileInit", errorCode);
                return DataFileLoadResult::Failure;
            }

            LogFileLoading(fileType, true);
        }

        return DataFileLoadResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during character data loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}
