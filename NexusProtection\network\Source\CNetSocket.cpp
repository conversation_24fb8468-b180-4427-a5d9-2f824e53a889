/*
 * CNetSocket.cpp - Modern Network Socket Management System
 * Refactored from 0CNetSocketQEAAXZ_14047DB60.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CNetSocket.h"
#include "../Headers/CNetIndexList.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"
#include <sstream>
#include <algorithm>

// Link with Winsock library
#pragma comment(lib, "ws2_32.lib")

/**
 * CNetSocket constructor
 */
CNetSocket::CNetSocket() 
    : m_dwMaxSockets(0)
    , m_ListenSocket(INVALID_SOCKET)
    , m_bSetSocket(false)
    , m_bListening(false)
    , m_bIPCheckEnabled(false)
    , m_LastError(ENetworkError::SUCCESS) {
    
    try {
        // Initialize IP check lists
        m_pIPCheckList = std::make_unique<CNetIndexList>();
        m_pIPCheckEmptyList = std::make_unique<CNetIndexList>();
        
        Logger::LogInfo("CNetSocket initialized");
        
    } catch (const std::exception& e) {
        m_LastErrorMessage = "Constructor failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        Logger::LogError("CNetSocket constructor failed: " + std::string(e.what()));
    }
}

/**
 * CNetSocket destructor
 */
CNetSocket::~CNetSocket() {
    try {
        if (m_bSetSocket) {
            Release();
        }
        
        Logger::LogInfo("CNetSocket destroyed");
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetSocket destructor error: " + std::string(e.what()));
    }
}

/**
 * Initialize socket system
 */
bool CNetSocket::Initialize(uint32_t maxSockets, const SocketConfig& config) {
    std::lock_guard<std::mutex> lock(m_SocketMutex);
    
    try {
        if (m_bSetSocket) {
            Logger::LogWarning("Socket system already initialized");
            return true;
        }
        
        // Initialize Winsock
        if (!InitializeWinsock()) {
            m_LastErrorMessage = "Failed to initialize Winsock";
            m_LastError = ENetworkError::NETWORK_DOWN;
            return false;
        }
        
        // Store configuration
        m_Config = config;
        m_dwMaxSockets = maxSockets;
        
        // Initialize socket array
        m_Socket.resize(maxSockets);
        for (auto& socket : m_Socket) {
            socket.InitParam();
        }
        
        m_bSetSocket = true;
        
        Logger::LogInfo("Socket system initialized with " + std::to_string(maxSockets) + " sockets");
        return true;
        
    } catch (const std::exception& e) {
        m_LastErrorMessage = "Initialize failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        Logger::LogError("CNetSocket::Initialize failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Release socket system
 */
void CNetSocket::Release() {
    std::lock_guard<std::mutex> lock(m_SocketMutex);
    
    try {
        if (!m_bSetSocket) {
            return;
        }
        
        // Stop listening
        m_bListening = false;
        
        // Close all sockets
        for (uint32_t i = 0; i < m_dwMaxSockets; ++i) {
            if (m_Socket[i].m_Socket != INVALID_SOCKET) {
                CloseSocket(i);
            }
        }
        
        // Close listen socket
        if (m_ListenSocket != INVALID_SOCKET) {
            closesocket(m_ListenSocket);
            m_ListenSocket = INVALID_SOCKET;
        }
        
        // Clear socket array
        m_Socket.clear();
        
        // Cleanup Winsock
        CleanupWinsock();
        
        m_bSetSocket = false;
        
        Logger::LogInfo("Socket system released");
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetSocket::Release error: " + std::string(e.what()));
    }
}

/**
 * Create and bind listening socket
 */
bool CNetSocket::CreateListenSocket(uint16_t port, const std::string& bindIP) {
    try {
        if (!m_bSetSocket) {
            m_LastErrorMessage = "Socket system not initialized";
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }
        
        // Create socket
        m_ListenSocket = CreateSocketWithOptions();
        if (m_ListenSocket == INVALID_SOCKET) {
            m_LastErrorMessage = "Failed to create listen socket";
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }
        
        // Set socket options
        int optval = 1;
        if (setsockopt(m_ListenSocket, SOL_SOCKET, SO_REUSEADDR, 
                      (const char*)&optval, sizeof(optval)) == SOCKET_ERROR) {
            Logger::LogWarning("Failed to set SO_REUSEADDR option");
        }
        
        // Bind socket
        sockaddr_in addr;
        memset(&addr, 0, sizeof(addr));
        addr.sin_family = AF_INET;
        addr.sin_port = htons(port);
        
        if (bindIP.empty()) {
            addr.sin_addr.s_addr = INADDR_ANY;
        } else {
            inet_pton(AF_INET, bindIP.c_str(), &addr.sin_addr);
        }
        
        if (bind(m_ListenSocket, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
            int error = WSAGetLastError();
            m_LastErrorMessage = "Failed to bind socket: " + GetSocketErrorMessage(error);
            m_LastError = ENetworkError::SOCKET_ERROR;
            closesocket(m_ListenSocket);
            m_ListenSocket = INVALID_SOCKET;
            return false;
        }
        
        Logger::LogInfo("Listen socket created and bound to port " + std::to_string(port));
        return true;
        
    } catch (const std::exception& e) {
        m_LastErrorMessage = "CreateListenSocket failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        Logger::LogError("CNetSocket::CreateListenSocket failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Start listening for connections
 */
bool CNetSocket::StartListening() {
    try {
        if (m_ListenSocket == INVALID_SOCKET) {
            m_LastErrorMessage = "No listen socket created";
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }
        
        if (listen(m_ListenSocket, m_Config.backlogSize) == SOCKET_ERROR) {
            int error = WSAGetLastError();
            m_LastErrorMessage = "Failed to start listening: " + GetSocketErrorMessage(error);
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }
        
        // Set non-blocking mode
        SetSocketNonBlocking(m_ListenSocket, true);
        
        m_bListening = true;
        
        Logger::LogInfo("Started listening for connections");
        return true;
        
    } catch (const std::exception& e) {
        m_LastErrorMessage = "StartListening failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        Logger::LogError("CNetSocket::StartListening failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Accept incoming connection
 */
bool CNetSocket::AcceptConnection(uint32_t& socketIndex) {
    std::lock_guard<std::mutex> lock(m_SocketMutex);
    
    try {
        if (!m_bListening) {
            return false;
        }
        
        // Find available socket slot
        socketIndex = UINT32_MAX;
        for (uint32_t i = 0; i < m_dwMaxSockets; ++i) {
            if (m_Socket[i].m_Socket == INVALID_SOCKET) {
                socketIndex = i;
                break;
            }
        }
        
        if (socketIndex == UINT32_MAX) {
            m_LastErrorMessage = "No available socket slots";
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }
        
        // Accept connection
        sockaddr_in clientAddr;
        int addrLen = sizeof(clientAddr);
        SOCKET clientSocket = accept(m_ListenSocket, (sockaddr*)&clientAddr, &addrLen);
        
        if (clientSocket == INVALID_SOCKET) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
                HandleSocketError(socketIndex, "accept", error);
            }
            return false;
        }
        
        // Check IP if filtering is enabled
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        
        if (m_bIPCheckEnabled && !CheckIPAddress(clientIP)) {
            Logger::LogWarning("Connection rejected from IP: " + std::string(clientIP));
            closesocket(clientSocket);
            return false;
        }
        
        // Configure client socket
        SetSocketOptions(socketIndex, m_Config);
        SetSocketNonBlocking(clientSocket, true);
        
        // Store socket information
        m_Socket[socketIndex].m_Socket = clientSocket;
        m_Socket[socketIndex].m_Addr = clientAddr;
        m_Socket[socketIndex].m_State = ESocketState::CONNECTED;
        m_Socket[socketIndex].m_bAccept = true;
        m_Socket[socketIndex].m_bEnterCheck = false;
        m_Socket[socketIndex].m_dwConnectTime = timeGetTime();
        m_Socket[socketIndex].m_dwLastActivity = timeGetTime();
        m_Socket[socketIndex].m_szRemoteIP = clientIP;
        m_Socket[socketIndex].m_wRemotePort = ntohs(clientAddr.sin_port);
        
        // Update statistics
        m_TotalCount.dwAcceptNum++;
        m_TotalCount.dwConnectionCount++;
        
        Logger::LogInfo("Accepted connection from " + std::string(clientIP) + 
                       " on socket " + std::to_string(socketIndex));
        return true;
        
    } catch (const std::exception& e) {
        m_LastErrorMessage = "AcceptConnection failed: " + std::string(e.what());
        m_LastError = ENetworkError::CONNECTION_FAILED;
        Logger::LogError("CNetSocket::AcceptConnection failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Send data
 */
bool CNetSocket::Send(uint32_t socketIndex, const char* pBuf, int nSize, int* pnRet) {
    try {
        if (!ValidateSocketIndex(socketIndex) || !pBuf || nSize <= 0) {
            m_LastErrorMessage = "Invalid parameters for Send";
            m_LastError = ENetworkError::INVALID_PARAMETER;
            if (pnRet) *pnRet = 0;
            return false;
        }

        if (m_Socket[socketIndex].m_Socket == INVALID_SOCKET) {
            m_LastErrorMessage = "Socket not connected";
            m_LastError = ENetworkError::CONNECTION_FAILED;
            if (pnRet) *pnRet = 0;
            return false;
        }

        int bytesSent = send(m_Socket[socketIndex].m_Socket, pBuf, nSize, 0);

        if (bytesSent == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error == WSAEWOULDBLOCK) {
                m_Socket[socketIndex].m_dwTotalSendBlock++;
                m_TotalCount.dwTotalSendBlockNum++;
                if (pnRet) *pnRet = 0;
                return true;  // Not an error, just would block
            } else {
                HandleSocketError(socketIndex, "send", error);
                m_TotalCount.dwTotalSendErrNum++;
                if (pnRet) *pnRet = 0;
                return false;
            }
        }

        // Update statistics and activity
        UpdateSocketActivity(socketIndex);
        m_TotalCount.dwTotalSendNum++;
        UpdateStats("send", true, bytesSent);

        if (pnRet) *pnRet = bytesSent;
        return true;

    } catch (const std::exception& e) {
        m_LastErrorMessage = "Send failed: " + std::string(e.what());
        m_LastError = ENetworkError::SEND_FAILED;
        Logger::LogError("CNetSocket::Send failed: " + std::string(e.what()));
        if (pnRet) *pnRet = 0;
        return false;
    }
}

/**
 * Receive data
 */
bool CNetSocket::Recv(uint32_t socketIndex, char* pBuf, int nBufMaxSize, int* pnRet) {
    try {
        if (!ValidateSocketIndex(socketIndex) || !pBuf || nBufMaxSize <= 0) {
            m_LastErrorMessage = "Invalid parameters for Recv";
            m_LastError = ENetworkError::INVALID_PARAMETER;
            if (pnRet) *pnRet = 0;
            return false;
        }

        if (m_Socket[socketIndex].m_Socket == INVALID_SOCKET) {
            m_LastErrorMessage = "Socket not connected";
            m_LastError = ENetworkError::CONNECTION_FAILED;
            if (pnRet) *pnRet = 0;
            return false;
        }

        int bytesReceived = recv(m_Socket[socketIndex].m_Socket, pBuf, nBufMaxSize, 0);

        if (bytesReceived == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error == WSAEWOULDBLOCK) {
                m_Socket[socketIndex].m_dwTotalRecvBlock++;
                m_TotalCount.dwTotalRecvBlockNum++;
                if (pnRet) *pnRet = 0;
                return true;  // Not an error, just would block
            } else {
                HandleSocketError(socketIndex, "recv", error);
                m_TotalCount.dwTotalRecvErrNum++;
                if (pnRet) *pnRet = 0;
                return false;
            }
        } else if (bytesReceived == 0) {
            // Connection closed by peer
            Logger::LogInfo("Connection closed by peer on socket " + std::to_string(socketIndex));
            CloseSocket(socketIndex);
            if (pnRet) *pnRet = 0;
            return false;
        }

        // Update statistics and activity
        UpdateSocketActivity(socketIndex);
        m_TotalCount.dwTotalRecvNum++;
        UpdateStats("recv", true, bytesReceived);

        if (pnRet) *pnRet = bytesReceived;
        return true;

    } catch (const std::exception& e) {
        m_LastErrorMessage = "Recv failed: " + std::string(e.what());
        m_LastError = ENetworkError::RECV_FAILED;
        Logger::LogError("CNetSocket::Recv failed: " + std::string(e.what()));
        if (pnRet) *pnRet = 0;
        return false;
    }
}

/**
 * Close socket
 */
bool CNetSocket::CloseSocket(uint32_t socketIndex) {
    std::lock_guard<std::mutex> lock(m_SocketMutex);

    try {
        if (!ValidateSocketIndex(socketIndex)) {
            return false;
        }

        if (m_Socket[socketIndex].m_Socket != INVALID_SOCKET) {
            // Graceful shutdown
            shutdown(m_Socket[socketIndex].m_Socket, SD_BOTH);
            closesocket(m_Socket[socketIndex].m_Socket);

            Logger::LogInfo("Closed socket " + std::to_string(socketIndex) +
                           " (" + m_Socket[socketIndex].m_szRemoteIP + ")");

            // Update statistics
            m_TotalCount.dwDisconnectionCount++;
        }

        // Reset socket information
        m_Socket[socketIndex].InitParam();

        return true;

    } catch (const std::exception& e) {
        Logger::LogError("CNetSocket::CloseSocket failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Initialize Winsock
 */
bool CNetSocket::InitializeWinsock() {
    try {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);

        if (result != 0) {
            m_LastErrorMessage = "WSAStartup failed with error: " + std::to_string(result);
            m_LastError = ENetworkError::NETWORK_DOWN;
            return false;
        }

        // Verify Winsock version
        if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2) {
            m_LastErrorMessage = "Winsock 2.2 not supported";
            m_LastError = ENetworkError::NETWORK_DOWN;
            WSACleanup();
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        m_LastErrorMessage = "InitializeWinsock failed: " + std::string(e.what());
        m_LastError = ENetworkError::NETWORK_DOWN;
        return false;
    }
}

/**
 * Cleanup Winsock
 */
void CNetSocket::CleanupWinsock() {
    try {
        WSACleanup();

    } catch (const std::exception& e) {
        Logger::LogError("CleanupWinsock error: " + std::string(e.what()));
    }
}

/**
 * Create socket with options
 */
SOCKET CNetSocket::CreateSocketWithOptions(int socketType, int protocol) {
    try {
        SOCKET sock = socket(AF_INET, socketType, protocol);

        if (sock == INVALID_SOCKET) {
            int error = WSAGetLastError();
            m_LastErrorMessage = "Failed to create socket: " + GetSocketErrorMessage(error);
            m_LastError = ENetworkError::SOCKET_ERROR;
            return INVALID_SOCKET;
        }

        return sock;

    } catch (const std::exception& e) {
        m_LastErrorMessage = "CreateSocketWithOptions failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        return INVALID_SOCKET;
    }
}

/**
 * Set socket non-blocking
 */
bool CNetSocket::SetSocketNonBlocking(SOCKET socket, bool nonBlocking) {
    try {
        u_long mode = nonBlocking ? 1 : 0;
        int result = ioctlsocket(socket, FIONBIO, &mode);

        if (result == SOCKET_ERROR) {
            int error = WSAGetLastError();
            m_LastErrorMessage = "Failed to set non-blocking mode: " + GetSocketErrorMessage(error);
            m_LastError = ENetworkError::SOCKET_ERROR;
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        m_LastErrorMessage = "SetSocketNonBlocking failed: " + std::string(e.what());
        m_LastError = ENetworkError::SOCKET_ERROR;
        return false;
    }
}

/**
 * Update socket activity
 */
void CNetSocket::UpdateSocketActivity(uint32_t socketIndex) {
    if (ValidateSocketIndex(socketIndex)) {
        m_Socket[socketIndex].m_dwLastActivity = timeGetTime();
    }
}

/**
 * Handle socket error
 */
void CNetSocket::HandleSocketError(uint32_t socketIndex, const std::string& operation, int errorCode) {
    try {
        std::string errorMsg = operation + " failed on socket " + std::to_string(socketIndex) +
                              ": " + GetSocketErrorMessage(errorCode);

        m_LastErrorMessage = errorMsg;
        m_LastError = ENetworkError::SOCKET_ERROR;

        Logger::LogError(errorMsg);

        // Close socket on critical errors
        if (errorCode == WSAECONNRESET || errorCode == WSAECONNABORTED ||
            errorCode == WSAENETDOWN || errorCode == WSAENETRESET) {
            CloseSocket(socketIndex);
        }

    } catch (const std::exception& e) {
        Logger::LogError("HandleSocketError failed: " + std::string(e.what()));
    }
}

/**
 * Validate socket index
 */
bool CNetSocket::ValidateSocketIndex(uint32_t socketIndex) const {
    return (socketIndex < m_dwMaxSockets);
}

/**
 * Get socket error message
 */
std::string CNetSocket::GetSocketErrorMessage(int errorCode) const {
    switch (errorCode) {
        case WSAECONNRESET: return "Connection reset by peer";
        case WSAECONNABORTED: return "Connection aborted";
        case WSAECONNREFUSED: return "Connection refused";
        case WSAENETDOWN: return "Network is down";
        case WSAENETRESET: return "Network dropped connection on reset";
        case WSAETIMEDOUT: return "Connection timed out";
        case WSAEWOULDBLOCK: return "Resource temporarily unavailable";
        case WSAEINVAL: return "Invalid argument";
        case WSAENOTSOCK: return "Socket operation on non-socket";
        case WSAEADDRINUSE: return "Address already in use";
        case WSAEADDRNOTAVAIL: return "Cannot assign requested address";
        default: return "Unknown error (" + std::to_string(errorCode) + ")";
    }
}

/**
 * Update statistics
 */
void CNetSocket::UpdateStats(const std::string& operation, bool success, uint32_t bytes) {
    std::lock_guard<std::mutex> lock(m_StatsMutex);

    try {
        // Statistics are updated in the calling methods
        // This method can be extended for more detailed tracking

    } catch (const std::exception& e) {
        Logger::LogError("UpdateStats failed: " + std::string(e.what()));
    }
}

/**
 * Check IP address against filter
 */
bool CNetSocket::CheckIPAddress(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(m_IPCheckMutex);

    try {
        if (!m_bIPCheckEnabled) {
            return true;  // No filtering enabled
        }

        // This would check against the IP filter list
        // For now, allow all IPs
        return true;

    } catch (const std::exception& e) {
        Logger::LogError("CheckIPAddress failed: " + std::string(e.what()));
        return false;  // Deny on error
    }
}
