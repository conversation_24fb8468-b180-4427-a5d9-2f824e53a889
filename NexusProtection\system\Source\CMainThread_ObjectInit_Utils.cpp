/**
 * @file CMainThread_ObjectInit_Utils.cpp
 * @brief Utility functions for CMainThread object initialization
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_ObjectInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>

/**
 * @brief Get object initialization statistics
 * @return const reference to initialization statistics
 */
const ObjectInitStats& CMainThreadObjectInit::GetInitializationStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_initStats;
}

/**
 * @brief Check if a specific object type is initialized
 * @param objectType The object type to check
 * @return true if initialized, false otherwise
 */
bool CMainThreadObjectInit::IsObjectInitialized(GameObjectType objectType) const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    size_t index = static_cast<size_t>(objectType);
    if (index >= m_initStats.objectStatus.size()) {
        return false;
    }
    return m_initStats.objectStatus[index];
}

/**
 * @brief Get the last initialization error message
 * @return string containing the last error message
 */
std::string CMainThreadObjectInit::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get information about a specific object type
 * @param objectType The object type
 * @return ObjectPoolInfo structure with object information
 */
ObjectPoolInfo CMainThreadObjectInit::GetObjectInfo(GameObjectType objectType) {
    auto it = s_objectRegistry.find(objectType);
    if (it != s_objectRegistry.end()) {
        return it->second;
    }
    return ObjectPoolInfo("unknown", "Unknown object type", 0, 0, 0, false);
}

/**
 * @brief Validate all initialized objects
 * @return true if all required objects are initialized and valid
 */
bool CMainThreadObjectInit::ValidateInitializedObjects() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    // Check all required objects
    for (const auto& [objectType, objectInfo] : s_objectRegistry) {
        if (objectInfo.isRequired) {
            size_t index = static_cast<size_t>(objectType);
            if (index >= m_initStats.objectStatus.size() || !m_initStats.objectStatus[index]) {
                return false;
            }
        }
    }
    
    return true;
}

/**
 * @brief Log object initialization status
 * @param objectType The object type
 * @param success Whether initialization was successful
 * @param count Number of objects initialized
 * @param memorySize Memory allocated for objects
 * @param errorMsg Error message if failed
 */
void CMainThreadObjectInit::LogObjectInitialization(GameObjectType objectType, bool success, 
                                                    uint32_t count, size_t memorySize, 
                                                    const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(objectType);
    if (index >= m_initStats.objectStatus.size()) {
        return;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_initStats.startTime);
    
    m_initStats.objectStatus[index] = success;
    m_initStats.objectInitTime[index] = duration;
    m_initStats.objectCount[index] = count;
    m_initStats.memoryAllocated[index] = memorySize;
    
    if (success) {
        m_initStats.successfulObjects++;
        
        std::cout << std::format("[INFO] {} initialized successfully: {} objects, {} KB in {}ms", 
                                GameObjectTypeToString(objectType), count, 
                                memorySize / 1024, duration.count()) << std::endl;
    } else {
        m_initStats.failedObjects++;
        std::string fullError = std::format("{} failed: {}", 
                                          GameObjectTypeToString(objectType), errorMsg);
        m_initStats.lastError = fullError;
        std::cerr << "[ERROR] " << fullError << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message to set
 */
void CMainThreadObjectInit::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    std::cerr << "[ERROR] " << error << std::endl;
}

/**
 * @brief Create object ID for game objects
 * @param pID Pointer to object ID structure
 * @param objectType Object type identifier
 * @param index Object index
 */
void CMainThreadObjectInit::CreateObjectID(_object_id* pID, uint8_t objectType, uint32_t index) {
    // Call legacy object ID constructor (equivalent to original _object_id::_object_id(&pID, 0, objectType, index))
    ObjectID_Constructor(pID, 0, objectType, index);
}

/**
 * @brief Legacy object ID creation for compatibility
 * @param pID Pointer to object ID structure
 * @param type1 First type parameter
 * @param type2 Second type parameter
 * @param index Object index
 */
void CMainThreadObjectInit::InitializeObjectID_Legacy(_object_id* pID, uint8_t type1, uint8_t type2, uint32_t index) {
    ObjectID_Constructor(pID, type1, type2, index);
}

/**
 * @brief Template function for allocating object arrays
 * @tparam T Object type
 * @param count Number of objects to allocate
 * @param objectType Object type for logging
 * @return Pointer to allocated array or nullptr on failure
 */
template<typename T>
T* CMainThreadObjectInit::AllocateObjectArray(uint32_t count, GameObjectType objectType) {
    try {
        size_t totalSize = 4 + (sizeof(T) * count); // 4 bytes for count + objects
        void* memory = operator new[](totalSize);
        
        if (!memory) {
            LogObjectInitialization(objectType, false, 0, 0, "Memory allocation failed");
            return nullptr;
        }
        
        // Set count and return object array pointer
        *static_cast<uint32_t*>(memory) = count;
        return static_cast<T*>(static_cast<char*>(memory) + 8);
        
    } catch (const std::exception& e) {
        LogObjectInitialization(objectType, false, 0, 0, e.what());
        return nullptr;
    }
}

/**
 * @brief Convert GameObjectType enum to string for logging
 * @param objectType The object type
 * @return String representation of the object type
 */
std::string GameObjectTypeToString(GameObjectType objectType) {
    switch (objectType) {
        case GameObjectType::UserDatabase: return "UserDatabase";
        case GameObjectType::PartyPlayer: return "PartyPlayer";
        case GameObjectType::Player: return "Player";
        case GameObjectType::Monster: return "Monster";
        case GameObjectType::Merchant: return "Merchant";
        case GameObjectType::Animus: return "Animus";
        case GameObjectType::GuardTower: return "GuardTower";
        case GameObjectType::HolyStone: return "HolyStone";
        case GameObjectType::HolyKeeper: return "HolyKeeper";
        case GameObjectType::Trap: return "Trap";
        case GameObjectType::ItemBox: return "ItemBox";
        case GameObjectType::ParkingUnit: return "ParkingUnit";
        case GameObjectType::DarkHole: return "DarkHole";
        case GameObjectType::Guild: return "Guild";
        case GameObjectType::RFEventBase: return "RFEventBase";
        case GameObjectType::RFEventClassRefine: return "RFEventClassRefine";
        case GameObjectType::RaceBossMsgController: return "RaceBossMsgController";
        case GameObjectType::ReturnGateController: return "ReturnGateController";
        case GameObjectType::RecallEffectController: return "RecallEffectController";
        case GameObjectType::LendItemManager: return "LendItemManager";
        case GameObjectType::NuclearBombManager: return "NuclearBombManager";
        case GameObjectType::TimeLimitJadeManager: return "TimeLimitJadeManager";
        case GameObjectType::PvpCashManager: return "PvpCashManager";
        case GameObjectType::MonsterEventSet: return "MonsterEventSet";
        case GameObjectType::DfAIManager: return "DfAIManager";
        default: return "Unknown";
    }
}

/**
 * @brief Convert ObjectInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string ObjectInitResultToString(ObjectInitResult result) {
    switch (result) {
        case ObjectInitResult::Success: return "Success";
        case ObjectInitResult::Failure: return "Failure";
        case ObjectInitResult::MemoryAllocationFailed: return "MemoryAllocationFailed";
        case ObjectInitResult::InvalidParameters: return "InvalidParameters";
        case ObjectInitResult::DependencyError: return "DependencyError";
        case ObjectInitResult::InitializationError: return "InitializationError";
        case ObjectInitResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief Initialize Merchant/NPC objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializeMerchants() {
    try {
        LogObjectInitialization(GameObjectType::Merchant, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::Merchant);
        
        // Allocate memory for merchants (equivalent to original operator new[])
        g_NPC = AllocateObjectArray<CMerchant>(objectInfo.maxCount, GameObjectType::Merchant);
        if (!g_NPC) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all Merchant objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id merchantID;
            CreateObjectID(&merchantID, objectInfo.objectType, index);
            
            if (!CMerchant_Init(&g_NPC[index], &merchantID)) {
                LogObjectInitialization(GameObjectType::Merchant, false, 0, 0, 
                                       std::format("Failed to initialize merchant {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::Merchant, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Merchant, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize Animus objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializeAnimus() {
    try {
        LogObjectInitialization(GameObjectType::Animus, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::Animus);
        
        // Allocate memory for animus objects
        g_Animus = AllocateObjectArray<CAnimus>(objectInfo.maxCount, GameObjectType::Animus);
        if (!g_Animus) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all Animus objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id animusID;
            CreateObjectID(&animusID, objectInfo.objectType, index);
            
            if (!CAnimus_Init(&g_Animus[index], &animusID)) {
                LogObjectInitialization(GameObjectType::Animus, false, 0, 0, 
                                       std::format("Failed to initialize animus {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::Animus, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Animus, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}
