/*
 * DfAIMgr.h - Default AI Manager System
 * Refactored for Visual Studio 2022 compatibility
 * Original: Various DfAIMgr functions from decompiled source
 */

#pragma once

#include <cstdint>
#include <memory>
#include <chrono>
#include <vector>
#include <unordered_map>

// Forward declarations
class CMonsterAI;
class CMonster;
class CCharacter;
class CMonsterSkill;
class UsStateTBL;
class Us_HFSM;
class SF_Timer;

/**
 * AI attack generation result
 */
enum class AIAttackResult : int32_t {
    NoAttack = 0,           // No attack generated
    AttackExecuted = 1,     // Attack was executed successfully
    TargetNotFound = -1,    // No valid target found
    OutOfRange = -2,        // Target out of attack range
    SkillNotReady = -3,     // Skill not ready for use
    InvalidParameters = -4   // Invalid input parameters
};

/**
 * AI target case types for wisdom targeting
 */
enum class AITargetCaseType : int32_t {
    AttackTarget = 0,       // Use current attack target
    Self = 1,               // Target self (monster)
    AssistMonster = 2,      // Target assist monster
    Invalid = -1            // Invalid target type
};

/**
 * AI attack generation parameters
 */
struct AIAttackParams {
    float distanceThreshold;        // Distance threshold for attack
    float heightThreshold;          // Height difference threshold
    float movementPrediction;       // Movement prediction factor
    float skillDelayMultiplier;     // Skill delay multiplier
    bool enableMovementPrediction;  // Enable movement prediction
    bool enableRangeChecking;       // Enable range checking
    
    AIAttackParams() 
        : distanceThreshold(50.0f)
        , heightThreshold(200.0f)
        , movementPrediction(0.4f)
        , skillDelayMultiplier(15.0f)
        , enableMovementPrediction(true)
        , enableRangeChecking(true) {}
};

/**
 * AI attack context for tracking attack state
 */
struct AIAttackContext {
    CCharacter* target;             // Current target
    float targetDistance;           // Distance to target
    float heightDifference;         // Height difference to target
    float adjustedDistance;         // Distance adjusted for movement
    uint32_t currentTime;           // Current loop time
    bool targetIsMoving;            // Whether target is moving
    bool inAttackRange;             // Whether target is in attack range
    
    AIAttackContext() 
        : target(nullptr), targetDistance(0.0f), heightDifference(0.0f)
        , adjustedDistance(0.0f), currentTime(0), targetIsMoving(false)
        , inAttackRange(false) {}
    
    void Reset() {
        target = nullptr;
        targetDistance = 0.0f;
        heightDifference = 0.0f;
        adjustedDistance = 0.0f;
        currentTime = 0;
        targetIsMoving = false;
        inAttackRange = false;
    }
};

/**
 * AI skill evaluation result
 */
struct AISkillEvaluation {
    CMonsterSkill* skill;           // Evaluated skill
    float skillDelay;               // Skill delay time
    float timeSinceLastUse;         // Time since skill was last used
    float attackDistance;           // Attack distance for skill
    bool isReady;                   // Whether skill is ready to use
    bool meetsConditions;           // Whether skill meets all conditions
    
    AISkillEvaluation() 
        : skill(nullptr), skillDelay(0.0f), timeSinceLastUse(0.0f)
        , attackDistance(0.0f), isReady(false), meetsConditions(false) {}
};

/**
 * Default AI Manager class - handles monster AI attack generation
 */
class DfAIMgr {
public:
    // Constants
    static constexpr int MAX_MONSTER_SKILLS = 16;
    static constexpr float DEFAULT_HEIGHT_THRESHOLD = 200.0f;
    static constexpr float DEFAULT_CLOSE_RANGE = 50.0f;
    static constexpr float MOVEMENT_PREDICTION_FACTOR = 0.4f;
    static constexpr float TIME_MULTIPLIER = 15.0f;
    
    /**
     * Check if monster can generate an attack
     * Refactored from CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.c
     * 
     * @param pAI Pointer to monster AI
     * @param pMonster Pointer to monster
     * @param distanceThreshold Distance threshold for attack consideration
     * @return AIAttackResult indicating the result of attack generation
     */
    static AIAttackResult CheckGen(CMonsterAI* pAI, CMonster* pMonster, 
                                  float distanceThreshold = DEFAULT_CLOSE_RANGE);
    
    /**
     * Get wisdom target based on case type
     * Refactored from GetWisdomTargetDfAIMgrSAPEAVCCharacterHPEAVCMonste_140152570.c
     * 
     * @param targetCaseType Type of target to get
     * @param pAI Pointer to monster AI
     * @param pMonster Pointer to monster
     * @return Pointer to target character, or nullptr if not found
     */
    static CCharacter* GetWisdomTarget(AITargetCaseType targetCaseType, 
                                      CMonsterAI* pAI, CMonster* pMonster);
    
    /**
     * Check special function delay time
     * @param pAI Pointer to monster AI
     * @param attackType Attack type index
     * @param currentTime Current loop time
     * @return true if delay time has passed
     */
    static bool CheckSPFDelayTime(CMonsterAI* pAI, int attackType, uint32_t currentTime);
    
    /**
     * Initialize HFSM (Hierarchical Finite State Machine)
     * @param pStateTBL Pointer to state table
     * @param pHFSM Pointer to HFSM
     * @return true if initialization successful
     */
    static bool OnDFInitHFSM(UsStateTBL* pStateTBL, Us_HFSM* pHFSM);
    
    /**
     * Set AI attack parameters
     * @param params New attack parameters
     */
    static void SetAttackParams(const AIAttackParams& params);
    
    /**
     * Get current AI attack parameters
     * @return Current attack parameters
     */
    static const AIAttackParams& GetAttackParams();
    
    /**
     * Enable/disable AI debugging
     * @param enable Whether to enable debugging
     */
    static void SetDebugging(bool enable);
    
    /**
     * Get AI statistics
     * @return Map of statistic names to values
     */
    static std::unordered_map<std::string, uint64_t> GetStatistics();
    
private:
    // Static configuration
    static AIAttackParams s_attackParams;
    static bool s_debuggingEnabled;
    
    // Statistics tracking
    static uint64_t s_totalAttackChecks;
    static uint64_t s_successfulAttacks;
    static uint64_t s_failedAttacks;
    static uint64_t s_targetsNotFound;
    static uint64_t s_outOfRangeAttacks;
    static uint64_t s_skillsNotReady;
    
    /**
     * Validate input parameters for attack generation
     * @param pAI Pointer to monster AI
     * @param pMonster Pointer to monster
     * @return true if parameters are valid
     */
    static bool ValidateInputParameters(CMonsterAI* pAI, CMonster* pMonster);
    
    /**
     * Initialize attack context
     * @param pMonster Pointer to monster
     * @param target Pointer to target
     * @param distanceThreshold Distance threshold
     * @param context Output attack context
     * @return true if context initialized successfully
     */
    static bool InitializeAttackContext(CMonster* pMonster, CCharacter* target,
                                       float distanceThreshold, AIAttackContext& context);
    
    /**
     * Check if target is in valid range
     * @param context Attack context
     * @return true if target is in valid range
     */
    static bool IsTargetInValidRange(const AIAttackContext& context);
    
    /**
     * Apply movement prediction to distance calculation
     * @param pMonster Pointer to monster
     * @param context Attack context (modified)
     */
    static void ApplyMovementPrediction(CMonster* pMonster, AIAttackContext& context);
    
    /**
     * Evaluate monster skill for attack
     * @param pMonster Pointer to monster
     * @param skill Pointer to skill
     * @param context Attack context
     * @param evaluation Output skill evaluation
     * @return true if skill evaluation successful
     */
    static bool EvaluateSkill(CMonster* pMonster, CMonsterSkill* skill,
                             const AIAttackContext& context, AISkillEvaluation& evaluation);
    
    /**
     * Check if skill meets attack conditions
     * @param skill Pointer to skill
     * @param evaluation Skill evaluation data
     * @return true if skill meets conditions
     */
    static bool SkillMeetsConditions(CMonsterSkill* skill, const AISkillEvaluation& evaluation);
    
    /**
     * Execute attack with skill
     * @param pMonster Pointer to monster
     * @param target Pointer to target
     * @param skill Pointer to skill
     * @return AIAttackResult indicating attack result
     */
    static AIAttackResult ExecuteAttack(CMonster* pMonster, CCharacter* target, CMonsterSkill* skill);

    /**
     * Evaluate skills for attack
     * @param pMonster Pointer to monster
     * @param context Attack context
     * @return AIAttackResult indicating evaluation result
     */
    static AIAttackResult EvaluateSkillsForAttack(CMonster* pMonster, const AIAttackContext& context);

    /**
     * Check skill timing conditions
     * @param evaluation Skill evaluation data
     * @param context Attack context
     * @return true if timing conditions are met
     */
    static bool CheckSkillTiming(const AISkillEvaluation& evaluation, const AIAttackContext& context);
    
    /**
     * Update statistics
     * @param result Attack result
     */
    static void UpdateStatistics(AIAttackResult result);
    
    /**
     * Log AI operation
     * @param operation Operation description
     * @param pMonster Pointer to monster (optional)
     * @param result Operation result
     */
    static void LogAIOperation(const std::string& operation, 
                              CMonster* pMonster = nullptr, 
                              AIAttackResult result = AIAttackResult::NoAttack);
    
    /**
     * Log debug information
     * @param message Debug message
     * @param context Attack context (optional)
     */
    static void LogDebug(const std::string& message, 
                        const AIAttackContext* context = nullptr);
};

/**
 * AI utility functions
 */
namespace AIUtils {
    /**
     * Convert AIAttackResult to string
     * @param result Attack result
     * @return String representation
     */
    std::string AttackResultToString(AIAttackResult result);
    
    /**
     * Convert AITargetCaseType to string
     * @param caseType Target case type
     * @return String representation
     */
    std::string TargetCaseTypeToString(AITargetCaseType caseType);
    
    /**
     * Calculate 3D distance between positions
     * @param pos1 First position
     * @param pos2 Second position
     * @return Distance between positions
     */
    float Calculate3DDistance(const float pos1[3], const float pos2[3]);
    
    /**
     * Calculate height difference between positions
     * @param pos1 First position
     * @param pos2 Second position
     * @return Height difference (absolute value)
     */
    float CalculateHeightDifference(const float pos1[3], const float pos2[3]);
    
    /**
     * Validate position array
     * @param position Position to validate
     * @return true if position is valid
     */
    bool IsValidPosition(const float position[3]);
    
    /**
     * Format attack context for logging
     * @param context Attack context
     * @return Formatted string
     */
    std::string FormatAttackContext(const AIAttackContext& context);
    
    /**
     * Format skill evaluation for logging
     * @param evaluation Skill evaluation
     * @return Formatted string
     */
    std::string FormatSkillEvaluation(const AISkillEvaluation& evaluation);
}

#endif // DFAIMGR_H
