/**
 * @file CMainThread.h
 * @brief Main thread system initialization and management
 * 
 * Refactored from InitCMainThreadQEAA_NXZ_1401E4630.c
 * This is the core system initialization class that manages all major game systems
 * including logging, guilds, ranking, billing, security, and 80+ other components.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <chrono>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <filesystem>

// Forward declarations for system components
class CRtc;
class CAsyncLogger;
class CTotalGuildRankManager;
class CWeeklyGuildRankManager;
class CandidateMgr;
class PatriarchElectProcessor;
class AutominePersonalMgr;
class CGuildRoomSystem;
class CUnmannedTraderController;
class CLogTypeDBTaskManager;
class TimeItem;
class CCashDBWorkManager;
class CashItemRemoteStore;
class CPostSystemManager;
class CPvpUserAndGuildRankingSystem;
class CMoveMapLimitManager;
class CRaceBuffManager;
class CHonorGuild;
class CPcBangFavor;
class CActionPointSystemMgr;
class CLuaScriptMgr;
class CCryptor;
class CBossMonsterScheduleSystem;
class cStaticMember_Player;
class CBillingManager;
class CGoldenBoxItemMgr;
class CExchangeEvent;
class CGuildBattleController;
class TimeLimitMgr;
class CLogFile;
class CMyTimer;
class CMsgData;
class CConnNumPHMgr;
class GuildCreateEventInfo;
class CNotifyNotifyRaceLeaderSownerUTaxrate;

// Legacy structures for compatibility
struct WheatyExceptionReport;
struct CMapOperation;
struct CMonsterEventRespawn;
struct CMonsterEventSet;
struct CDarkHoleDungeonQuest;

/**
 * @enum InitializationResult
 * @brief Result codes for system initialization
 */
enum class InitializationResult : int32_t {
    Success = 1,
    Failure = 0,
    InvalidParameters = -1,
    SystemNotReady = -2,
    NetworkError = -3,
    DatabaseError = -4,
    FileSystemError = -5,
    MemoryError = -6,
    SecurityError = -7,
    ThreadError = -8
};

/**
 * @enum SystemComponent
 * @brief Enumeration of all system components for initialization tracking
 */
enum class SystemComponent : uint32_t {
    // Core Systems
    ExceptionReporting = 0,
    LoggingSystem,
    FileSystem,
    
    // Configuration
    INILoader,
    DefineChecker,
    DatabaseSizeChecker,
    
    // Data Systems
    DataFileInit,
    ObjectInit,
    NetworkInit,
    
    // Map Systems
    MapOperation,
    MonsterEventRespawn,
    MonsterEventSet,
    
    // Async Systems
    AsyncLogger,
    TimeLimitManager,
    
    // Quest Systems
    DarkHoleDungeonQuest,
    
    // Guild Systems
    GuildBattleController,
    TotalGuildRankManager,
    WeeklyGuildRankManager,
    GuildRoomSystem,
    HonorGuild,
    
    // Management Systems
    CandidateManager,
    PatriarchElectProcessor,
    AutominePersonalManager,
    UnmannedTraderController,
    LogTypeDBTaskManager,
    
    // Item Systems
    TimeItem,
    CashDBWorkManager,
    CashItemRemoteStore,
    GoldenBoxItemManager,
    
    // Communication Systems
    PostSystemManager,
    
    // PvP Systems
    PvpUserAndGuildRankingSystem,
    MoveMapLimitManager,
    RaceBuffManager,
    
    // Event Systems
    ExchangeEvent,
    PcBangFavor,
    ActionPointSystemManager,
    
    // Script Systems
    LuaScriptManager,
    
    // Security Systems
    Cryptor,
    
    // Monster Systems
    BossMonsterScheduleSystem,
    
    // Player Systems
    StaticMemberPlayer,
    
    // Billing Systems
    BillingManager,
    
    // Threading
    RuleThread,
    DQSThread,
    
    // Performance Monitoring
    ConnectionMonitoring,
    
    // System Towers
    SystemTowers,
    
    // Network Agents
    WebAgentServer,
    ControllServer,
    
    // Final Configuration
    CheatDetection,
    
    MAX_COMPONENTS
};

/**
 * @struct SystemInitializationStats
 * @brief Statistics for system initialization process
 */
struct SystemInitializationStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(SystemComponent::MAX_COMPONENTS)> componentStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(SystemComponent::MAX_COMPONENTS)> componentInitTime;
    uint32_t successfulComponents;
    uint32_t failedComponents;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        componentStatus.fill(false);
        componentInitTime.fill(std::chrono::milliseconds::zero());
        successfulComponents = 0;
        failedComponents = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalInitTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulComponents + failedComponents;
        return total > 0 ? (static_cast<double>(successfulComponents) / total) * 100.0 : 0.0;
    }
};

/**
 * @class CMainThread
 * @brief Main thread system initialization and management class
 *
 * This class is responsible for initializing all major game systems in the correct order.
 * It manages the startup sequence, error handling, logging, and system monitoring.
 *
 * Original function: CMainThread::Init (Address: 0x1401E4630)
 * Refactored to modern C++20 with comprehensive error handling and monitoring.
 */
class CMainThread {
public:
    /**
     * @brief Constructor
     */
    CMainThread();

    /**
     * @brief Destructor
     */
    ~CMainThread();

    // Delete copy constructor and assignment operator
    CMainThread(const CMainThread&) = delete;
    CMainThread& operator=(const CMainThread&) = delete;

    // Allow move constructor and assignment operator
    CMainThread(CMainThread&&) = default;
    CMainThread& operator=(CMainThread&&) = default;

    /**
     * @brief Main system initialization function
     *
     * Initializes all major game systems in the correct order.
     * This is the modern refactored version of the original Init function.
     *
     * @return InitializationResult indicating success or failure
     */
    InitializationResult Init();

    /**
     * @brief Legacy Init function for backward compatibility
     *
     * Maintains the original function signature for existing code.
     * Original: char __fastcall CMainThread::Init(CMainThread *this)
     *
     * @return char (1 for success, 0 for failure)
     */
    char Init_Legacy();

    /**
     * @brief Get initialization statistics
     * @return const reference to initialization statistics
     */
    const SystemInitializationStats& GetInitializationStats() const;

    /**
     * @brief Check if a specific system component is initialized
     * @param component The system component to check
     * @return true if initialized, false otherwise
     */
    bool IsComponentInitialized(SystemComponent component) const;

    /**
     * @brief Get the last initialization error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;

    /**
     * @brief Force shutdown of all systems
     */
    void Shutdown();

    /**
     * @brief Check if the system is fully initialized
     * @return true if all critical systems are initialized
     */
    bool IsFullyInitialized() const;

private:
    // System state
    std::atomic<bool> m_bWorldOpen{false};
    std::atomic<bool> m_bWorldService{false};
    std::atomic<bool> m_bCheckOverTickCount{false};
    std::atomic<bool> m_bRuleThread{false};
    std::atomic<bool> m_bDQSThread{false};

    // Network state
    std::atomic<uint8_t> m_byWebAgentServerNetInx{0};
    std::atomic<bool> m_bConnectedWebAgentServer{false};
    std::atomic<uint8_t> m_byControllServerNetInx{0};
    std::atomic<bool> m_bConnectedControllServer{false};

    // Cheat detection settings
    std::atomic<uint32_t> m_dwCheatSetPlayTime{600};
    std::atomic<uint32_t> m_dwCheatSetScanerCnt{0};
    std::atomic<uint32_t> m_dwCheatSetLevel{50};
    std::atomic<uint32_t> m_dwServerResetToken{1990011};

    // Timing
    std::atomic<uint32_t> m_dwCheckAccountOldTick{0};

    // Statistics and monitoring
    SystemInitializationStats m_initStats;
    mutable std::mutex m_statsMutex;

    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;

    // System component pointers (managed externally)
    TimeLimitMgr* m_pTimeLimitMgr{nullptr};

    // Logging system (forward declared, will be properly defined in implementation)
    struct LoggingSystem;
    std::unique_ptr<LoggingSystem> m_loggingSystem;

    // Timer system (forward declared, will be properly defined in implementation)
    struct TimerSystem;
    std::unique_ptr<TimerSystem> m_timerSystem;

    // Message system (forward declared, will be properly defined in implementation)
    struct MessageSystem;
    std::unique_ptr<MessageSystem> m_messageSystem;

    // Performance monitoring (forward declared, will be properly defined in implementation)
    struct PerformanceMonitoring;
    std::unique_ptr<PerformanceMonitoring> m_performanceMonitoring;

    // Guild event info (forward declared, will be properly defined in implementation)
    struct GuildEventInfo;
    std::unique_ptr<GuildEventInfo> m_guildEventInfo;

    // Notification system (forward declared, will be properly defined in implementation)
    struct NotificationSystem;
    std::unique_ptr<NotificationSystem> m_notificationSystem;

private:
    // Core initialization phases
    InitializationResult InitializeExceptionReporting();
    InitializationResult InitializeFileSystem();
    InitializationResult InitializeLoggingSystem();
    InitializationResult InitializeAsyncLogging();

    // Configuration phases
    InitializationResult LoadINI();
    InitializationResult CheckDefine();
    InitializationResult CheckDatabaseSizeData();

    // Data initialization phases
    InitializationResult DataFileInit();
    InitializationResult ObjectInit();
    InitializationResult NetworkInit();

    // Map system phases
    InitializationResult InitializeMapOperation();
    InitializationResult InitializeMonsterEventSystems();

    // System manager phases
    InitializationResult InitializeSystemManagers();
    InitializationResult InitializeGuildSystems();
    InitializationResult InitializeItemSystems();
    InitializationResult InitializePvPSystems();
    InitializationResult InitializeEventSystems();
    InitializationResult InitializeScriptSystems();
    InitializationResult InitializeSecuritySystems();
    InitializationResult InitializeMonsterSystems();
    InitializationResult InitializePlayerSystems();
    InitializationResult InitializeBillingSystems();

    // Threading phases
    InitializationResult InitializeThreading();

    // Final phases
    InitializationResult InitializePerformanceMonitoring();
    InitializationResult InitializeSystemTowers();
    InitializationResult InitializeNetworkAgents();
    InitializationResult InitializeCheatDetection();

    // Utility functions
    void LogComponentInitialization(SystemComponent component, bool success, const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    bool CreateLogDirectories();
    void InitializeTimers();
    void StartBackgroundThreads();
    void WriteLog(const char* message);

    // Legacy compatibility functions
    static void RuleThread(void* pParam);
    static void DQSThread(void* pParam);

    // Thread management
    std::vector<std::thread> m_backgroundThreads;
    std::atomic<bool> m_shutdownRequested{false};

    // Security
    uint64_t m_securityCookie{0};
};
