#pragma once

#include <vector>
#include <array>
#include <memory>
#include <functional>
#include <unordered_map>

// Forward declarations
class <PERSON>haracter;
class CGameObject;
class CMapData;
class CObjectList;
struct _attack_param;
struct _sec_info;
struct _pnt_rect;

/**
 * Damage calculation utilities and constants
 */
namespace DamageConstants {
    constexpr int MAX_DAMAGE_TARGETS = 50;
    constexpr int MAX_SECTOR_ANGLE = 360;
    constexpr int DEFAULT_FLASH_ANGLE = 45;
    constexpr float CRITICAL_DAMAGE_MULTIPLIER = 1.5f;
    constexpr float DISTANCE_DAMAGE_FALLOFF = 0.8f;
    constexpr float ANGLE_DAMAGE_FALLOFF = 0.9f;
}

/**
 * Damage type enumeration
 */
enum class EDamageType : int {
    PHYSICAL = 0,
    MAGICAL = 1,
    ELEMENTAL = 2,
    PIERCING = 3,
    AREA = 4,
    FLASH = 5,
    SECTOR = 6
};

/**
 * Damage result structure
 */
struct DamageResult {
    <PERSON>haracter* pTarget;
    int nDamage;
    bool bCritical;
    bool bBlocked;
    bool bMissed;
    EDamageType damageType;
    float fDistance;
    float fAngle;
    
    DamageResult() 
        : pTarget(nullptr), nDamage(0), bCritical(false), 
          bBlocked(false), bMissed(false), damageType(EDamageType::PHYSICAL),
          fDistance(0.0f), fAngle(0.0f) {}
};

/**
 * Area damage parameters
 */
struct AreaDamageParams {
    int nLimitRadius;
    int nAttPower;
    std::array<float, 3> targetArea;
    int nEffAttPower;
    bool bUseEffBullet;
    bool bIgnoreAllies;
    float fDamageFalloff;
    
    AreaDamageParams() 
        : nLimitRadius(100), nAttPower(0), nEffAttPower(0),
          bUseEffBullet(false), bIgnoreAllies(true), fDamageFalloff(0.8f) {
        targetArea.fill(0.0f);
    }
};

/**
 * Flash damage parameters
 */
struct FlashDamageParams {
    int nLimDist;
    int nAttPower;
    int nAngle;
    int nEffAttPower;
    bool bUseEffBullet;
    bool bIgnoreAllies;
    float fAngleTolerance;
    
    FlashDamageParams() 
        : nLimDist(200), nAttPower(0), nAngle(45), nEffAttPower(0),
          bUseEffBullet(false), bIgnoreAllies(true), fAngleTolerance(5.0f) {}
};

/**
 * Sector damage parameters
 */
struct SectorDamageParams {
    int nLimitRadius;
    int nAttPower;
    int nAngle;
    int nEffAttPower;
    int nStartAngle;
    int nEndAngle;
    bool bUseEffBullet;
    bool bIgnoreAllies;
    float fDamageFalloff;
    
    SectorDamageParams() 
        : nLimitRadius(150), nAttPower(0), nAngle(0), nEffAttPower(0),
          nStartAngle(0), nEndAngle(90), bUseEffBullet(false), 
          bIgnoreAllies(true), fDamageFalloff(0.9f) {}
};

/**
 * CDamageProcessor - Advanced damage calculation and processing system
 */
class CDamageProcessor {
public:
    /**
     * Constructor
     * @param pAttacker Attacking character
     */
    explicit CDamageProcessor(CCharacter* pAttacker);
    
    /**
     * Destructor
     */
    virtual ~CDamageProcessor();
    
    /**
     * Process area damage
     * @param params Area damage parameters
     * @param pAttackParam Attack parameters
     * @return Vector of damage results
     */
    std::vector<DamageResult> ProcessAreaDamage(const AreaDamageParams& params, _attack_param* pAttackParam);
    
    /**
     * Process flash damage
     * @param params Flash damage parameters
     * @param pAttackParam Attack parameters
     * @return Vector of damage results
     */
    std::vector<DamageResult> ProcessFlashDamage(const FlashDamageParams& params, _attack_param* pAttackParam);
    
    /**
     * Process sector damage
     * @param params Sector damage parameters
     * @param pAttackParam Attack parameters
     * @return Vector of damage results
     */
    std::vector<DamageResult> ProcessSectorDamage(const SectorDamageParams& params, _attack_param* pAttackParam);
    
    /**
     * Calculate damage for a specific target
     * @param pTarget Target character
     * @param nBaseDamage Base damage amount
     * @param damageType Type of damage
     * @param fDistance Distance to target
     * @param fAngle Angle to target
     * @return Calculated damage result
     */
    DamageResult CalculateTargetDamage(CCharacter* pTarget, int nBaseDamage, EDamageType damageType, 
                                     float fDistance = 0.0f, float fAngle = 0.0f);
    
    /**
     * Apply damage to target
     * @param result Damage result to apply
     * @return true if damage was applied successfully
     */
    bool ApplyDamage(const DamageResult& result);
    
    /**
     * Get all potential targets in area
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param radius Search radius
     * @return Vector of potential targets
     */
    std::vector<CCharacter*> GetTargetsInArea(float centerX, float centerY, float radius);
    
    /**
     * Check if target is valid for damage
     * @param pTarget Target to check
     * @param bIgnoreAllies Whether to ignore allied targets
     * @return true if target is valid
     */
    bool IsValidTarget(CCharacter* pTarget, bool bIgnoreAllies = true) const;
    
    /**
     * Calculate distance between two points
     * @param x1 First point X
     * @param y1 First point Y
     * @param x2 Second point X
     * @param y2 Second point Y
     * @return Distance between points
     */
    static float CalculateDistance(float x1, float y1, float x2, float y2);
    
    /**
     * Calculate angle between two points
     * @param x1 First point X
     * @param y1 First point Y
     * @param x2 Second point X
     * @param y2 Second point Y
     * @return Angle in degrees
     */
    static float CalculateAngle(float x1, float y1, float x2, float y2);
    
    /**
     * Check if angle is within range
     * @param targetAngle Target angle to check
     * @param centerAngle Center angle
     * @param tolerance Angle tolerance
     * @return true if angle is within range
     */
    static bool IsAngleInRange(float targetAngle, float centerAngle, float tolerance);

protected:
    CCharacter* m_pAttacker;                    // Attacking character
    std::vector<DamageResult> m_damageResults;  // Damage results cache
    
    /**
     * Get map data for current area
     * @return Pointer to map data
     */
    CMapData* GetMapData() const;
    
    /**
     * Get sector information for coordinates
     * @param x X coordinate
     * @param y Y coordinate
     * @return Sector information
     */
    _sec_info* GetSectorInfo(float x, float y) const;
    
    /**
     * Apply damage falloff based on distance
     * @param baseDamage Base damage amount
     * @param distance Distance to target
     * @param maxDistance Maximum effective distance
     * @param falloffRate Damage falloff rate
     * @return Adjusted damage
     */
    int ApplyDistanceFalloff(int baseDamage, float distance, float maxDistance, float falloffRate) const;
    
    /**
     * Apply damage falloff based on angle
     * @param baseDamage Base damage amount
     * @param angle Angle difference
     * @param maxAngle Maximum effective angle
     * @param falloffRate Damage falloff rate
     * @return Adjusted damage
     */
    int ApplyAngleFalloff(int baseDamage, float angle, float maxAngle, float falloffRate) const;
    
    /**
     * Check for critical hit
     * @param pTarget Target character
     * @return true if critical hit
     */
    bool CheckCriticalHit(CCharacter* pTarget) const;
    
    /**
     * Check for damage block
     * @param pTarget Target character
     * @param damageType Type of damage
     * @return true if damage was blocked
     */
    bool CheckDamageBlock(CCharacter* pTarget, EDamageType damageType) const;
    
    /**
     * Check for damage miss
     * @param pTarget Target character
     * @return true if attack missed
     */
    bool CheckDamageMiss(CCharacter* pTarget) const;
};
