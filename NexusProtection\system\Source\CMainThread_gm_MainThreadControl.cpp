/**
 * @file CMainThread_gm_MainThreadControl.cpp
 * @brief Implementation of main thread control system for CMainThread
 * 
 * Refactored from gm_MainThreadControl function
 * This handles the main thread control and management system that manages
 * thread lifecycle, control commands, and thread synchronization for the main server thread.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_gm_MainThreadControl.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <algorithm>
#include <cassert>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External message box function
extern void MyMessageBox(const char* title, const char* message);

// Global thread control storage
std::unique_ptr<CMainThreadControl> g_MainThreadControl;
std::mutex g_MainThreadControlMutex;

/**
 * @brief Constructor
 */
CMainThreadControl::CMainThreadControl() {
    m_controlStats.Reset();
}

/**
 * @brief Destructor
 */
CMainThreadControl::~CMainThreadControl() {
    if (m_monitoringEnabled.load()) {
        StopMonitoring();
    }
}

/**
 * @brief Main thread control function
 * 
 * Initializes and starts the main thread control system.
 * This is the modern refactored version of the original gm_MainThreadControl function.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return ThreadControlResult indicating success or failure
 */
ThreadControlResult CMainThreadControl::InitializeMainThreadControl(CMainThread* mainThread) {
    try {
        m_controlStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting main thread control initialization..." << std::endl;
        
        if (!mainThread) {
            SetLastError("Invalid CMainThread pointer");
            return ThreadControlResult::SystemError;
        }
        
        m_mainThread = mainThread;
        ChangeState(ThreadState::Initializing);
        
        // Initialize control system
        auto result = InitializeControl();
        if (result != ThreadControlResult::Success) {
            ChangeState(ThreadState::Error);
            return result;
        }
        
        // Set initial thread flags and states
        InitializeThreadFlags(mainThread);
        
        // Enable world service and open world
        SetWorldServiceState(mainThread, true);
        SetWorldOpenState(mainThread, true);
        
        // Start monitoring if enabled
        if (m_controlConfig.enableMonitoring) {
            StartMonitoring();
        }
        
        ChangeState(ThreadState::Running);
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return ThreadControlResult::SecurityError;
        }
        
        std::cout << "[INFO] Main thread control initialized successfully" << std::endl;
        LogCommand(ThreadControlCommand::Initialize, true);
        
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during thread control initialization: {}", e.what()));
        ChangeState(ThreadState::Error);
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Legacy gm_MainThreadControl function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: void __fastcall CMainThread::gm_MainThreadControl(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 */
void CMainThreadControl::gm_MainThreadControl_Legacy(CMainThread* mainThread) {
    try {
        if (!mainThread) {
            std::cerr << "[ERROR] CMainThread pointer is null in gm_MainThreadControl" << std::endl;
            return;
        }
        
        // Create or get global thread control instance
        {
            std::lock_guard<std::mutex> lock(g_MainThreadControlMutex);
            if (!g_MainThreadControl) {
                g_MainThreadControl = std::make_unique<CMainThreadControl>();
            }
        }
        
        // Initialize thread control
        ThreadControlResult result = g_MainThreadControl->InitializeMainThreadControl(mainThread);
        
        if (result != ThreadControlResult::Success) {
            std::cerr << "[ERROR] Thread control initialization failed: " 
                      << g_MainThreadControl->GetLastError() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in gm_MainThreadControl_Legacy: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in gm_MainThreadControl_Legacy" << std::endl;
    }
}

/**
 * @brief Execute a thread control command
 * @param command The command to execute
 * @param parameters Optional command parameters
 * @return ThreadControlResult indicating success or failure
 */
ThreadControlResult CMainThreadControl::ExecuteCommand(ThreadControlCommand command, 
                                                      const std::unordered_map<std::string, std::string>& parameters) {
    try {
        auto startTime = std::chrono::steady_clock::now();
        
        if (!ValidateCommand(command)) {
            LogCommand(command, false, "Invalid command");
            return ThreadControlResult::InvalidCommand;
        }
        
        ThreadControlResult result = ThreadControlResult::Failure;
        
        switch (command) {
            case ThreadControlCommand::Initialize:
                result = InitializeControl();
                break;
            case ThreadControlCommand::Start:
                result = StartControl();
                break;
            case ThreadControlCommand::Stop:
                result = StopControl();
                break;
            case ThreadControlCommand::Pause:
                result = PauseControl();
                break;
            case ThreadControlCommand::Resume:
                result = ResumeControl();
                break;
            case ThreadControlCommand::Shutdown:
                result = ShutdownControl();
                break;
            case ThreadControlCommand::GetStatus:
                result = GetStatusInfo();
                break;
            case ThreadControlCommand::GetStatistics:
                result = GetStatisticsInfo();
                break;
            case ThreadControlCommand::CheckHealth:
                result = CheckHealthStatus();
                break;
            case ThreadControlCommand::StartNetworking:
                result = StartNetworkingInternal();
                break;
            case ThreadControlCommand::StopNetworking:
                result = StopNetworkingInternal();
                break;
            case ThreadControlCommand::ResetConnections:
                result = ResetConnectionsInternal();
                break;
            case ThreadControlCommand::ReloadData:
                result = ReloadDataInternal();
                break;
            case ThreadControlCommand::ValidateData:
                result = ValidateDataInternal();
                break;
            case ThreadControlCommand::BackupData:
                result = BackupDataInternal();
                break;
            case ThreadControlCommand::EnableWorldService:
                result = EnableWorldServiceInternal();
                break;
            case ThreadControlCommand::DisableWorldService:
                result = DisableWorldServiceInternal();
                break;
            case ThreadControlCommand::OpenWorld:
                result = OpenWorldInternal();
                break;
            case ThreadControlCommand::CloseWorld:
                result = CloseWorldInternal();
                break;
            case ThreadControlCommand::StartMonitoring:
                result = StartMonitoring();
                break;
            case ThreadControlCommand::StopMonitoring:
                result = StopMonitoring();
                break;
            case ThreadControlCommand::EmergencyStop:
                result = EmergencyStopInternal();
                break;
            case ThreadControlCommand::ForceRestart:
                result = ForceRestartInternal();
                break;
            case ThreadControlCommand::SafeShutdown:
                result = SafeShutdownInternal();
                break;
            default:
                result = ThreadControlResult::InvalidCommand;
                break;
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t commandIndex = static_cast<size_t>(command);
            if (commandIndex < m_controlStats.commandCounts.size()) {
                m_controlStats.commandCounts[commandIndex]++;
                m_controlStats.commandTimes[commandIndex] = duration;
            }
            m_controlStats.totalCommands++;
            m_controlStats.lastCommandTime = endTime;
            
            if (result == ThreadControlResult::Success) {
                m_controlStats.successfulCommands++;
            } else {
                m_controlStats.failedCommands++;
            }
        }
        
        LogCommand(command, result == ThreadControlResult::Success);
        return result;
        
    } catch (const std::exception& e) {
        LogCommand(command, false, std::format("Exception: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Get current thread state
 * @return Current ThreadState
 */
ThreadState CMainThreadControl::GetCurrentState() const {
    return m_currentState.load();
}

/**
 * @brief Get thread control statistics
 * @return const reference to control statistics
 */
const ThreadControlStats& CMainThreadControl::GetControlStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_controlStats;
}

/**
 * @brief Get thread control configuration
 * @return const reference to control configuration
 */
const ThreadControlConfig& CMainThreadControl::GetControlConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_controlConfig;
}

/**
 * @brief Update thread control configuration
 * @param config New configuration
 * @return ThreadControlResult indicating success or failure
 */
ThreadControlResult CMainThreadControl::UpdateConfig(const ThreadControlConfig& config) {
    try {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_controlConfig = config;
        
        std::cout << "[INFO] Thread control configuration updated" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to update configuration: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Check if thread is in a healthy state
 * @return true if healthy, false otherwise
 */
bool CMainThreadControl::IsHealthy() const {
    ThreadState state = GetCurrentState();
    return (state == ThreadState::Ready || state == ThreadState::Running);
}

/**
 * @brief Get the last control error message
 * @return string containing the last error message
 */
std::string CMainThreadControl::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Wait for thread to reach a specific state
 * @param targetState The state to wait for
 * @param timeout Maximum time to wait
 * @return true if state reached, false if timeout
 */
bool CMainThreadControl::WaitForState(ThreadState targetState, std::chrono::milliseconds timeout) {
    std::unique_lock<std::mutex> lock(m_stateMutex);
    return m_stateCondition.wait_for(lock, timeout, [this, targetState]() {
        return m_currentState.load() == targetState;
    });
}

/**
 * @brief Register a state change callback
 * @param callback Function to call when state changes
 */
void CMainThreadControl::RegisterStateChangeCallback(std::function<void(ThreadState, ThreadState)> callback) {
    std::lock_guard<std::mutex> lock(m_stateMutex);
    m_stateCallbacks.push_back(std::move(callback));
}

/**
 * @brief Start the control monitoring system
 * @return ThreadControlResult indicating success or failure
 */
ThreadControlResult CMainThreadControl::StartMonitoring() {
    try {
        if (m_monitoringEnabled.load()) {
            return ThreadControlResult::Success; // Already running
        }
        
        m_monitoringEnabled = true;
        m_monitoringThread = std::thread(&CMainThreadControl::MonitoringThreadFunction, this);
        
        std::cout << "[INFO] Thread control monitoring started" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to start monitoring: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Stop the control monitoring system
 * @return ThreadControlResult indicating success or failure
 */
ThreadControlResult CMainThreadControl::StopMonitoring() {
    try {
        if (!m_monitoringEnabled.load()) {
            return ThreadControlResult::Success; // Already stopped
        }
        
        m_monitoringEnabled = false;
        
        if (m_monitoringThread.joinable()) {
            m_monitoringThread.join();
        }
        
        std::cout << "[INFO] Thread control monitoring stopped" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to stop monitoring: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Initialize control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::InitializeControl() {
    try {
        std::cout << "[INFO] Initializing thread control system..." << std::endl;

        // Reset statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_controlStats.Reset();
        }

        // Initialize configuration with defaults
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            // Configuration is already initialized with defaults in constructor
        }

        std::cout << "[INFO] Thread control system initialized" << std::endl;
        return ThreadControlResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to initialize control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Start control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::StartControl() {
    try {
        if (GetCurrentState() == ThreadState::Running) {
            return ThreadControlResult::Success; // Already running
        }

        ChangeState(ThreadState::Running);
        std::cout << "[INFO] Thread control started" << std::endl;
        return ThreadControlResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to start control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Stop control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::StopControl() {
    try {
        if (GetCurrentState() == ThreadState::Stopped) {
            return ThreadControlResult::Success; // Already stopped
        }

        ChangeState(ThreadState::Stopping);

        // Stop monitoring if running
        if (m_monitoringEnabled.load()) {
            StopMonitoring();
        }

        ChangeState(ThreadState::Stopped);
        std::cout << "[INFO] Thread control stopped" << std::endl;
        return ThreadControlResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to stop control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}
