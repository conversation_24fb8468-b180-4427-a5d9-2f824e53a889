/**
 * @file CreateSetData.h
 * @brief Create SetData Structures for Game Objects
 * @details Defines the hierarchy of creation data structures for various game objects
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#pragma once

#include <cstdint>
#include <array>
#include <memory>
#include <string>
#include <chrono>

// Forward declarations
class CMapData;
class CItemStore;
class CMonster;

namespace NexusProtection {
namespace World {

/**
 * @struct ObjectCreateSetData
 * @brief Base structure for object creation data
 * @details Contains fundamental data needed to create any game object
 */
struct ObjectCreateSetData {
    int32_t m_nLayerIndex;              ///< Layer index for the object
    CMapData* m_pMap;                   ///< Pointer to the map where object will be created
    std::array<float, 3> m_fStartPos;   ///< Starting position [x, y, z]
    void* m_pRecordSet;                 ///< Pointer to record set data (type varies by object)
    uint32_t m_dwFlags;                 ///< Creation flags
    
    /**
     * @brief Default constructor
     * Initializes all members to safe default values
     */
    ObjectCreateSetData() noexcept
        : m_nLayerIndex(0)
        , m_pMap(nullptr)
        , m_fStartPos{0.0f, 0.0f, 0.0f}
        , m_pRecordSet(nullptr)
        , m_dwFlags(0) {}
    
    /**
     * @brief Virtual destructor for proper inheritance
     */
    virtual ~ObjectCreateSetData() = default;
    
    /**
     * @brief Validates the creation data
     * @return true if the data is valid for object creation
     */
    [[nodiscard]] virtual bool IsValid() const noexcept {
        return m_pMap != nullptr && m_pRecordSet != nullptr;
    }
    
    /**
     * @brief Resets all data to default values
     */
    virtual void Reset() noexcept {
        m_nLayerIndex = 0;
        m_pMap = nullptr;
        m_fStartPos.fill(0.0f);
        m_pRecordSet = nullptr;
        m_dwFlags = 0;
    }
};

/**
 * @struct CharacterCreateSetData
 * @brief Character creation data structure
 * @details Extends ObjectCreateSetData with character-specific information
 */
struct CharacterCreateSetData : public ObjectCreateSetData {
    uint32_t m_dwCharacterType;         ///< Type of character being created
    uint32_t m_dwCharacterFlags;        ///< Character-specific creation flags
    float m_fRotation;                  ///< Initial rotation angle
    uint32_t m_dwLevel;                 ///< Initial level
    
    /**
     * @brief Default constructor
     * Initializes character-specific data
     */
    CharacterCreateSetData() noexcept
        : ObjectCreateSetData()
        , m_dwCharacterType(0)
        , m_dwCharacterFlags(0)
        , m_fRotation(0.0f)
        , m_dwLevel(1) {}
    
    /**
     * @brief Validates character creation data
     * @return true if the data is valid for character creation
     */
    [[nodiscard]] bool IsValid() const noexcept override {
        return ObjectCreateSetData::IsValid() && m_dwLevel > 0;
    }
    
    /**
     * @brief Resets character data to defaults
     */
    void Reset() noexcept override {
        ObjectCreateSetData::Reset();
        m_dwCharacterType = 0;
        m_dwCharacterFlags = 0;
        m_fRotation = 0.0f;
        m_dwLevel = 1;
    }
};

/**
 * @struct MonsterCreateSetData
 * @brief Monster creation data structure
 * @details Contains all data needed to create a monster instance
 */
struct MonsterCreateSetData : public CharacterCreateSetData {
    void* pActiveRec;                   ///< Pointer to active record data
    void* pDumPosition;                 ///< Pointer to dummy position data
    bool bDungeon;                      ///< Whether this is a dungeon monster
    CMonster* pParent;                  ///< Parent monster for hierarchy
    bool bRobExp;                       ///< Can rob experience from players
    bool bRewardExp;                    ///< Can reward experience to players
    bool bStdItemLoot;                  ///< Uses standard item loot system
    uint32_t m_dwMonsterSerial;         ///< Unique monster serial number
    std::string m_strMonsterCode;       ///< Monster code identifier
    
    /**
     * @brief Default constructor
     * Initializes monster-specific data with game defaults
     */
    MonsterCreateSetData() noexcept
        : CharacterCreateSetData()
        , pActiveRec(nullptr)
        , pDumPosition(nullptr)
        , bDungeon(false)
        , pParent(nullptr)
        , bRobExp(true)
        , bRewardExp(true)
        , bStdItemLoot(true)
        , m_dwMonsterSerial(0) {}
    
    /**
     * @brief Validates monster creation data
     * @return true if the data is valid for monster creation
     */
    [[nodiscard]] bool IsValid() const noexcept override {
        return CharacterCreateSetData::IsValid() && !m_strMonsterCode.empty();
    }
    
    /**
     * @brief Resets monster data to defaults
     */
    void Reset() noexcept override {
        CharacterCreateSetData::Reset();
        pActiveRec = nullptr;
        pDumPosition = nullptr;
        bDungeon = false;
        pParent = nullptr;
        bRobExp = true;
        bRewardExp = true;
        bStdItemLoot = true;
        m_dwMonsterSerial = 0;
        m_strMonsterCode.clear();
    }
    
    /**
     * @brief Sets the monster position
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     */
    void SetPosition(float x, float y, float z) noexcept {
        m_fStartPos[0] = x;
        m_fStartPos[1] = y;
        m_fStartPos[2] = z;
    }
    
    /**
     * @brief Gets the monster position
     * @return Array containing [x, y, z] coordinates
     */
    [[nodiscard]] const std::array<float, 3>& GetPosition() const noexcept {
        return m_fStartPos;
    }
};

/**
 * @struct NPCCreateSetData
 * @brief NPC creation data structure
 * @details Contains all data needed to create an NPC instance
 */
struct NPCCreateSetData : public CharacterCreateSetData {
    CItemStore* m_pLinkItemStore;       ///< Associated item store for merchant NPCs
    uint8_t m_byRaceCode;               ///< Race code for the NPC
    uint32_t m_dwNPCFlags;              ///< NPC-specific flags
    std::string m_strNPCName;           ///< Display name for the NPC
    uint32_t m_dwNPCType;               ///< Type of NPC (merchant, quest giver, etc.)
    
    /**
     * @brief Default constructor
     * Initializes NPC-specific data with safe defaults
     */
    NPCCreateSetData() noexcept
        : CharacterCreateSetData()
        , m_pLinkItemStore(nullptr)
        , m_byRaceCode(0xFF)  // -1 as unsigned
        , m_dwNPCFlags(0)
        , m_dwNPCType(0) {}
    
    /**
     * @brief Validates NPC creation data
     * @return true if the data is valid for NPC creation
     */
    [[nodiscard]] bool IsValid() const noexcept override {
        return CharacterCreateSetData::IsValid() && 
               m_byRaceCode != 0xFF && 
               !m_strNPCName.empty();
    }
    
    /**
     * @brief Resets NPC data to defaults
     */
    void Reset() noexcept override {
        CharacterCreateSetData::Reset();
        m_pLinkItemStore = nullptr;
        m_byRaceCode = 0xFF;
        m_dwNPCFlags = 0;
        m_strNPCName.clear();
        m_dwNPCType = 0;
    }
    
    /**
     * @brief Sets the NPC race code
     * @param raceCode The race code to set
     */
    void SetRaceCode(uint8_t raceCode) noexcept {
        m_byRaceCode = raceCode;
    }
    
    /**
     * @brief Gets the NPC race code
     * @return The current race code
     */
    [[nodiscard]] uint8_t GetRaceCode() const noexcept {
        return m_byRaceCode;
    }
    
    /**
     * @brief Sets the NPC name
     * @param name The name to set
     */
    void SetName(const std::string& name) {
        m_strNPCName = name;
    }
    
    /**
     * @brief Gets the NPC name
     * @return The current NPC name
     */
    [[nodiscard]] const std::string& GetName() const noexcept {
        return m_strNPCName;
    }
};

// Type aliases for backward compatibility with legacy code
using _object_create_setdata = ObjectCreateSetData;
using _character_create_setdata = CharacterCreateSetData;
using _monster_create_setdata = MonsterCreateSetData;
using _npc_create_setdata = NPCCreateSetData;

/**
 * @namespace CreateSetDataUtils
 * @brief Utility functions for create setdata operations
 */
namespace CreateSetDataUtils {
    /**
     * @brief Creates a default monster creation data structure
     * @param monsterCode The monster code identifier
     * @param pMap The map where the monster will be created
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return Initialized MonsterCreateSetData structure
     */
    [[nodiscard]] MonsterCreateSetData CreateDefaultMonsterData(
        const std::string& monsterCode,
        CMapData* pMap,
        float x, float y, float z
    );
    
    /**
     * @brief Creates a default NPC creation data structure
     * @param npcName The NPC name
     * @param raceCode The race code
     * @param pMap The map where the NPC will be created
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return Initialized NPCCreateSetData structure
     */
    [[nodiscard]] NPCCreateSetData CreateDefaultNPCData(
        const std::string& npcName,
        uint8_t raceCode,
        CMapData* pMap,
        float x, float y, float z
    );
    
    /**
     * @brief Validates any create setdata structure
     * @param pData Pointer to the data structure
     * @return true if the data is valid
     */
    template<typename T>
    [[nodiscard]] bool ValidateCreateData(const T* pData) noexcept {
        return pData != nullptr && pData->IsValid();
    }
}

} // namespace World
} // namespace NexusProtection
