/*
 * CBsp_LoadEntities.cpp - BSP Entity Loading Implementation
 * Refactored from LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CBsp.h"
#include "../Headers/CEntity.h"
#include "../Headers/CParticle.h"
#include "../Headers/CMergeFileManager.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <sstream>
#include <iomanip>

// External dependencies
extern void* Dmalloc(size_t size);
extern void memset_0(void* ptr, int value, size_t size);
extern void memcpy_0(void* dest, const void* src, size_t size);
extern void SetMergeFileManager(CMergeFileManager* mfm);
extern void Warning(const char* filename, const char* message);
extern uint32_t rand();
extern size_t strlen(const char* str);
extern uint32_t dword_184A797AC;
extern const char byte_184A790F0[];

/**
 * Constructor
 */
CBsp::CBsp() 
    : mEntity(nullptr)
    , mParticle(nullptr)
    , mEntityList(nullptr)
    , mMapEntitiesList(nullptr)
    , mEntityCache(nullptr)
    , mEntityListNum(0)
    , mMapEntitiesListNum(0)
    , mTotalAllocSize(0)
    , m_debuggingEnabled(false)
    , m_initialized(false) {
    
    m_statistics.Reset();
    LogDebug("CBsp::CBsp - BSP system constructed");
}

/**
 * Destructor
 */
CBsp::~CBsp() {
    Cleanup();
    LogDebug("CBsp::~CBsp - BSP system destroyed");
}

/**
 * Load entities from entity list
 * Refactored from original LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 */
BSPLoadResult CBsp::LoadEntities(const _READ_MAP_ENTITIES_LIST* pEntityList) {
    try {
        LogDebug("CBsp::LoadEntities - Starting entity loading process");
        
        // Validate input parameters
        if (!ValidateEntityList(pEntityList)) {
            LogBSPOperation("LoadEntities - Invalid parameters", BSPLoadResult::InvalidParameters);
            return BSPLoadResult::InvalidParameters;
        }
        
        // Reset statistics
        m_statistics.Reset();
        m_statistics.totalEntities = mEntityListNum;
        
        // Check if we have entities to load
        if (mEntityListNum == 0) {
            LogDebug("LoadEntities - No entities to load");
            return BSPLoadResult::Success;
        }
        
        // Allocate memory for entities
        if (!AllocateEntityMemory()) {
            LogBSPOperation("LoadEntities - Memory allocation failed", BSPLoadResult::MemoryAllocationFailed);
            return BSPLoadResult::MemoryAllocationFailed;
        }
        
        // Set up merge file manager
        SetMergeFileManager(&mMapEntityMFM);
        
        // Load individual entities
        BSPLoadResult loadResult = LoadIndividualEntities();
        if (loadResult != BSPLoadResult::Success) {
            LogBSPOperation("LoadEntities - Individual entity loading failed", loadResult);
            return loadResult;
        }
        
        // Process map entities
        if (!ProcessMapEntities(pEntityList)) {
            LogBSPOperation("LoadEntities - Map entity processing failed", BSPLoadResult::LoadingFailed);
            return BSPLoadResult::LoadingFailed;
        }
        
        LogBSPOperation("LoadEntities completed successfully", BSPLoadResult::Success);
        return BSPLoadResult::Success;
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::LoadEntities - Exception occurred: %s", e.what());
        return BSPLoadResult::LoadingFailed;
    } catch (...) {
        Logger::Error("CBsp::LoadEntities - Unknown exception occurred");
        return BSPLoadResult::LoadingFailed;
    }
}

/**
 * Allocate memory for entities
 */
bool CBsp::AllocateEntityMemory() {
    try {
        LogDebug("AllocateEntityMemory - Allocating memory for entities");
        
        // Allocate memory for entities (244 bytes each)
        mEntity = static_cast<CEntity*>(Dmalloc(ENTITY_SIZE * mEntityListNum));
        if (!mEntity) {
            Logger::Error("AllocateEntityMemory - Failed to allocate entity memory");
            return false;
        }
        
        // Initialize entity memory
        memset_0(mEntity, 0, ENTITY_SIZE * mEntityListNum);
        
        // Allocate memory for particles (1168 bytes each)
        mParticle = static_cast<CParticle*>(Dmalloc(PARTICLE_SIZE * mEntityListNum));
        if (!mParticle) {
            Logger::Error("AllocateEntityMemory - Failed to allocate particle memory");
            return false;
        }
        
        // Initialize particle memory
        memset_0(mParticle, 0, PARTICLE_SIZE * mEntityListNum);
        
        // Update total allocated size (equivalent to original 1412 * mEntityListNum)
        mTotalAllocSize += TOTAL_ENTITY_SIZE * mEntityListNum;
        m_statistics.memoryAllocated = mTotalAllocSize;
        
        LogDebug("AllocateEntityMemory - Memory allocated successfully: %u bytes", mTotalAllocSize);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("AllocateEntityMemory - Exception: %s", e.what());
        return false;
    }
}

/**
 * Load individual entities
 */
BSPLoadResult CBsp::LoadIndividualEntities() {
    try {
        LogDebug("LoadIndividualEntities - Loading %u entities", mEntityListNum);
        
        // Build base path (equivalent to original byte_184A790F0 copy)
        std::string basePath;
        if (byte_184A790F0) {
            size_t baseLen = 0;
            while (byte_184A790F0[baseLen] != '\0') {
                basePath += byte_184A790F0[baseLen];
                baseLen++;
            }
        }
        
        // Process each entity in the list
        for (uint32_t entityIndex = 0; entityIndex < mEntityListNum; ++entityIndex) {
            if (!mEntityList) {
                Logger::Error("LoadIndividualEntities - Entity list is null");
                return BSPLoadResult::InvalidParameters;
            }
            
            // Build entity path (equivalent to original path building logic)
            std::string entityPath = BuildEntityPath(mEntityList[entityIndex].Name, basePath.c_str());
            
            // Initialize entity state
            mEntityList[entityIndex].IsFileExist = false;
            
            // Set shader ID if global flag is not set (equivalent to original dword_184A797AC check)
            if (!dword_184A797AC) {
                mEntityList[entityIndex].ShaderID = 0;
            }
            
            // Load entity based on type
            bool loadSuccess = false;
            if (mEntityList[entityIndex].IsParticle) {
                loadSuccess = LoadSingleParticle(entityIndex, entityPath);
                UpdateStatistics(true, loadSuccess);
            } else {
                loadSuccess = LoadSingleEntity(entityIndex, entityPath);
                UpdateStatistics(false, loadSuccess);
            }
            
            if (loadSuccess) {
                mEntityList[entityIndex].IsFileExist = true;
                m_statistics.loadedEntities++;
            } else {
                m_statistics.failedEntities++;
            }
        }
        
        LogDebug("LoadIndividualEntities - Completed: %u loaded, %u failed", 
                m_statistics.loadedEntities, m_statistics.failedEntities);
        
        return BSPLoadResult::Success;
        
    } catch (const std::exception& e) {
        Logger::Error("LoadIndividualEntities - Exception: %s", e.what());
        return BSPLoadResult::LoadingFailed;
    }
}

/**
 * Load single particle
 */
bool CBsp::LoadSingleParticle(uint32_t index, const std::string& particlePath) {
    try {
        LogDebug("LoadSingleParticle - Loading particle %u: %s", index, particlePath.c_str());
        
        // Load particle SPT file (equivalent to original CParticle::LoadParticleSPT call)
        if (CParticle::LoadParticleSPT(&mParticle[index], particlePath.c_str(), 0)) {
            // Initialize particle (equivalent to original CParticle::InitParticle call)
            CParticle::InitParticle(&mParticle[index]);
            
            // Set particle state (equivalent to original CParticle::SetParticleState call)
            CParticle::SetParticleState(&mParticle[index], 1);
            
            LogDebug("LoadSingleParticle - Particle loaded successfully: %u", index);
            return true;
        } else {
            // Warning message (equivalent to original Warning call)
            std::string warningMsg = " <- 파티클 로딩, 파티클 spt가 아닙니다.";
            Warning(particlePath.c_str(), warningMsg.c_str());
            
            LogDebug("LoadSingleParticle - Particle loading failed: %u", index);
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("LoadSingleParticle - Exception: %s", e.what());
        return false;
    }
}

/**
 * Load single entity
 */
bool CBsp::LoadSingleEntity(uint32_t index, const std::string& entityPath) {
    try {
        LogDebug("LoadSingleEntity - Loading entity %u: %s", index, entityPath.c_str());
        
        // Determine load flags (equivalent to original shader flag logic)
        int loadFlags = 0x20; // Base flag
        if (mEntityList[index].ShaderID) {
            loadFlags |= 2; // Add shader flag
        }
        
        // Load entity (equivalent to original CEntity::LoadEntity call)
        if (CEntity::LoadEntity(&mEntity[index], entityPath.c_str(), loadFlags)) {
            // Apply entity flags (equivalent to original flag check)
            if (mEntityList[index].Flag & 0x40) {
                mEntity[index].mFlag |= 0x40;
            }
            
            // Restore texture memory (equivalent to original CEntity::RestoreTexMem call)
            CEntity::RestoreTexMem(&mEntity[index]);
            
            LogDebug("LoadSingleEntity - Entity loaded successfully: %u", index);
            return true;
        } else {
            LogDebug("LoadSingleEntity - Entity loading failed: %u", index);
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("LoadSingleEntity - Exception: %s", e.what());
        return false;
    }
}

/**
 * Build entity file path
 */
std::string CBsp::BuildEntityPath(const char* entityName, const char* basePath) const {
    if (!entityName) {
        return "";
    }
    
    std::string fullPath;
    
    // Add base path
    if (basePath && strlen(basePath) > 0) {
        fullPath = basePath;
        if (fullPath.back() != '\\' && fullPath.back() != '/') {
            fullPath += "\\";
        }
    }
    
    // Handle entity name (equivalent to original path building logic)
    if (entityName[0] == '\\') {
        // Skip leading backslash (equivalent to original v12[v7].Name[v14 + 1] logic)
        fullPath += (entityName + 1);
    } else {
        // Use name as-is (equivalent to original v12[v7].Name[v17] logic)
        fullPath += entityName;
    }
    
    return fullPath;
}

/**
 * Process map entities from read list
 */
bool CBsp::ProcessMapEntities(const _READ_MAP_ENTITIES_LIST* pEntityList) {
    try {
        LogDebug("ProcessMapEntities - Processing %u map entities", mMapEntitiesListNum);

        if (mMapEntitiesListNum == 0) {
            LogDebug("ProcessMapEntities - No map entities to process");
            return true;
        }

        // Process each map entity (equivalent to original loop starting at v24 = 0)
        for (uint32_t mapIndex = 0; mapIndex < mMapEntitiesListNum; ++mapIndex) {
            const _READ_MAP_ENTITIES_LIST* pReadEntity = &pEntityList[mapIndex];

            // Check if the referenced entity file exists (equivalent to original IsFileExist check)
            if (pReadEntity->ID < mEntityListNum && mEntityList[pReadEntity->ID].IsFileExist) {
                // Copy entity data (equivalent to original data copying)
                mMapEntitiesList[mapIndex].ID = pReadEntity->ID;
                mMapEntitiesList[mapIndex].Pos[0] = pReadEntity->Pos[0];
                mMapEntitiesList[mapIndex].Pos[1] = pReadEntity->Pos[1];
                mMapEntitiesList[mapIndex].Pos[2] = pReadEntity->Pos[2];
                mMapEntitiesList[mapIndex].RotX = pReadEntity->RotX;
                mMapEntitiesList[mapIndex].RotY = pReadEntity->RotY;
                mMapEntitiesList[mapIndex].Scale = pReadEntity->Scale;
                mMapEntitiesList[mapIndex].BBMin[0] = pReadEntity->BBMin[0];
                mMapEntitiesList[mapIndex].BBMin[1] = pReadEntity->BBMin[1];
                mMapEntitiesList[mapIndex].BBMin[2] = pReadEntity->BBMin[2];
                mMapEntitiesList[mapIndex].BBMax[0] = pReadEntity->BBMax[0];
                mMapEntitiesList[mapIndex].BBMax[1] = pReadEntity->BBMax[1];
                mMapEntitiesList[mapIndex].BBMax[2] = pReadEntity->BBMax[2];

                // Generate random frame offset (equivalent to original rand() calculation)
                mMapEntitiesList[mapIndex].AddFrame = GenerateRandomFrameOffset();

                // Initialize particle pointer
                mMapEntitiesList[mapIndex].Particle = nullptr;

                // Handle particle entities (equivalent to original IsParticle check)
                if (mEntityList[pReadEntity->ID].IsParticle) {
                    if (!CreateParticleInstance(mapIndex)) {
                        Logger::Warning("ProcessMapEntities - Failed to create particle instance for map entity %u", mapIndex);
                    }
                }

                LogDebug("ProcessMapEntities - Map entity %u processed successfully", mapIndex);
            } else {
                // Clear map entity data for non-existent entities (equivalent to original memset)
                ClearMapEntity(mapIndex);
                LogDebug("ProcessMapEntities - Map entity %u cleared (entity file not found)", mapIndex);
            }
        }

        LogDebug("ProcessMapEntities - All map entities processed successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("ProcessMapEntities - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create particle instance for map entity
 */
bool CBsp::CreateParticleInstance(uint32_t mapIndex) {
    try {
        // Allocate new particle (equivalent to original operator new(0x490ui64))
        CParticle* newParticle = static_cast<CParticle*>(operator new(PARTICLE_SIZE));
        if (!newParticle) {
            Logger::Error("CreateParticleInstance - Failed to allocate particle memory");
            return false;
        }

        // Initialize particle (equivalent to original CParticle::CParticle call)
        CParticle::CParticle(newParticle);

        // Set particle reference
        mMapEntitiesList[mapIndex].Particle = newParticle;

        // Copy particle data (equivalent to original memcpy_0 call)
        uint16_t entityID = mMapEntitiesList[mapIndex].ID;
        memcpy_0(newParticle, &mParticle[entityID], PARTICLE_SIZE);

        // Initialize particle (equivalent to original CParticle::InitParticle call)
        CParticle::InitParticle(newParticle);

        LogDebug("CreateParticleInstance - Particle instance created for map entity %u", mapIndex);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CreateParticleInstance - Exception: %s", e.what());
        return false;
    }
}

/**
 * Clear map entity data
 */
void CBsp::ClearMapEntity(uint32_t mapIndex) {
    if (mapIndex >= mMapEntitiesListNum) {
        return;
    }

    // Clear all data (equivalent to original memset operations)
    _MAP_ENTITIES_LIST* pMapEntity = &mMapEntitiesList[mapIndex];
    memset(pMapEntity, 0, sizeof(_MAP_ENTITIES_LIST));
}

/**
 * Generate random frame offset
 */
float CBsp::GenerateRandomFrameOffset() const {
    // Equivalent to original: (float)((unsigned __int8)(BYTE4(v28) + v28) - BYTE4(v28)) * 0.25
    uint32_t randValue = rand();
    uint8_t lowByte = static_cast<uint8_t>(randValue);
    uint8_t highByte = static_cast<uint8_t>(randValue >> 24);
    uint8_t combined = lowByte + highByte;
    float offset = static_cast<float>(combined - highByte) * DEFAULT_FRAME_VARIANCE;

    return offset;
}

/**
 * Validate entity list
 */
bool CBsp::ValidateEntityList(const _READ_MAP_ENTITIES_LIST* pEntityList) const {
    if (!pEntityList && mMapEntitiesListNum > 0) {
        Logger::Error("ValidateEntityList - Entity list is null but entity count is non-zero");
        return false;
    }

    if (!mEntityList && mEntityListNum > 0) {
        Logger::Error("ValidateEntityList - Entity list is null but entity count is non-zero");
        return false;
    }

    LogDebug("ValidateEntityList - Entity list validation successful");
    return true;
}

/**
 * Update statistics
 */
void CBsp::UpdateStatistics(bool isParticle, bool success) {
    if (isParticle) {
        m_statistics.particleEntities++;
    } else {
        m_statistics.regularEntities++;
    }

    if (success) {
        m_statistics.loadedEntities++;
    } else {
        m_statistics.failedEntities++;
    }
}

/**
 * Log BSP operation
 */
void CBsp::LogBSPOperation(const std::string& operation, BSPLoadResult result) const {
    if (!m_debuggingEnabled) {
        return;
    }

    std::string resultStr = BSPUtils::LoadResultToString(result);
    Logger::Info("BSP Operation: %s - Result: %s", operation.c_str(), resultStr.c_str());
}

/**
 * Log debug information
 */
void CBsp::LogDebug(const std::string& message) const {
    if (!m_debuggingEnabled) {
        return;
    }

    Logger::Debug("BSP Debug: %s", message.c_str());
}

/**
 * Legacy C-style LoadEntities function for compatibility
 * Maintains original function signature: void __fastcall CBsp::LoadEntities(CBsp *this, struct _READ_MAP_ENTITIES_LIST *a2)
 * Original function from: LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 */
void CBsp::LoadEntities_Legacy(struct _READ_MAP_ENTITIES_LIST* pEntityList) {
    // Call the modern implementation and handle the result
    BSPLoadResult result = LoadEntities(pEntityList);

    // Log any errors for legacy callers
    if (result != BSPLoadResult::Success) {
        Logger::Error("LoadEntities_Legacy - Loading failed with result: %d", static_cast<int>(result));
    }
}
