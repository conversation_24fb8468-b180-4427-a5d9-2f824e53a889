/*
 * CNetIndexList.cpp - Network Index and IP Management List
 * Simple implementation for IP filtering and index management
 */

#include "../Headers/CNetIndexList.h"
#include "../../common/Headers/Logger.h"

/**
 * CNetIndexList constructor
 */
CNetIndexList::CNetIndexList() {
    // Constructor implementation
}

/**
 * CNetIndexList destructor
 */
CNetIndexList::~CNetIndexList() {
    Clear();
}

/**
 * Add IP address to list
 */
bool CNetIndexList::AddIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        if (ipAddress.empty()) {
            return false;
        }
        
        auto result = m_ipSet.insert(ipAddress);
        return result.second;  // true if insertion took place
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::AddIP failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Remove IP address from list
 */
bool CNetIndexList::RemoveIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        if (ipAddress.empty()) {
            return false;
        }
        
        size_t removed = m_ipSet.erase(ipAddress);
        return removed > 0;
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::RemoveIP failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Check if IP address exists in list
 */
bool CNetIndexList::ContainsIP(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        if (ipAddress.empty()) {
            return false;
        }
        
        return m_ipSet.find(ipAddress) != m_ipSet.end();
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::ContainsIP failed: " + std::string(e.what()));
        return false;
    }
}

/**
 * Clear all IP addresses
 */
void CNetIndexList::Clear() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        m_ipSet.clear();
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::Clear failed: " + std::string(e.what()));
    }
}

/**
 * Get number of IP addresses in list
 */
size_t CNetIndexList::GetCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        return m_ipSet.size();
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::GetCount failed: " + std::string(e.what()));
        return 0;
    }
}

/**
 * Get all IP addresses as vector
 */
std::vector<std::string> CNetIndexList::GetAllIPs() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    try {
        std::vector<std::string> result;
        result.reserve(m_ipSet.size());
        
        for (const auto& ip : m_ipSet) {
            result.push_back(ip);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::LogError("CNetIndexList::GetAllIPs failed: " + std::string(e.what()));
        return std::vector<std::string>();
    }
}
