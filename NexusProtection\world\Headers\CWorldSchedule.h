/**
 * @file CWorldSchedule.h
 * @brief World scheduling system for managing timed events and operations
 * @details Provides scheduling functionality for world events, timers, and scheduled operations
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#pragma once

#ifndef CWORLDSCHEDULE_H
#define CWORLDSCHEDULE_H

#include <cstdint>
#include <memory>
#include <vector>
#include <chrono>
#include <functional>

// Forward declarations
class CRecordData;
class CMyTimer;

namespace NexusProtection {
namespace World {

/**
 * @brief Schedule entry structure for world events
 */
struct WorldScheduleEntry {
    uint32_t scheduleId;        ///< Unique schedule identifier
    uint32_t eventType;         ///< Type of scheduled event
    uint32_t startHour;         ///< Start hour (0-23)
    uint32_t startMinute;       ///< Start minute (0-59)
    uint32_t endHour;           ///< End hour (0-23)
    uint32_t endMinute;         ///< End minute (0-59)
    uint32_t dayMask;           ///< Bitmask for days of week (bit 0 = Sunday)
    bool isActive;              ///< Whether this schedule entry is active
    std::string description;    ///< Description of the scheduled event
    
    WorldScheduleEntry() noexcept
        : scheduleId(0), eventType(0), startHour(0), startMinute(0)
        , endHour(0), endMinute(0), dayMask(0), isActive(false) {}
};

/**
 * @brief Schedule status enumeration
 */
enum class ScheduleStatus : uint32_t {
    Inactive = 0,       ///< Schedule is not active
    Active = 1,         ///< Schedule is currently active
    Pending = 2,        ///< Schedule is pending activation
    Expired = 3,        ///< Schedule has expired
    Error = 4           ///< Schedule has an error
};

/**
 * @brief Event type enumeration for scheduled events
 */
enum class ScheduleEventType : uint32_t {
    ServerMaintenance = 0,  ///< Server maintenance event
    BossSpawn = 1,          ///< Boss monster spawn event
    EventStart = 2,         ///< Special event start
    EventEnd = 3,           ///< Special event end
    SystemMessage = 4,      ///< System message broadcast
    Custom = 99             ///< Custom event type
};

/**
 * @brief Statistics for schedule monitoring
 */
struct ScheduleStatistics {
    std::size_t totalSchedules;         ///< Total number of schedules
    std::size_t activeSchedules;        ///< Number of currently active schedules
    std::size_t pendingSchedules;       ///< Number of pending schedules
    std::size_t expiredSchedules;       ///< Number of expired schedules
    std::size_t errorSchedules;         ///< Number of schedules with errors
    std::chrono::system_clock::time_point lastCheckTime;  ///< Last check time
    uint64_t totalChecks;               ///< Total number of schedule checks performed
    uint64_t totalActivations;          ///< Total number of schedule activations
};

/**
 * @brief Callback function type for schedule events
 */
using ScheduleCallback = std::function<void(const WorldScheduleEntry&)>;

/**
 * @brief World scheduling system class
 * 
 * This class manages timed events and operations in the world system.
 * It provides functionality for:
 * - Creating and managing scheduled events
 * - Time-based activation and deactivation
 * - Schedule validation and monitoring
 * - Event callbacks and notifications
 */
class CWorldSchedule {
public:
    /**
     * @brief Default constructor
     */
    CWorldSchedule();
    
    /**
     * @brief Destructor
     */
    ~CWorldSchedule();
    
    // Delete copy constructor and copy assignment operator
    CWorldSchedule(const CWorldSchedule&) = delete;
    CWorldSchedule& operator=(const CWorldSchedule&) = delete;
    
    /**
     * @brief Move constructor
     * @param other The object to move from
     */
    CWorldSchedule(CWorldSchedule&& other) noexcept;
    
    /**
     * @brief Move assignment operator
     * @param other The object to move from
     * @return Reference to this object
     */
    CWorldSchedule& operator=(CWorldSchedule&& other) noexcept;

    // Core scheduling operations
    
    /**
     * @brief Initialize the schedule system
     * @return true if initialization was successful, false otherwise
     */
    [[nodiscard]] bool Initialize();
    
    /**
     * @brief Shutdown the schedule system
     */
    void Shutdown();
    
    /**
     * @brief Check and process all schedules
     * @details This should be called periodically to check for schedule activations
     */
    void CheckSchedules();
    
    /**
     * @brief Calculate schedule cursor position for given time
     * @param hour Hour value (0-23)
     * @param minute Minute value (0-59)
     * @return Schedule cursor position, or -1 if invalid
     */
    [[nodiscard]] int32_t CalcScheduleCursor(int32_t hour, int32_t minute) const noexcept;
    
    /**
     * @brief Change schedule cursor to specified position
     * @param scheduleData Pointer to schedule data structure
     */
    void ChangeScheduleCursor(void* scheduleData);
    
    /**
     * @brief Validate schedule data integrity
     * @return true if all schedule data is valid, false otherwise
     */
    [[nodiscard]] bool DataCheck() const;
    
    // Schedule management
    
    /**
     * @brief Add a new schedule entry
     * @param entry The schedule entry to add
     * @return true if the schedule was added successfully, false otherwise
     */
    [[nodiscard]] bool AddSchedule(const WorldScheduleEntry& entry);
    
    /**
     * @brief Remove a schedule entry by ID
     * @param scheduleId The ID of the schedule to remove
     * @return true if the schedule was removed, false if not found
     */
    [[nodiscard]] bool RemoveSchedule(uint32_t scheduleId);
    
    /**
     * @brief Update an existing schedule entry
     * @param scheduleId The ID of the schedule to update
     * @param entry The new schedule data
     * @return true if the schedule was updated, false if not found
     */
    [[nodiscard]] bool UpdateSchedule(uint32_t scheduleId, const WorldScheduleEntry& entry);
    
    /**
     * @brief Find a schedule entry by ID
     * @param scheduleId The ID of the schedule to find
     * @return Pointer to the schedule entry, or nullptr if not found
     */
    [[nodiscard]] const WorldScheduleEntry* FindSchedule(uint32_t scheduleId) const;
    
    /**
     * @brief Get all active schedules
     * @return Vector of pointers to active schedule entries
     */
    [[nodiscard]] std::vector<const WorldScheduleEntry*> GetActiveSchedules() const;
    
    /**
     * @brief Get schedules by event type
     * @param eventType The event type to filter by
     * @return Vector of pointers to matching schedule entries
     */
    [[nodiscard]] std::vector<const WorldScheduleEntry*> GetSchedulesByType(ScheduleEventType eventType) const;
    
    // Status and monitoring
    
    /**
     * @brief Check if the schedule system is operational
     * @return true if operational, false otherwise
     */
    [[nodiscard]] bool IsOperational() const noexcept;
    
    /**
     * @brief Set the operational status
     * @param operational The new operational status
     */
    void SetOperational(bool operational) noexcept;
    
    /**
     * @brief Get the number of schedules
     * @return Total number of schedules
     */
    [[nodiscard]] std::size_t GetScheduleCount() const noexcept;
    
    /**
     * @brief Check if the schedule system is empty
     * @return true if no schedules exist, false otherwise
     */
    [[nodiscard]] bool IsEmpty() const noexcept;
    
    /**
     * @brief Get comprehensive statistics
     * @return Statistics structure with current data
     */
    [[nodiscard]] ScheduleStatistics GetStatistics() const;
    
    /**
     * @brief Clear all schedules
     */
    void Clear();
    
    // Callback management
    
    /**
     * @brief Set callback for schedule activation events
     * @param callback The callback function to set
     */
    void SetActivationCallback(ScheduleCallback callback);
    
    /**
     * @brief Set callback for schedule deactivation events
     * @param callback The callback function to set
     */
    void SetDeactivationCallback(ScheduleCallback callback);
    
    /**
     * @brief Remove all callbacks
     */
    void ClearCallbacks();
    
    // Utility functions
    
    /**
     * @brief Validate a schedule entry
     * @param entry The schedule entry to validate
     * @return true if the entry is valid, false otherwise
     */
    [[nodiscard]] static bool ValidateScheduleEntry(const WorldScheduleEntry& entry);
    
    /**
     * @brief Check if a time falls within a schedule
     * @param entry The schedule entry to check
     * @param hour Current hour (0-23)
     * @param minute Current minute (0-59)
     * @param dayOfWeek Current day of week (0 = Sunday)
     * @return true if the time is within the schedule, false otherwise
     */
    [[nodiscard]] static bool IsTimeInSchedule(const WorldScheduleEntry& entry, 
                                               int32_t hour, int32_t minute, int32_t dayOfWeek);
    
    /**
     * @brief Get current system time components
     * @param hour Reference to store current hour
     * @param minute Reference to store current minute
     * @param dayOfWeek Reference to store current day of week
     */
    static void GetCurrentTime(int32_t& hour, int32_t& minute, int32_t& dayOfWeek);
    
    /**
     * @brief Get memory usage of the schedule system
     * @return Memory usage in bytes
     */
    [[nodiscard]] std::size_t GetMemoryUsage() const;

private:
    // Private member variables
    std::unique_ptr<CRecordData> m_tblSch;      ///< Schedule table data
    std::unique_ptr<CMyTimer> m_tmrCheck;       ///< Timer for schedule checking
    bool m_bOper;                               ///< Operational status flag
    
    std::vector<WorldScheduleEntry> m_schedules;    ///< Schedule entries
    ScheduleCallback m_activationCallback;         ///< Callback for activations
    ScheduleCallback m_deactivationCallback;       ///< Callback for deactivations
    
    mutable ScheduleStatistics m_statistics;       ///< Statistics tracking
    
    // Private helper methods
    
    /**
     * @brief Initialize internal data structures
     */
    void InitializeInternal();
    
    /**
     * @brief Process a single schedule entry
     * @param entry The schedule entry to process
     * @param currentHour Current hour
     * @param currentMinute Current minute
     * @param currentDay Current day of week
     */
    void ProcessScheduleEntry(WorldScheduleEntry& entry, int32_t currentHour, 
                             int32_t currentMinute, int32_t currentDay);
    
    /**
     * @brief Update statistics
     */
    void UpdateStatistics() const;
    
    /**
     * @brief Validate internal state
     * @return true if internal state is valid, false otherwise
     */
    [[nodiscard]] bool ValidateInternalState() const;
    
    /**
     * @brief Get maximum number of schedules allowed
     * @return Maximum schedule count
     */
    [[nodiscard]] static constexpr std::size_t GetMaxScheduleCount() noexcept {
        return 1000; // Reasonable limit for schedule entries
    }
};

} // namespace World
} // namespace NexusProtection

#endif // CWORLDSCHEDULE_H
