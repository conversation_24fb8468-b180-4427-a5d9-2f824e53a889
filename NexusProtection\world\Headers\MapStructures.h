/*
 * MapStructures.h - Map Data Structures
 * Refactored for Visual Studio 2022 compatibility
 * Original: Various map-related structures from decompiled source
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <cstring>

// Forward declarations
class CMapData;
class CItemStore;
class CMapData;

/**
 * Base field structure for all record types
 */
struct _base_fld {
    uint32_t m_dwIndex;         // Record index
    char m_strCode[32];         // Record code string
    
    _base_fld() : m_dwIndex(0) {
        memset(m_strCode, 0, sizeof(m_strCode));
    }
};

/**
 * Map field structure containing map configuration data
 */
struct _map_fld : public _base_fld {
    char m_strFileName[64];     // Map file name
    int m_nMapType;             // Map type (0 = standard, 1 = instance, etc.)
    int m_nLayerNum;            // Number of layers in the map
    float m_fStartPos[3];       // Default start position
    int m_nMaxPlayers;          // Maximum players allowed
    bool m_bPvPEnabled;         // PvP enabled flag
    bool m_bSafeZone;           // Safe zone flag
    int m_nRespawnTime;         // Respawn time in seconds
    
    // Additional map properties
    int m_nMinLevel;            // Minimum level requirement
    int m_nMaxLevel;            // Maximum level requirement
    int m_nInstanceType;        // Instance type for instanced maps
    
    _map_fld() : _base_fld(), m_nMapType(0), m_nLayerNum(1), m_nMaxPlayers(100),
                 m_bPvPEnabled(false), m_bSafeZone(true), m_nRespawnTime(10),
                 m_nMinLevel(1), m_nMaxLevel(999), m_nInstanceType(0) {
        memset(m_strFileName, 0, sizeof(m_strFileName));
        m_fStartPos[0] = m_fStartPos[1] = m_fStartPos[2] = 0.0f;
    }
};

/**
 * NPC creation setup data structure
 */
struct _npc_create_setdata {
    void* m_pRecordSet;         // Pointer to NPC record data
    CItemStore* m_pLinkItemStore; // Linked item store for merchant NPCs
    CMapData* m_pMap;           // Map where NPC will be created
    float m_fStartPos[3];       // Starting position
    int m_nLayerIndex;          // Layer index for placement
    uint8_t m_byRaceCode;       // Race code for NPC type
    uint32_t m_dwFlags;         // Creation flags
    
    _npc_create_setdata() : m_pRecordSet(nullptr), m_pLinkItemStore(nullptr),
                           m_pMap(nullptr), m_nLayerIndex(0), m_byRaceCode(0),
                           m_dwFlags(0) {
        m_fStartPos[0] = m_fStartPos[1] = m_fStartPos[2] = 0.0f;
    }
};

/**
 * Portal dummy structure for portal positioning
 */
struct _portal_dummy {
    void* m_pPortalRec;         // Portal record data
    float m_fPosition[3];       // Portal position
    float m_fRotation[3];       // Portal rotation
    bool m_bActive;             // Portal active state
    
    _portal_dummy() : m_pPortalRec(nullptr), m_bActive(true) {
        m_fPosition[0] = m_fPosition[1] = m_fPosition[2] = 0.0f;
        m_fRotation[0] = m_fRotation[1] = m_fRotation[2] = 0.0f;
    }
};

/**
 * Portal field structure for portal configuration
 */
struct _portal_fld : public _base_fld {
    char m_strLinkMapCode[32];      // Linked map code
    char m_strLinkPortalCode[32];   // Linked portal code
    float m_fPosition[3];           // Portal position
    int m_nPortalType;              // Portal type
    bool m_bBidirectional;          // Bidirectional portal flag
    
    _portal_fld() : _base_fld(), m_nPortalType(0), m_bBidirectional(true) {
        memset(m_strLinkMapCode, 0, sizeof(m_strLinkMapCode));
        memset(m_strLinkPortalCode, 0, sizeof(m_strLinkPortalCode));
        m_fPosition[0] = m_fPosition[1] = m_fPosition[2] = 0.0f;
    }
};

/**
 * Store dummy structure for item store positioning
 */
struct _store_dummy {
    void* m_pDumPos;            // Dummy position data
    int m_nStoreType;           // Store type
    bool m_bActive;             // Store active state
    
    _store_dummy() : m_pDumPos(nullptr), m_nStoreType(0), m_bActive(true) {}
};

/**
 * Dummy position structure for various positioning needs
 */
struct _dummy_position {
    float m_fPosition[3];       // Position coordinates
    float m_fRotation[3];       // Rotation angles
    char m_strCode[32];         // Position code
    int m_nType;                // Position type
    
    _dummy_position() : m_nType(0) {
        m_fPosition[0] = m_fPosition[1] = m_fPosition[2] = 0.0f;
        m_fRotation[0] = m_fRotation[1] = m_fRotation[2] = 0.0f;
        memset(m_strCode, 0, sizeof(m_strCode));
    }
};

/**
 * Map operation constants
 */
namespace MapConstants {
    constexpr int MAX_MAPS = 100;
    constexpr int MAX_MAP_NAME_LENGTH = 64;
    constexpr int MAX_PORTALS_PER_MAP = 50;
    constexpr int MAX_STORES_PER_MAP = 20;
    constexpr int MAX_NPC_POOL_SIZE = 500;
    
    // Map types
    constexpr int MAP_TYPE_STANDARD = 0;
    constexpr int MAP_TYPE_INSTANCE = 1;
    constexpr int MAP_TYPE_PVP = 2;
    constexpr int MAP_TYPE_DUNGEON = 3;
    
    // Portal types
    constexpr int PORTAL_TYPE_NORMAL = 0;
    constexpr int PORTAL_TYPE_INSTANCE = 1;
    constexpr int PORTAL_TYPE_PVP = 2;
}

/**
 * Utility functions for map structures
 */
namespace MapStructureUtils {
    // Validation functions
    bool IsValidMapField(const _map_fld* pMapField);
    bool IsValidPortalField(const _portal_fld* pPortalField);
    bool IsValidNpcCreateData(const _npc_create_setdata* pCreateData);
    
    // String utilities
    std::string SafeStringCopy(const char* source, size_t maxLength = 256);
    void SafeStringSet(char* dest, const char* source, size_t destSize);
    
    // Position utilities
    bool IsValidPosition(const float position[3]);
    float CalculateDistance(const float pos1[3], const float pos2[3]);
    void NormalizeRotation(float rotation[3]);
    
    // Logging utilities
    void LogMapFieldInfo(const _map_fld* pMapField);
    void LogPortalFieldInfo(const _portal_fld* pPortalField);
    void LogNpcCreateInfo(const _npc_create_setdata* pCreateData);
}

#endif // MAPSTRUCTURES_H
