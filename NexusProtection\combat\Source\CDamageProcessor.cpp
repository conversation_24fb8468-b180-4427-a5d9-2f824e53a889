#include "../Headers/CDamageProcessor.h"
#include "../../player/Headers/CMonsterAttack.h"
#include <cmath>
#include <algorithm>
#include <random>

// Forward declarations for external types
class CCharacter;
class CMapData;
class CObjectList;
struct _attack_param;
struct _sec_info;

/**
 * CDamageProcessor constructor
 */
CDamageProcessor::CDamageProcessor(CCharacter* pAttacker) 
    : m_p<PERSON><PERSON><PERSON>(pAttacker) {
    m_damageResults.reserve(DamageConstants::MAX_DAMAGE_TARGETS);
}

/**
 * CDamageProcessor destructor
 */
CDamageProcessor::~CDamageProcessor() {
    m_damageResults.clear();
    m_pAttacker = nullptr;
}

/**
 * Process area damage
 */
std::vector<DamageResult> CDamageProcessor::ProcessAreaDamage(const AreaDamageParams& params, _attack_param* pAttackParam) {
    std::vector<DamageResult> results;
    
    if (!m_pAttacker || !pAttackParam) {
        return results;
    }
    
    try {
        // Get all potential targets in the area
        std::vector<CCharacter*> targets = GetTargetsInArea(
            params.targetArea[0], 
            params.targetArea[1], 
            static_cast<float>(params.nLimitRadius)
        );
        
        // Process each target
        for (CCharacter* pTarget : targets) {
            if (!IsValidTarget(pTarget, params.bIgnoreAllies)) {
                continue;
            }
            
            // Calculate distance from center
            float distance = CalculateDistance(
                params.targetArea[0], params.targetArea[1],
                0.0f, 0.0f  // Target position would be retrieved from pTarget
            );
            
            // Apply distance-based damage falloff
            int adjustedDamage = ApplyDistanceFalloff(
                params.nAttPower, 
                distance, 
                static_cast<float>(params.nLimitRadius), 
                params.fDamageFalloff
            );
            
            // Calculate final damage
            DamageResult result = CalculateTargetDamage(
                pTarget, 
                adjustedDamage, 
                EDamageType::AREA, 
                distance
            );
            
            if (result.nDamage > 0) {
                results.push_back(result);
            }
        }
        
    } catch (const std::exception& e) {
        // Log error and return empty results
        results.clear();
    }
    
    return results;
}

/**
 * Process flash damage
 */
std::vector<DamageResult> CDamageProcessor::ProcessFlashDamage(const FlashDamageParams& params, _attack_param* pAttackParam) {
    std::vector<DamageResult> results;
    
    if (!m_pAttacker || !pAttackParam) {
        return results;
    }
    
    try {
        // Get attacker position (would be retrieved from m_pAttacker)
        float attackerX = 0.0f, attackerY = 0.0f;  // Placeholder
        
        // Get all potential targets in the distance range
        std::vector<CCharacter*> targets = GetTargetsInArea(
            attackerX, 
            attackerY, 
            static_cast<float>(params.nLimDist)
        );
        
        // Process each target
        for (CCharacter* pTarget : targets) {
            if (!IsValidTarget(pTarget, params.bIgnoreAllies)) {
                continue;
            }
            
            // Calculate angle to target (would use actual target position)
            float targetAngle = CalculateAngle(attackerX, attackerY, 0.0f, 0.0f);
            
            // Check if target is within the flash cone
            if (!IsAngleInRange(targetAngle, static_cast<float>(params.nAngle), params.fAngleTolerance)) {
                continue;
            }
            
            // Calculate distance
            float distance = CalculateDistance(attackerX, attackerY, 0.0f, 0.0f);
            
            // Apply distance-based damage falloff
            int adjustedDamage = ApplyDistanceFalloff(
                params.nAttPower, 
                distance, 
                static_cast<float>(params.nLimDist), 
                DamageConstants::DISTANCE_DAMAGE_FALLOFF
            );
            
            // Calculate final damage
            DamageResult result = CalculateTargetDamage(
                pTarget, 
                adjustedDamage, 
                EDamageType::FLASH, 
                distance, 
                targetAngle
            );
            
            if (result.nDamage > 0) {
                results.push_back(result);
            }
        }
        
    } catch (const std::exception& e) {
        // Log error and return empty results
        results.clear();
    }
    
    return results;
}

/**
 * Process sector damage
 */
std::vector<DamageResult> CDamageProcessor::ProcessSectorDamage(const SectorDamageParams& params, _attack_param* pAttackParam) {
    std::vector<DamageResult> results;
    
    if (!m_pAttacker || !pAttackParam) {
        return results;
    }
    
    try {
        // Get attacker position
        float attackerX = 0.0f, attackerY = 0.0f;  // Placeholder
        
        // Get all potential targets in the radius
        std::vector<CCharacter*> targets = GetTargetsInArea(
            attackerX, 
            attackerY, 
            static_cast<float>(params.nLimitRadius)
        );
        
        // Calculate sector angle range
        float sectorStart = static_cast<float>(params.nStartAngle);
        float sectorEnd = static_cast<float>(params.nEndAngle);
        float sectorRange = std::abs(sectorEnd - sectorStart);
        
        // Process each target
        for (CCharacter* pTarget : targets) {
            if (!IsValidTarget(pTarget, params.bIgnoreAllies)) {
                continue;
            }
            
            // Calculate angle to target
            float targetAngle = CalculateAngle(attackerX, attackerY, 0.0f, 0.0f);
            
            // Check if target is within the sector
            bool inSector = false;
            if (sectorStart <= sectorEnd) {
                inSector = (targetAngle >= sectorStart && targetAngle <= sectorEnd);
            } else {
                // Handle wrap-around case
                inSector = (targetAngle >= sectorStart || targetAngle <= sectorEnd);
            }
            
            if (!inSector) {
                continue;
            }
            
            // Calculate distance
            float distance = CalculateDistance(attackerX, attackerY, 0.0f, 0.0f);
            
            // Apply distance-based damage falloff
            int adjustedDamage = ApplyDistanceFalloff(
                params.nAttPower, 
                distance, 
                static_cast<float>(params.nLimitRadius), 
                params.fDamageFalloff
            );
            
            // Apply angle-based damage falloff
            float angleFromCenter = std::abs(targetAngle - static_cast<float>(params.nAngle));
            adjustedDamage = ApplyAngleFalloff(
                adjustedDamage, 
                angleFromCenter, 
                sectorRange / 2.0f, 
                DamageConstants::ANGLE_DAMAGE_FALLOFF
            );
            
            // Calculate final damage
            DamageResult result = CalculateTargetDamage(
                pTarget, 
                adjustedDamage, 
                EDamageType::SECTOR, 
                distance, 
                targetAngle
            );
            
            if (result.nDamage > 0) {
                results.push_back(result);
            }
        }
        
    } catch (const std::exception& e) {
        // Log error and return empty results
        results.clear();
    }
    
    return results;
}

/**
 * Calculate damage for a specific target
 */
DamageResult CDamageProcessor::CalculateTargetDamage(CCharacter* pTarget, int nBaseDamage, EDamageType damageType, 
                                                   float fDistance, float fAngle) {
    DamageResult result;
    result.pTarget = pTarget;
    result.damageType = damageType;
    result.fDistance = fDistance;
    result.fAngle = fAngle;
    
    if (!pTarget || nBaseDamage <= 0) {
        return result;
    }
    
    try {
        // Check for miss
        result.bMissed = CheckDamageMiss(pTarget);
        if (result.bMissed) {
            result.nDamage = 0;
            return result;
        }
        
        // Check for block
        result.bBlocked = CheckDamageBlock(pTarget, damageType);
        if (result.bBlocked) {
            result.nDamage = nBaseDamage / 4;  // Blocked damage is reduced to 25%
            return result;
        }
        
        // Check for critical hit
        result.bCritical = CheckCriticalHit(pTarget);
        
        // Calculate final damage
        result.nDamage = nBaseDamage;
        if (result.bCritical) {
            result.nDamage = static_cast<int>(result.nDamage * DamageConstants::CRITICAL_DAMAGE_MULTIPLIER);
        }
        
        // Ensure minimum damage
        if (result.nDamage < 1) {
            result.nDamage = 1;
        }
        
    } catch (const std::exception& e) {
        result.nDamage = 0;
        result.bMissed = true;
    }
    
    return result;
}

/**
 * Apply damage to target
 */
bool CDamageProcessor::ApplyDamage(const DamageResult& result) {
    if (!result.pTarget || result.nDamage <= 0) {
        return false;
    }
    
    try {
        // This would call the target's damage application method
        // For now, we'll just return true to indicate success
        return true;
        
    } catch (const std::exception& e) {
        return false;
    }
}

/**
 * Get all potential targets in area
 */
std::vector<CCharacter*> CDamageProcessor::GetTargetsInArea(float centerX, float centerY, float radius) {
    std::vector<CCharacter*> targets;

    try {
        // This would interface with the game's object management system
        // to find all characters within the specified area
        // For now, return empty vector as placeholder

    } catch (const std::exception& e) {
        targets.clear();
    }

    return targets;
}

/**
 * Check if target is valid for damage
 */
bool CDamageProcessor::IsValidTarget(CCharacter* pTarget, bool bIgnoreAllies) const {
    if (!pTarget || !m_pAttacker) {
        return false;
    }

    try {
        // Check if target is the attacker
        if (pTarget == m_pAttacker) {
            return false;
        }

        // Check if target is alive (would check actual health/status)
        // For now, assume all targets are valid

        // Check alliance if required
        if (bIgnoreAllies) {
            // This would check if the target is an ally
            // For now, assume all targets are enemies
        }

        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

/**
 * Calculate distance between two points
 */
float CDamageProcessor::CalculateDistance(float x1, float y1, float x2, float y2) {
    float dx = x2 - x1;
    float dy = y2 - y1;
    return std::sqrt(dx * dx + dy * dy);
}

/**
 * Calculate angle between two points
 */
float CDamageProcessor::CalculateAngle(float x1, float y1, float x2, float y2) {
    float dx = x2 - x1;
    float dy = y2 - y1;
    float angle = std::atan2(dy, dx) * 180.0f / static_cast<float>(M_PI);

    // Normalize to 0-360 degrees
    if (angle < 0) {
        angle += 360.0f;
    }

    return angle;
}

/**
 * Check if angle is within range
 */
bool CDamageProcessor::IsAngleInRange(float targetAngle, float centerAngle, float tolerance) {
    float diff = std::abs(targetAngle - centerAngle);

    // Handle wrap-around case
    if (diff > 180.0f) {
        diff = 360.0f - diff;
    }

    return diff <= tolerance;
}

/**
 * Get map data for current area
 */
CMapData* CDamageProcessor::GetMapData() const {
    // This would retrieve the current map data
    // For now, return nullptr as placeholder
    return nullptr;
}

/**
 * Get sector information for coordinates
 */
_sec_info* CDamageProcessor::GetSectorInfo(float x, float y) const {
    // This would retrieve sector information for the given coordinates
    // For now, return nullptr as placeholder
    return nullptr;
}

/**
 * Apply damage falloff based on distance
 */
int CDamageProcessor::ApplyDistanceFalloff(int baseDamage, float distance, float maxDistance, float falloffRate) const {
    if (distance <= 0.0f || maxDistance <= 0.0f) {
        return baseDamage;
    }

    if (distance >= maxDistance) {
        return static_cast<int>(baseDamage * falloffRate);
    }

    float ratio = distance / maxDistance;
    float multiplier = 1.0f - (ratio * (1.0f - falloffRate));

    return static_cast<int>(baseDamage * multiplier);
}

/**
 * Apply damage falloff based on angle
 */
int CDamageProcessor::ApplyAngleFalloff(int baseDamage, float angle, float maxAngle, float falloffRate) const {
    if (angle <= 0.0f || maxAngle <= 0.0f) {
        return baseDamage;
    }

    if (angle >= maxAngle) {
        return static_cast<int>(baseDamage * falloffRate);
    }

    float ratio = angle / maxAngle;
    float multiplier = 1.0f - (ratio * (1.0f - falloffRate));

    return static_cast<int>(baseDamage * multiplier);
}

/**
 * Check for critical hit
 */
bool CDamageProcessor::CheckCriticalHit(CCharacter* pTarget) const {
    if (!m_pAttacker || !pTarget) {
        return false;
    }

    try {
        // This would calculate critical hit probability based on attacker and target stats
        // For now, use a simple random chance
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);

        float criticalChance = 0.05f;  // 5% base critical chance
        return dis(gen) < criticalChance;

    } catch (const std::exception& e) {
        return false;
    }
}

/**
 * Check for damage block
 */
bool CDamageProcessor::CheckDamageBlock(CCharacter* pTarget, EDamageType damageType) const {
    if (!pTarget) {
        return false;
    }

    try {
        // This would check target's block chance and equipment
        // For now, use a simple random chance
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);

        float blockChance = 0.1f;  // 10% base block chance
        return dis(gen) < blockChance;

    } catch (const std::exception& e) {
        return false;
    }
}

/**
 * Check for damage miss
 */
bool CDamageProcessor::CheckDamageMiss(CCharacter* pTarget) const {
    if (!m_pAttacker || !pTarget) {
        return true;  // Miss if invalid parameters
    }

    try {
        // This would calculate miss chance based on attacker accuracy and target evasion
        // For now, use a simple random chance
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);

        float missChance = 0.05f;  // 5% base miss chance
        return dis(gen) < missChance;

    } catch (const std::exception& e) {
        return true;  // Miss on error
    }
}
