/**
 * @file AccountServerLogin.h
 * @brief Account server login functionality for CMainThread
 * 
 * Refactored from decompiled source: AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c
 * Original function: CMainThread::AccountServerLogin
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <string>
#include <cstdint>
#include <memory>

// Forward declarations
class CMainThread;
class CNationSettingManager;

/**
 * @enum AccountServerLoginResult
 * @brief Result codes for account server login operations
 */
enum class AccountServerLoginResult : int32_t {
    Success = 1,
    Failure = 0,
    InvalidWorldName = -1,
    NetworkError = -2,
    ConfigurationError = -3,
    SecurityError = -4,
    SystemError = -5
};

/**
 * @struct WorldConnectionInfo
 * @brief Information for connecting to the world server
 */
struct WorldConnectionInfo {
    std::string worldName;
    std::string gateIP;
    uint32_t ipAddress;
    uint8_t hashVerify[32];
    uint8_t connectionType;
    uint8_t protocolVersion;
    
    WorldConnectionInfo();
    void Reset();
    bool IsValid() const;
};

/**
 * @class AccountServerLogin
 * @brief Handles account server login functionality
 * 
 * This class manages the account server login process, including
 * world name validation, IP address resolution, and connection
 * establishment with the account server.
 */
class AccountServerLogin {
public:
    /**
     * @brief Constructor
     */
    AccountServerLogin();
    
    /**
     * @brief Destructor
     */
    ~AccountServerLogin();
    
    // Delete copy constructor and assignment operator
    AccountServerLogin(const AccountServerLogin&) = delete;
    AccountServerLogin& operator=(const AccountServerLogin&) = delete;
    
    // Allow move constructor and assignment operator
    AccountServerLogin(AccountServerLogin&&) = default;
    AccountServerLogin& operator=(AccountServerLogin&&) = default;
    
    /**
     * @brief Execute account server login
     * 
     * Performs the account server login process for the specified main thread.
     * This includes reading configuration, resolving IP addresses, and sending
     * the login request to the account server.
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return AccountServerLoginResult indicating success or failure
     */
    AccountServerLoginResult ExecuteLogin(CMainThread* mainThread);
    
    /**
     * @brief Legacy AccountServerLogin function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * 
     * @param mainThread Pointer to CMainThread instance
     */
    static void AccountServerLogin_Legacy(CMainThread* mainThread);
    
    /**
     * @brief Get the last error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get world connection information
     * @return const reference to world connection info
     */
    const WorldConnectionInfo& GetConnectionInfo() const;

private:
    /**
     * @brief Initialize world connection information
     * @param mainThread Pointer to CMainThread instance
     * @return true if successful, false otherwise
     */
    bool InitializeConnectionInfo(CMainThread* mainThread);
    
    /**
     * @brief Read configuration from WorldInfo.ini
     * @return true if successful, false otherwise
     */
    bool ReadConfiguration();
    
    /**
     * @brief Resolve IP address
     * @param gateIP Gate IP string from configuration
     * @return Resolved IP address, or 0 if failed
     */
    uint32_t ResolveIPAddress(const std::string& gateIP);
    
    /**
     * @brief Prepare hash verification data
     * @return true if successful, false otherwise
     */
    bool PrepareHashVerification();
    
    /**
     * @brief Send login request to account server
     * @return true if successful, false otherwise
     */
    bool SendLoginRequest();
    
    /**
     * @brief Send cash database DSN request
     * @return true if successful, false otherwise
     */
    bool SendCashDBRequest();
    
    /**
     * @brief Set the last error message
     * @param error Error message
     */
    void SetLastError(const std::string& error);
    
    /**
     * @brief Validate security cookie
     * @return true if valid, false if corrupted
     */
    bool ValidateSecurityCookie() const;
    
    // Member variables
    WorldConnectionInfo m_connectionInfo;
    std::string m_lastError;
    uint64_t m_securityCookie;
    CMainThread* m_mainThread;
    
    // Configuration file path
    static constexpr const char* CONFIG_FILE_PATH = "..\\WorldInfo\\WorldInfo.ini";
    static constexpr const char* CONFIG_SECTION = "System";
    static constexpr const char* CONFIG_KEY_GATE_IP = "GateIP";
    static constexpr const char* CONFIG_DEFAULT_IP = "X";
    static constexpr size_t MAX_IP_STRING_LENGTH = 128;
    static constexpr size_t HASH_VERIFY_SIZE = 32;
};

/**
 * @brief Convert AccountServerLoginResult enum to string for logging
 * @param result The login result
 * @return String representation of the result
 */
std::string AccountServerLoginResultToString(AccountServerLoginResult result);

// External functions (to be properly linked)
extern uint32_t GetIPAddress();
extern uint32_t inet_addr(const char* cp);
extern int GetPrivateProfileStringA(const char* lpAppName, const char* lpKeyName, 
                                   const char* lpDefault, char* lpReturnedString, 
                                   uint32_t nSize, const char* lpFileName);
extern void* CNetProcess_LoadSendMsg(void* netProcess, int param1, void* msgType, 
                                   void* msgData, uint16_t msgSize);
extern CNationSettingManager* CTSingleton_CNationSettingManager_Instance();
extern void CNationSettingManager_SendCashDBDSNRequest(CNationSettingManager* manager);

// External global variables
extern uint8_t g_cbHashVerify[32];
extern void* unk_1414F2090;
extern uintptr_t _security_cookie;
