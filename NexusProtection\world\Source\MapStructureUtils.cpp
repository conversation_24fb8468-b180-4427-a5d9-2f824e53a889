/*
 * MapStructureUtils.cpp - Map Structure Utility Functions
 * Implementation of utility functions for map data structures
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/MapStructures.h"
#include "../../common/Headers/Logger.h"

#include <cmath>
#include <algorithm>
#include <cstring>
#include <sstream>

namespace MapStructureUtils {

/**
 * Validate map field structure
 */
bool IsValidMapField(const _map_fld* pMapField) {
    if (!pMapField) {
        return false;
    }
    
    // Check if code string is not empty
    if (strlen(pMapField->m_strCode) == 0) {
        return false;
    }
    
    // Check if filename is not empty
    if (strlen(pMapField->m_strFileName) == 0) {
        return false;
    }
    
    // Validate map type
    if (pMapField->m_nMapType < 0 || pMapField->m_nMapType > 10) {
        return false;
    }
    
    // Validate layer number
    if (pMapField->m_nLayerNum < 1 || pMapField->m_nLayerNum > 100) {
        return false;
    }
    
    // Validate max players
    if (pMapField->m_nMaxPlayers < 1 || pMapField->m_nMaxPlayers > 10000) {
        return false;
    }
    
    // Validate level requirements
    if (pMapField->m_nMinLevel < 1 || pMapField->m_nMinLevel > 999) {
        return false;
    }
    
    if (pMapField->m_nMaxLevel < pMapField->m_nMinLevel || pMapField->m_nMaxLevel > 999) {
        return false;
    }
    
    // Validate respawn time
    if (pMapField->m_nRespawnTime < 1 || pMapField->m_nRespawnTime > 3600) {
        return false;
    }
    
    return true;
}

/**
 * Validate portal field structure
 */
bool IsValidPortalField(const _portal_fld* pPortalField) {
    if (!pPortalField) {
        return false;
    }
    
    // Check if code string is not empty
    if (strlen(pPortalField->m_strCode) == 0) {
        return false;
    }
    
    // Check if link map code is not empty
    if (strlen(pPortalField->m_strLinkMapCode) == 0) {
        return false;
    }
    
    // Check if link portal code is not empty
    if (strlen(pPortalField->m_strLinkPortalCode) == 0) {
        return false;
    }
    
    // Validate portal type
    if (pPortalField->m_nPortalType < 0 || pPortalField->m_nPortalType > 10) {
        return false;
    }
    
    // Validate position
    if (!IsValidPosition(pPortalField->m_fPosition)) {
        return false;
    }
    
    return true;
}

/**
 * Validate NPC creation data structure
 */
bool IsValidNpcCreateData(const _npc_create_setdata* pCreateData) {
    if (!pCreateData) {
        return false;
    }
    
    // Check if map pointer is valid
    if (!pCreateData->m_pMap) {
        return false;
    }
    
    // Validate layer index
    if (pCreateData->m_nLayerIndex < 0 || pCreateData->m_nLayerIndex > 100) {
        return false;
    }
    
    // Validate position
    if (!IsValidPosition(pCreateData->m_fStartPos)) {
        return false;
    }
    
    // Race code should be valid (non-zero for most NPCs)
    // Note: 0 might be valid for some special NPCs, so we allow it
    
    return true;
}

/**
 * Safe string copy with length limit
 */
std::string SafeStringCopy(const char* source, size_t maxLength) {
    if (!source) {
        return std::string();
    }
    
    size_t sourceLen = strlen(source);
    size_t copyLen = std::min(sourceLen, maxLength);
    
    return std::string(source, copyLen);
}

/**
 * Safe string set with bounds checking
 */
void SafeStringSet(char* dest, const char* source, size_t destSize) {
    if (!dest || destSize == 0) {
        return;
    }
    
    if (!source) {
        dest[0] = '\0';
        return;
    }
    
    size_t sourceLen = strlen(source);
    size_t copyLen = std::min(sourceLen, destSize - 1);
    
    strncpy(dest, source, copyLen);
    dest[copyLen] = '\0';
}

/**
 * Validate position coordinates
 */
bool IsValidPosition(const float position[3]) {
    if (!position) {
        return false;
    }
    
    // Check for NaN or infinite values
    for (int i = 0; i < 3; ++i) {
        if (std::isnan(position[i]) || std::isinf(position[i])) {
            return false;
        }
        
        // Check for reasonable coordinate ranges
        // Assuming world coordinates are within -100000 to 100000
        if (position[i] < -100000.0f || position[i] > 100000.0f) {
            return false;
        }
    }
    
    return true;
}

/**
 * Calculate distance between two positions
 */
float CalculateDistance(const float pos1[3], const float pos2[3]) {
    if (!pos1 || !pos2) {
        return -1.0f; // Invalid input
    }
    
    float dx = pos2[0] - pos1[0];
    float dy = pos2[1] - pos1[1];
    float dz = pos2[2] - pos1[2];
    
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Normalize rotation angles to 0-360 degree range
 */
void NormalizeRotation(float rotation[3]) {
    if (!rotation) {
        return;
    }
    
    for (int i = 0; i < 3; ++i) {
        // Normalize to 0-360 range
        while (rotation[i] < 0.0f) {
            rotation[i] += 360.0f;
        }
        while (rotation[i] >= 360.0f) {
            rotation[i] -= 360.0f;
        }
    }
}

/**
 * Log map field information
 */
void LogMapFieldInfo(const _map_fld* pMapField) {
    if (!pMapField) {
        Logger::Error("LogMapFieldInfo - Invalid map field pointer");
        return;
    }
    
    Logger::Info("Map Field Info:");
    Logger::Info("  Code: %s", pMapField->m_strCode);
    Logger::Info("  File: %s", pMapField->m_strFileName);
    Logger::Info("  Type: %d", pMapField->m_nMapType);
    Logger::Info("  Layers: %d", pMapField->m_nLayerNum);
    Logger::Info("  Max Players: %d", pMapField->m_nMaxPlayers);
    Logger::Info("  Start Pos: (%.2f, %.2f, %.2f)", 
                 pMapField->m_fStartPos[0], pMapField->m_fStartPos[1], pMapField->m_fStartPos[2]);
    Logger::Info("  Level Range: %d - %d", pMapField->m_nMinLevel, pMapField->m_nMaxLevel);
    Logger::Info("  PvP: %s", pMapField->m_bPvPEnabled ? "Yes" : "No");
    Logger::Info("  Safe Zone: %s", pMapField->m_bSafeZone ? "Yes" : "No");
    Logger::Info("  Respawn Time: %d seconds", pMapField->m_nRespawnTime);
}

/**
 * Log portal field information
 */
void LogPortalFieldInfo(const _portal_fld* pPortalField) {
    if (!pPortalField) {
        Logger::Error("LogPortalFieldInfo - Invalid portal field pointer");
        return;
    }
    
    Logger::Info("Portal Field Info:");
    Logger::Info("  Code: %s", pPortalField->m_strCode);
    Logger::Info("  Link Map: %s", pPortalField->m_strLinkMapCode);
    Logger::Info("  Link Portal: %s", pPortalField->m_strLinkPortalCode);
    Logger::Info("  Type: %d", pPortalField->m_nPortalType);
    Logger::Info("  Position: (%.2f, %.2f, %.2f)", 
                 pPortalField->m_fPosition[0], pPortalField->m_fPosition[1], pPortalField->m_fPosition[2]);
    Logger::Info("  Bidirectional: %s", pPortalField->m_bBidirectional ? "Yes" : "No");
}

/**
 * Log NPC creation information
 */
void LogNpcCreateInfo(const _npc_create_setdata* pCreateData) {
    if (!pCreateData) {
        Logger::Error("LogNpcCreateInfo - Invalid NPC create data pointer");
        return;
    }
    
    Logger::Info("NPC Create Data Info:");
    Logger::Info("  Map: %p", pCreateData->m_pMap);
    Logger::Info("  Layer: %d", pCreateData->m_nLayerIndex);
    Logger::Info("  Position: (%.2f, %.2f, %.2f)", 
                 pCreateData->m_fStartPos[0], pCreateData->m_fStartPos[1], pCreateData->m_fStartPos[2]);
    Logger::Info("  Race Code: %d", pCreateData->m_byRaceCode);
    Logger::Info("  Flags: 0x%08X", pCreateData->m_dwFlags);
    Logger::Info("  Record Set: %p", pCreateData->m_pRecordSet);
    Logger::Info("  Item Store: %p", pCreateData->m_pLinkItemStore);
}

} // namespace MapStructureUtils
