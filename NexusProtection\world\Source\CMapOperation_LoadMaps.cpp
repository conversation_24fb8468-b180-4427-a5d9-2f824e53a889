/*
 * CMapOperation_LoadMaps.cpp - Map Loading Implementation
 * Refactored from LoadMapsCMapOperationAEAA_NXZ_140196750.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CMapOperation.h"
#include "../Headers/MapStructures.h"
#include "../Headers/CMapDataTable.h"
#include "../Headers/CItemStoreManager.h"
#include "../Headers/CMerchant.h"
#include "../Headers/CMapData.h"
#include "../Headers/CItemStore.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>

// External dependencies (to be properly linked)
extern void* g_NPC;
extern bool LoadRegionData();
extern void CheckMapPortalLink();
extern void NetTrace(const char* format, ...);
extern int MyMessageBox(const char* message, const char* title, int type);

/**
 * Load all maps and initialize map system
 * Refactored from original LoadMapsCMapOperationAEAA_NXZ_140196750.c
 * 
 * @return true if maps loaded successfully, false otherwise
 */
bool CMapOperation::LoadMaps() {
    try {
        Logger::Info("CMapOperation::LoadMaps - Starting map loading process");
        
        // Validate prerequisites
        if (!ValidateMapLoadingPrerequisites()) {
            Logger::Error("CMapOperation::LoadMaps - Prerequisites validation failed");
            return false;
        }
        
        // Initialize map data structures
        if (!InitializeMapDataStructures()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to initialize map data structures");
            return false;
        }
        
        // Load region data first
        if (!LoadRegionData()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to load region data");
            return false;
        }
        
        // Load map field data
        if (!LoadMapFieldData()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to load map field data");
            return false;
        }
        
        // Process item stores for maps
        if (!ProcessMapItemStores()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to process map item stores");
            return false;
        }
        
        // Create NPCs for maps
        if (!CreateMapNPCs()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to create map NPCs");
            return false;
        }
        
        // Setup portal links
        CheckMapPortalLink();
        
        // Finalize map loading
        if (!FinalizeMapLoading()) {
            Logger::Error("CMapOperation::LoadMaps - Failed to finalize map loading");
            return false;
        }
        
        Logger::Info("CMapOperation::LoadMaps - Map loading completed successfully");
        NetTrace("Map loading completed successfully");
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CMapOperation::LoadMaps - Exception occurred: %s", e.what());
        MyMessageBox(e.what(), "Map Loading Error", 0);
        return false;
    } catch (...) {
        Logger::Error("CMapOperation::LoadMaps - Unknown exception occurred");
        MyMessageBox("Unknown error occurred during map loading", "Map Loading Error", 0);
        return false;
    }
}

/**
 * Validate prerequisites for map loading
 */
bool CMapOperation::ValidateMapLoadingPrerequisites() {
    // Check if map data table is available
    if (!CMapDataTable::GetInstance()) {
        Logger::Error("ValidateMapLoadingPrerequisites - Map data table not available");
        return false;
    }
    
    // Check if item store manager is available
    if (!CItemStoreManager::GetInstance()) {
        Logger::Error("ValidateMapLoadingPrerequisites - Item store manager not available");
        return false;
    }
    
    // Check if NPC system is initialized
    if (!g_NPC) {
        Logger::Error("ValidateMapLoadingPrerequisites - NPC system not initialized");
        return false;
    }
    
    return true;
}

/**
 * Initialize map data structures
 */
bool CMapOperation::InitializeMapDataStructures() {
    try {
        // Initialize map data table
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable->Initialize()) {
            Logger::Error("InitializeMapDataStructures - Failed to initialize map data table");
            return false;
        }
        
        // Initialize item store manager
        auto* pItemStoreManager = CItemStoreManager::GetInstance();
        if (!pItemStoreManager->Initialize()) {
            Logger::Error("InitializeMapDataStructures - Failed to initialize item store manager");
            return false;
        }
        
        Logger::Info("InitializeMapDataStructures - Map data structures initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("InitializeMapDataStructures - Exception: %s", e.what());
        return false;
    }
}

/**
 * Load map field data from database/files
 */
bool CMapOperation::LoadMapFieldData() {
    try {
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable) {
            Logger::Error("LoadMapFieldData - Map data table not available");
            return false;
        }
        
        // Get map field records
        auto mapFields = pMapDataTable->GetMapFields();
        if (mapFields.empty()) {
            Logger::Warning("LoadMapFieldData - No map fields found");
            return true; // Not an error, just no maps to load
        }
        
        Logger::Info("LoadMapFieldData - Loading %zu map fields", mapFields.size());
        
        // Process each map field
        for (const auto& mapField : mapFields) {
            if (!ProcessSingleMapField(mapField)) {
                Logger::Error("LoadMapFieldData - Failed to process map field: %s", mapField.m_strCode);
                return false;
            }
        }
        
        Logger::Info("LoadMapFieldData - Successfully loaded %zu map fields", mapFields.size());
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("LoadMapFieldData - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process a single map field
 */
bool CMapOperation::ProcessSingleMapField(const _map_fld& mapField) {
    try {
        // Validate map field
        if (!MapStructureUtils::IsValidMapField(&mapField)) {
            Logger::Error("ProcessSingleMapField - Invalid map field: %s", mapField.m_strCode);
            return false;
        }
        
        // Create map data object
        auto pMapData = std::make_unique<CMapData>();
        if (!pMapData->LoadFromMapField(mapField)) {
            Logger::Error("ProcessSingleMapField - Failed to load map data for: %s", mapField.m_strCode);
            return false;
        }
        
        // Register map with map data table
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable->RegisterMap(std::move(pMapData))) {
            Logger::Error("ProcessSingleMapField - Failed to register map: %s", mapField.m_strCode);
            return false;
        }
        
        Logger::Debug("ProcessSingleMapField - Successfully processed map: %s", mapField.m_strCode);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("ProcessSingleMapField - Exception for map %s: %s", mapField.m_strCode, e.what());
        return false;
    }
}

/**
 * Process item stores for all maps
 */
bool CMapOperation::ProcessMapItemStores() {
    try {
        auto* pItemStoreManager = CItemStoreManager::GetInstance();
        if (!pItemStoreManager) {
            Logger::Error("ProcessMapItemStores - Item store manager not available");
            return false;
        }
        
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable) {
            Logger::Error("ProcessMapItemStores - Map data table not available");
            return false;
        }
        
        // Get all registered maps
        auto maps = pMapDataTable->GetAllMaps();
        
        Logger::Info("ProcessMapItemStores - Processing item stores for %zu maps", maps.size());
        
        // Process item stores for each map
        for (const auto& pMap : maps) {
            if (!ProcessItemStoresForMap(pMap.get())) {
                Logger::Error("ProcessMapItemStores - Failed to process item stores for map: %s", 
                             pMap->GetMapCode().c_str());
                return false;
            }
        }
        
        Logger::Info("ProcessMapItemStores - Successfully processed item stores for all maps");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("ProcessMapItemStores - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process item stores for a specific map
 */
bool CMapOperation::ProcessItemStoresForMap(CMapData* pMapData) {
    try {
        if (!pMapData) {
            Logger::Error("ProcessItemStoresForMap - Invalid map data pointer");
            return false;
        }
        
        // Get item store list for this map
        auto itemStores = pMapData->GetItemStoreList();
        if (itemStores.empty()) {
            Logger::Debug("ProcessItemStoresForMap - No item stores for map: %s", pMapData->GetMapCode().c_str());
            return true; // Not an error
        }
        
        Logger::Debug("ProcessItemStoresForMap - Processing %zu item stores for map: %s", 
                     itemStores.size(), pMapData->GetMapCode().c_str());
        
        // Process each item store
        for (const auto& pItemStore : itemStores) {
            if (!ProcessSingleItemStore(pItemStore.get(), pMapData)) {
                Logger::Error("ProcessItemStoresForMap - Failed to process item store in map: %s", 
                             pMapData->GetMapCode().c_str());
                return false;
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("ProcessItemStoresForMap - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process a single item store
 */
bool CMapOperation::ProcessSingleItemStore(CItemStore* pItemStore, CMapData* pMapData) {
    try {
        if (!pItemStore || !pMapData) {
            Logger::Error("ProcessSingleItemStore - Invalid parameters");
            return false;
        }
        
        // Initialize item store
        if (!pItemStore->Initialize()) {
            Logger::Error("ProcessSingleItemStore - Failed to initialize item store");
            return false;
        }
        
        // Link item store to map
        pItemStore->SetParentMap(pMapData);
        
        // Create merchant for item store if needed
        if (pItemStore->RequiresMerchant()) {
            auto pMerchant = std::make_unique<CMerchant>();
            if (!pMerchant->Initialize(pItemStore)) {
                Logger::Error("ProcessSingleItemStore - Failed to initialize merchant for item store");
                return false;
            }
            
            pItemStore->SetMerchant(std::move(pMerchant));
        }
        
        Logger::Debug("ProcessSingleItemStore - Successfully processed item store");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("ProcessSingleItemStore - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create NPCs for all maps
 */
bool CMapOperation::CreateMapNPCs() {
    try {
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable) {
            Logger::Error("CreateMapNPCs - Map data table not available");
            return false;
        }

        // Get all registered maps
        auto maps = pMapDataTable->GetAllMaps();

        Logger::Info("CreateMapNPCs - Creating NPCs for %zu maps", maps.size());

        // Create NPCs for each map
        for (const auto& pMap : maps) {
            if (!CreateNPCsForMap(pMap.get())) {
                Logger::Error("CreateMapNPCs - Failed to create NPCs for map: %s",
                             pMap->GetMapCode().c_str());
                return false;
            }
        }

        Logger::Info("CreateMapNPCs - Successfully created NPCs for all maps");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CreateMapNPCs - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create NPCs for a specific map
 */
bool CMapOperation::CreateNPCsForMap(CMapData* pMapData) {
    try {
        if (!pMapData) {
            Logger::Error("CreateNPCsForMap - Invalid map data pointer");
            return false;
        }

        // Get NPC creation data for this map
        auto npcCreateDataList = pMapData->GetNPCCreateDataList();
        if (npcCreateDataList.empty()) {
            Logger::Debug("CreateNPCsForMap - No NPCs to create for map: %s", pMapData->GetMapCode().c_str());
            return true; // Not an error
        }

        Logger::Debug("CreateNPCsForMap - Creating %zu NPCs for map: %s",
                     npcCreateDataList.size(), pMapData->GetMapCode().c_str());

        // Create each NPC
        for (const auto& createData : npcCreateDataList) {
            if (!CreateSingleNPC(createData, pMapData)) {
                Logger::Error("CreateNPCsForMap - Failed to create NPC in map: %s",
                             pMapData->GetMapCode().c_str());
                return false;
            }
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CreateNPCsForMap - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create a single NPC
 */
bool CMapOperation::CreateSingleNPC(const _npc_create_setdata& createData, CMapData* pMapData) {
    try {
        // Validate NPC creation data
        if (!MapStructureUtils::IsValidNpcCreateData(&createData)) {
            Logger::Error("CreateSingleNPC - Invalid NPC creation data");
            return false;
        }

        if (!pMapData) {
            Logger::Error("CreateSingleNPC - Invalid map data pointer");
            return false;
        }

        // Validate position
        if (!MapStructureUtils::IsValidPosition(createData.m_fStartPos)) {
            Logger::Error("CreateSingleNPC - Invalid NPC start position");
            return false;
        }

        // Create NPC through NPC system
        // Note: This would interface with the actual NPC creation system
        // For now, we'll simulate the creation process

        Logger::Debug("CreateSingleNPC - Creating NPC at position (%.2f, %.2f, %.2f) on layer %d",
                     createData.m_fStartPos[0], createData.m_fStartPos[1], createData.m_fStartPos[2],
                     createData.m_nLayerIndex);

        // Link item store if this is a merchant NPC
        if (createData.m_pLinkItemStore) {
            Logger::Debug("CreateSingleNPC - Linking item store to merchant NPC");
            // Link the item store to the NPC
            // This would be handled by the actual NPC creation system
        }

        // Register NPC with map
        if (!pMapData->RegisterNPC(createData)) {
            Logger::Error("CreateSingleNPC - Failed to register NPC with map");
            return false;
        }

        Logger::Debug("CreateSingleNPC - Successfully created NPC");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CreateSingleNPC - Exception: %s", e.what());
        return false;
    }
}

/**
 * Finalize map loading process
 */
bool CMapOperation::FinalizeMapLoading() {
    try {
        // Validate all loaded maps
        if (!ValidateLoadedMaps()) {
            Logger::Error("FinalizeMapLoading - Map validation failed");
            return false;
        }

        // Initialize map connections
        if (!InitializeMapConnections()) {
            Logger::Error("FinalizeMapLoading - Failed to initialize map connections");
            return false;
        }

        // Set map system as loaded
        m_bMapSystemLoaded = true;

        Logger::Info("FinalizeMapLoading - Map loading finalization completed successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("FinalizeMapLoading - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate all loaded maps
 */
bool CMapOperation::ValidateLoadedMaps() {
    try {
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable) {
            Logger::Error("ValidateLoadedMaps - Map data table not available");
            return false;
        }

        auto maps = pMapDataTable->GetAllMaps();
        if (maps.empty()) {
            Logger::Warning("ValidateLoadedMaps - No maps loaded");
            return true; // Not necessarily an error
        }

        Logger::Info("ValidateLoadedMaps - Validating %zu loaded maps", maps.size());

        // Validate each map
        for (const auto& pMap : maps) {
            if (!pMap->Validate()) {
                Logger::Error("ValidateLoadedMaps - Map validation failed: %s", pMap->GetMapCode().c_str());
                return false;
            }
        }

        Logger::Info("ValidateLoadedMaps - All maps validated successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("ValidateLoadedMaps - Exception: %s", e.what());
        return false;
    }
}

/**
 * Initialize map connections and portals
 */
bool CMapOperation::InitializeMapConnections() {
    try {
        auto* pMapDataTable = CMapDataTable::GetInstance();
        if (!pMapDataTable) {
            Logger::Error("InitializeMapConnections - Map data table not available");
            return false;
        }

        // Initialize portal connections between maps
        if (!pMapDataTable->InitializePortalConnections()) {
            Logger::Error("InitializeMapConnections - Failed to initialize portal connections");
            return false;
        }

        // Validate portal links
        if (!pMapDataTable->ValidatePortalLinks()) {
            Logger::Error("InitializeMapConnections - Portal link validation failed");
            return false;
        }

        Logger::Info("InitializeMapConnections - Map connections initialized successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("InitializeMapConnections - Exception: %s", e.what());
        return false;
    }
}
