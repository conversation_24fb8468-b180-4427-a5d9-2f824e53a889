/**
 * @file CMainThread_PlayerManagement.cpp
 * @brief Implementation of player management system for CMainThread
 * 
 * Refactored player management system that handles player initialization,
 * character management, player state tracking, and related operations.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_PlayerManagement.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <algorithm>
#include <cassert>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External global player objects (2532 slots each)
extern CUserDB g_UserDB[2532];
extern CPartyPlayer g_PartyPlayer[2532];
extern CPlayer g_Player[2532];

// External initialization functions
extern void CUserDB_Init(CUserDB* userDB, uint32_t index);
extern void CPartyPlayer_Init(CPartyPlayer* partyPlayer, uint16_t index);
extern bool CPlayer_Init(CPlayer* player, _object_id* pID);
extern void ObjectID_Constructor(_object_id* pID, uint8_t field1, uint8_t objectType, uint32_t index);

// Global player management instance
std::unique_ptr<CMainThreadPlayerManagement> g_PlayerManagement;
std::mutex g_PlayerManagementMutex;

/**
 * @brief Constructor
 */
CMainThreadPlayerManagement::CMainThreadPlayerManagement() {
    m_initStats.Reset();
    m_playerStates.resize(2532, PlayerState::Inactive);
}

/**
 * @brief Destructor
 */
CMainThreadPlayerManagement::~CMainThreadPlayerManagement() = default;

/**
 * @brief Initialize player objects
 * 
 * Initializes all player-related objects including UserDB, PartyPlayer, and Player.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return PlayerInitResult indicating success or failure
 */
PlayerInitResult CMainThreadPlayerManagement::InitializePlayerObjects(CMainThread* mainThread) {
    try {
        m_initStats.Reset();
        m_mainThread = mainThread;
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting player object initialization..." << std::endl;
        
        if (!mainThread) {
            SetLastError("Invalid CMainThread pointer");
            return PlayerInitResult::InvalidParameter;
        }
        
        // Phase 1: Initialize UserDB objects
        auto result = InitializeUserDatabase();
        if (result != PlayerInitResult::Success) {
            return result;
        }
        
        // Phase 2: Initialize PartyPlayer objects
        result = InitializePartyPlayers();
        if (result != PlayerInitResult::Success) {
            return result;
        }
        
        // Phase 3: Initialize Player objects
        result = InitializePlayers();
        if (result != PlayerInitResult::Success) {
            return result;
        }
        
        // Finalize initialization statistics
        m_initStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return PlayerInitResult::SystemError;
        }
        
        std::cout << std::format("[INFO] Player object initialization completed successfully in {}ms", 
                                m_initStats.GetTotalInitTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} objects)", 
                                m_initStats.GetSuccessRate(), 
                                m_initStats.successfulInits) << std::endl;
        
        return PlayerInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during player object initialization: {}", e.what()));
        return PlayerInitResult::SystemError;
    }
}

/**
 * @brief Legacy ObjectInit function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThreadPlayerManagement::ObjectInit_Legacy(CMainThread* mainThread) {
    try {
        if (!mainThread) {
            std::cerr << "[ERROR] CMainThread pointer is null in ObjectInit" << std::endl;
            return false;
        }
        
        // Create or get global player management instance
        {
            std::lock_guard<std::mutex> lock(g_PlayerManagementMutex);
            if (!g_PlayerManagement) {
                g_PlayerManagement = std::make_unique<CMainThreadPlayerManagement>();
            }
        }
        
        // Initialize player objects
        PlayerInitResult result = g_PlayerManagement->InitializePlayerObjects(mainThread);
        
        if (result != PlayerInitResult::Success) {
            std::cerr << "[ERROR] Player object initialization failed: " 
                      << g_PlayerManagement->GetLastError() << std::endl;
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ObjectInit_Legacy: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in ObjectInit_Legacy" << std::endl;
        return false;
    }
}

/**
 * @brief Initialize UserDB objects
 * @return PlayerInitResult
 */
PlayerInitResult CMainThreadPlayerManagement::InitializeUserDatabase() {
    try {
        auto startTime = std::chrono::steady_clock::now();
        LogInitialization(PlayerObjectType::UserDatabase, false);
        
        auto objectInfo = GetObjectInfo(PlayerObjectType::UserDatabase);
        
        // Initialize all UserDB objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            CUserDB_Init(&g_UserDB[index], index);
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t typeIndex = static_cast<size_t>(PlayerObjectType::UserDatabase);
            m_initStats.initStatus[typeIndex] = true;
            m_initStats.initTime[typeIndex] = duration;
            m_initStats.successfulInits++;
        }
        
        LogInitialization(PlayerObjectType::UserDatabase, true, objectInfo.maxCount,
                         objectInfo.maxCount * objectInfo.objectSize);
        
        return PlayerInitResult::Success;
        
    } catch (const std::exception& e) {
        LogInitialization(PlayerObjectType::UserDatabase, false, 0, 0, e.what());
        return PlayerInitResult::InitializationError;
    }
}

/**
 * @brief Initialize PartyPlayer objects
 * @return PlayerInitResult
 */
PlayerInitResult CMainThreadPlayerManagement::InitializePartyPlayers() {
    try {
        auto startTime = std::chrono::steady_clock::now();
        LogInitialization(PlayerObjectType::PartyPlayer, false);
        
        auto objectInfo = GetObjectInfo(PlayerObjectType::PartyPlayer);
        
        // Initialize all PartyPlayer objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            CPartyPlayer_Init(&g_PartyPlayer[index], static_cast<uint16_t>(index));
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t typeIndex = static_cast<size_t>(PlayerObjectType::PartyPlayer);
            m_initStats.initStatus[typeIndex] = true;
            m_initStats.initTime[typeIndex] = duration;
            m_initStats.successfulInits++;
        }
        
        LogInitialization(PlayerObjectType::PartyPlayer, true, objectInfo.maxCount,
                         objectInfo.maxCount * objectInfo.objectSize);
        
        return PlayerInitResult::Success;
        
    } catch (const std::exception& e) {
        LogInitialization(PlayerObjectType::PartyPlayer, false, 0, 0, e.what());
        return PlayerInitResult::InitializationError;
    }
}

/**
 * @brief Initialize Player objects
 * @return PlayerInitResult
 */
PlayerInitResult CMainThreadPlayerManagement::InitializePlayers() {
    try {
        auto startTime = std::chrono::steady_clock::now();
        LogInitialization(PlayerObjectType::Player, false);
        
        auto objectInfo = GetObjectInfo(PlayerObjectType::Player);
        
        // Initialize all Player objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id playerID;
            CreateObjectID(&playerID, 0, index);  // objectType = 0 for Player
            
            if (!CPlayer_Init(&g_Player[index], &playerID)) {
                LogInitialization(PlayerObjectType::Player, false, 0, 0,
                                std::format("Failed to initialize player {}", index));
                return PlayerInitResult::InitializationError;
            }
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t typeIndex = static_cast<size_t>(PlayerObjectType::Player);
            m_initStats.initStatus[typeIndex] = true;
            m_initStats.initTime[typeIndex] = duration;
            m_initStats.successfulInits++;
        }
        
        LogInitialization(PlayerObjectType::Player, true, objectInfo.maxCount,
                         objectInfo.maxCount * objectInfo.objectSize);
        
        return PlayerInitResult::Success;
        
    } catch (const std::exception& e) {
        LogInitialization(PlayerObjectType::Player, false, 0, 0, e.what());
        return PlayerInitResult::InitializationError;
    }
}

/**
 * @brief Create object ID
 * @param pID Pointer to object ID structure
 * @param objectType Object type
 * @param index Object index
 */
void CMainThreadPlayerManagement::CreateObjectID(_object_id* pID, uint8_t objectType, uint32_t index) {
    if (pID) {
        ObjectID_Constructor(pID, 0, objectType, index);
    }
}

/**
 * @brief Log initialization result
 * @param objectType Type of object initialization
 * @param success Whether initialization succeeded
 * @param count Number of objects initialized
 * @param memoryUsage Memory usage in bytes
 * @param errorMsg Error message if initialization failed
 */
void CMainThreadPlayerManagement::LogInitialization(PlayerObjectType objectType, bool success, 
                                                   uint32_t count, size_t memoryUsage, 
                                                   const std::string& errorMsg) {
    auto objectInfo = GetObjectInfo(objectType);
    
    if (success) {
        std::cout << std::format("[INFO] Initialized {} objects: {} ({}KB)", 
                                objectInfo.name, count, memoryUsage / 1024) << std::endl;
    } else {
        std::cout << std::format("[ERROR] Failed to initialize {}: {}", 
                                objectInfo.name, errorMsg.empty() ? "Unknown error" : errorMsg) << std::endl;
        
        // Update failure statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_initStats.failedInits++;
            if (!errorMsg.empty()) {
                m_initStats.lastError = errorMsg;
            }
        }
    }
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void CMainThreadPlayerManagement::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    
    // Also update statistics
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_initStats.lastError = error;
    }
}

/**
 * @brief Get player initialization statistics
 * @return const reference to initialization statistics
 */
const PlayerInitStats& CMainThreadPlayerManagement::GetInitStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_initStats;
}

/**
 * @brief Get the last initialization error message
 * @return string containing the last error message
 */
std::string CMainThreadPlayerManagement::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get player object information
 * @param objectType The player object type
 * @return PlayerObjectInfo structure with object information
 */
PlayerObjectInfo CMainThreadPlayerManagement::GetObjectInfo(PlayerObjectType objectType) {
    switch (objectType) {
        case PlayerObjectType::UserDatabase:
            return PlayerObjectInfo("UserDatabase", "User database objects", 2532, sizeof(CUserDB), true);
        case PlayerObjectType::PartyPlayer:
            return PlayerObjectInfo("PartyPlayer", "Party player objects", 2532, sizeof(CPartyPlayer), true);
        case PlayerObjectType::Player:
            return PlayerObjectInfo("Player", "Player character objects", 2532, sizeof(CPlayer), true);
        default:
            return PlayerObjectInfo("Unknown", "Unknown object type", 0, 0, false);
    }
}
