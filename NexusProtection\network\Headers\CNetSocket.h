#pragma once

#include <winsock2.h>
#include <ws2tcpip.h>
#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <functional>

// Forward declarations
class CNetIndexList;

/**
 * Socket states
 */
enum class ESocketState : int {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    LISTENING = 3,
    ERROR = 4,
    CLOSING = 5
};

/**
 * Network error codes
 */
enum class ENetworkError : int {
    SUCCESS = 0,
    SOCKET_ERROR = 1,
    CONNECTION_FAILED = 2,
    SEND_FAILED = 3,
    RECV_FAILED = 4,
    TIMEOUT = 5,
    BUFFER_FULL = 6,
    INVALID_PARAMETER = 7,
    NETWORK_DOWN = 8
};

/**
 * Socket configuration
 */
struct SocketConfig {
    int sendBufferSize;
    int recvBufferSize;
    int sendTimeout;
    int recvTimeout;
    bool keepAlive;
    bool noDelay;
    bool reuseAddr;
    int backlogSize;
    
    SocketConfig() 
        : sendBufferSize(65536), recvBufferSize(65536),
          sendTimeout(30000), recvTimeout(30000),
          keepAlive(true), noDelay(true), reuseAddr(true),
          backlogSize(128) {}
};

/**
 * Network statistics
 */
struct NetworkStats {
    std::atomic<uint64_t> dwAcceptNum;
    std::atomic<uint64_t> dwTotalSendNum;
    std::atomic<uint64_t> dwTotalRecvNum;
    std::atomic<uint64_t> dwTotalSendErrNum;
    std::atomic<uint64_t> dwTotalRecvErrNum;
    std::atomic<uint64_t> dwTotalSendBlockNum;
    std::atomic<uint64_t> dwTotalRecvBlockNum;
    std::atomic<uint64_t> dwConnectionCount;
    std::atomic<uint64_t> dwDisconnectionCount;
    
    NetworkStats() 
        : dwAcceptNum(0), dwTotalSendNum(0), dwTotalRecvNum(0),
          dwTotalSendErrNum(0), dwTotalRecvErrNum(0),
          dwTotalSendBlockNum(0), dwTotalRecvBlockNum(0),
          dwConnectionCount(0), dwDisconnectionCount(0) {}
};

/**
 * Individual socket information
 */
struct SocketInfo {
    SOCKET m_Socket;
    sockaddr_in m_Addr;
    ESocketState m_State;
    bool m_bAccept;
    bool m_bEnterCheck;
    uint32_t m_dwLastCloseTime;
    uint32_t m_dwConnectTime;
    uint32_t m_dwLastActivity;
    uint32_t m_dwTotalSendBlock;
    uint32_t m_dwTotalRecvBlock;
    std::string m_szRemoteIP;
    uint16_t m_wRemotePort;
    
    SocketInfo() 
        : m_Socket(INVALID_SOCKET), m_State(ESocketState::DISCONNECTED),
          m_bAccept(false), m_bEnterCheck(false),
          m_dwLastCloseTime(0), m_dwConnectTime(0), m_dwLastActivity(0),
          m_dwTotalSendBlock(0), m_dwTotalRecvBlock(0), m_wRemotePort(0) {
        memset(&m_Addr, 0, sizeof(m_Addr));
    }
    
    void InitParam() {
        m_Socket = INVALID_SOCKET;
        m_State = ESocketState::DISCONNECTED;
        m_bAccept = false;
        m_bEnterCheck = false;
        m_dwLastCloseTime = timeGetTime();
        m_dwConnectTime = 0;
        m_dwLastActivity = 0;
        m_dwTotalSendBlock = 0;
        m_dwTotalRecvBlock = 0;
        m_szRemoteIP.clear();
        m_wRemotePort = 0;
        memset(&m_Addr, 0, sizeof(m_Addr));
    }
};

/**
 * CNetSocket - Modern network socket management system
 * Refactored from 0CNetSocketQEAAXZ_14047DB60.c
 */
class CNetSocket {
public:
    /**
     * Constructor
     */
    CNetSocket();
    
    /**
     * Virtual destructor
     */
    virtual ~CNetSocket();
    
    /**
     * Initialize socket system
     * @param maxSockets Maximum number of sockets
     * @param config Socket configuration
     * @return true if initialization successful
     */
    bool Initialize(uint32_t maxSockets, const SocketConfig& config = SocketConfig());
    
    /**
     * Release socket system
     */
    void Release();
    
    /**
     * Create and bind listening socket
     * @param port Port to listen on
     * @param bindIP IP address to bind to (empty for INADDR_ANY)
     * @return true if creation successful
     */
    bool CreateListenSocket(uint16_t port, const std::string& bindIP = "");
    
    /**
     * Start listening for connections
     * @return true if listening started successfully
     */
    bool StartListening();
    
    /**
     * Accept incoming connection
     * @param socketIndex Output socket index
     * @return true if connection accepted
     */
    bool AcceptConnection(uint32_t& socketIndex);
    
    /**
     * Connect to remote server
     * @param socketIndex Socket index to use
     * @param pAddr Remote address
     * @return Connection result code
     */
    int Connect(uint32_t socketIndex, sockaddr_in* pAddr);
    
    /**
     * Send data
     * @param socketIndex Socket index
     * @param pBuf Data buffer
     * @param nSize Data size
     * @param pnRet Output bytes sent
     * @return true if send successful
     */
    bool Send(uint32_t socketIndex, const char* pBuf, int nSize, int* pnRet);
    
    /**
     * Receive data
     * @param socketIndex Socket index
     * @param pBuf Receive buffer
     * @param nBufMaxSize Buffer size
     * @param pnRet Output bytes received
     * @return true if receive successful
     */
    bool Recv(uint32_t socketIndex, char* pBuf, int nBufMaxSize, int* pnRet);
    
    /**
     * Close socket
     * @param socketIndex Socket index
     * @return true if close successful
     */
    bool CloseSocket(uint32_t socketIndex);
    
    /**
     * Check if socket is connected
     * @param socketIndex Socket index
     * @return true if connected
     */
    bool IsConnected(uint32_t socketIndex) const;
    
    /**
     * Get socket state
     * @param socketIndex Socket index
     * @return Socket state
     */
    ESocketState GetSocketState(uint32_t socketIndex) const;
    
    /**
     * Get socket information
     * @param socketIndex Socket index
     * @return Socket information
     */
    const SocketInfo* GetSocketInfo(uint32_t socketIndex) const;
    
    /**
     * Get network statistics
     * @return Network statistics
     */
    const NetworkStats& GetNetworkStats() const;
    
    /**
     * Empty socket buffer
     * @param socketIndex Socket index
     */
    void EmptySocketBuffer(uint32_t socketIndex);
    
    /**
     * Set socket options
     * @param socketIndex Socket index
     * @param config Socket configuration
     * @return true if options set successfully
     */
    bool SetSocketOptions(uint32_t socketIndex, const SocketConfig& config);
    
    /**
     * Get remote address string
     * @param socketIndex Socket index
     * @return Remote address as string
     */
    std::string GetRemoteAddress(uint32_t socketIndex) const;
    
    /**
     * Check for socket activity
     * @param timeoutMs Timeout in milliseconds
     * @return Number of active sockets
     */
    int CheckSocketActivity(int timeoutMs = 0);
    
    /**
     * Cleanup inactive sockets
     * @param timeoutSeconds Inactivity timeout in seconds
     * @return Number of sockets cleaned up
     */
    uint32_t CleanupInactiveSockets(uint32_t timeoutSeconds = 300);
    
    /**
     * Get socket statistics as string
     * @return Formatted statistics
     */
    std::string GetStatsString() const;
    
    /**
     * Enable/disable IP checking
     * @param enable Enable IP checking
     */
    void SetIPCheckEnabled(bool enable);
    
    /**
     * Add IP to check list
     * @param ipAddress IP address to add
     * @return true if added successfully
     */
    bool AddIPToCheckList(const std::string& ipAddress);
    
    /**
     * Remove IP from check list
     * @param ipAddress IP address to remove
     * @return true if removed successfully
     */
    bool RemoveIPFromCheckList(const std::string& ipAddress);

protected:
    // Socket management
    std::vector<SocketInfo> m_Socket;
    uint32_t m_dwMaxSockets;
    SOCKET m_ListenSocket;
    SocketConfig m_Config;
    
    // State management
    std::atomic<bool> m_bSetSocket;
    std::atomic<bool> m_bListening;
    std::atomic<bool> m_bIPCheckEnabled;
    
    // Statistics
    NetworkStats m_TotalCount;
    
    // IP checking
    std::unique_ptr<CNetIndexList> m_pIPCheckList;
    std::unique_ptr<CNetIndexList> m_pIPCheckEmptyList;
    
    // Thread safety
    mutable std::mutex m_SocketMutex;
    mutable std::mutex m_StatsMutex;
    mutable std::mutex m_IPCheckMutex;
    
    // Error handling
    ENetworkError m_LastError;
    std::string m_LastErrorMessage;
    
    /**
     * Initialize Winsock
     * @return true if initialization successful
     */
    bool InitializeWinsock();
    
    /**
     * Cleanup Winsock
     */
    void CleanupWinsock();
    
    /**
     * Create socket with options
     * @param socketType Socket type
     * @param protocol Protocol
     * @return Created socket or INVALID_SOCKET
     */
    SOCKET CreateSocketWithOptions(int socketType = SOCK_STREAM, int protocol = IPPROTO_TCP);
    
    /**
     * Set socket non-blocking
     * @param socket Socket to modify
     * @param nonBlocking Non-blocking flag
     * @return true if successful
     */
    bool SetSocketNonBlocking(SOCKET socket, bool nonBlocking = true);
    
    /**
     * Update socket activity
     * @param socketIndex Socket index
     */
    void UpdateSocketActivity(uint32_t socketIndex);
    
    /**
     * Handle socket error
     * @param socketIndex Socket index
     * @param operation Operation that failed
     * @param errorCode Error code
     */
    void HandleSocketError(uint32_t socketIndex, const std::string& operation, int errorCode);
    
    /**
     * Validate socket index
     * @param socketIndex Socket index to validate
     * @return true if valid
     */
    bool ValidateSocketIndex(uint32_t socketIndex) const;
    
    /**
     * Get socket error message
     * @param errorCode Error code
     * @return Error message
     */
    std::string GetSocketErrorMessage(int errorCode) const;
    
    /**
     * Update statistics
     * @param operation Operation type
     * @param success Success flag
     * @param bytes Bytes transferred
     */
    void UpdateStats(const std::string& operation, bool success, uint32_t bytes = 0);
    
    /**
     * Check IP address against filter
     * @param ipAddress IP address to check
     * @return true if allowed
     */
    bool CheckIPAddress(const std::string& ipAddress) const;
};
