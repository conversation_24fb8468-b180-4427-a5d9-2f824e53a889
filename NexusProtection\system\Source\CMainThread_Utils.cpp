/**
 * @file CMainThread_Utils.cpp
 * @brief Utility functions for CMainThread system initialization
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>

/**
 * @brief Get initialization statistics
 * @return const reference to initialization statistics
 */
const SystemInitializationStats& CMainThread::GetInitializationStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_initStats;
}

/**
 * @brief Check if a specific system component is initialized
 * @param component The system component to check
 * @return true if initialized, false otherwise
 */
bool CMainThread::IsComponentInitialized(SystemComponent component) const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    size_t index = static_cast<size_t>(component);
    if (index >= m_initStats.componentStatus.size()) {
        return false;
    }
    return m_initStats.componentStatus[index];
}

/**
 * @brief Get the last initialization error message
 * @return string containing the last error message
 */
std::string CMainThread::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Force shutdown of all systems
 */
void CMainThread::Shutdown() {
    try {
        m_shutdownRequested = true;
        
        // Wait for background threads to finish
        for (auto& thread : m_backgroundThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        m_backgroundThreads.clear();
        
        // Reset system state
        m_bWorldOpen = false;
        m_bWorldService = false;
        m_bRuleThread = false;
        m_bDQSThread = false;
        
        std::cout << "[INFO] CMainThread shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception during shutdown: " << e.what() << std::endl;
    }
}

/**
 * @brief Check if the system is fully initialized
 * @return true if all critical systems are initialized
 */
bool CMainThread::IsFullyInitialized() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    // Check critical components
    const std::vector<SystemComponent> criticalComponents = {
        SystemComponent::ExceptionReporting,
        SystemComponent::LoggingSystem,
        SystemComponent::FileSystem,
        SystemComponent::DataFileInit,
        SystemComponent::ObjectInit,
        SystemComponent::NetworkInit,
        SystemComponent::MapOperation,
        SystemComponent::AsyncLogger
    };
    
    for (const auto& component : criticalComponents) {
        size_t index = static_cast<size_t>(component);
        if (index >= m_initStats.componentStatus.size() || !m_initStats.componentStatus[index]) {
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Log component initialization status
 * @param component The system component
 * @param success Whether initialization was successful
 * @param errorMsg Error message if failed
 */
void CMainThread::LogComponentInitialization(SystemComponent component, bool success, const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(component);
    if (index >= m_initStats.componentStatus.size()) {
        return;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_initStats.startTime);
    
    m_initStats.componentStatus[index] = success;
    m_initStats.componentInitTime[index] = duration;
    
    if (success) {
        m_initStats.successfulComponents++;
        std::cout << std::format("[INFO] Component {} initialized successfully in {}ms", 
                                static_cast<int>(component), duration.count()) << std::endl;
    } else {
        m_initStats.failedComponents++;
        std::string fullError = std::format("Component {} failed: {}", 
                                          static_cast<int>(component), errorMsg);
        m_initStats.lastError = fullError;
        std::cerr << "[ERROR] " << fullError << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message to set
 */
void CMainThread::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    std::cerr << "[ERROR] " << error << std::endl;
}

/**
 * @brief Initialize timers
 */
void CMainThread::InitializeTimers() {
    if (m_timerSystem) {
        m_timerSystem->Initialize();
    }
}

/**
 * @brief Start background threads
 */
void CMainThread::StartBackgroundThreads() {
    try {
        // Start rule thread
        m_bRuleThread = true;
        m_backgroundThreads.emplace_back([this]() {
            RuleThread(this);
        });
        
        // Start DQS thread
        m_bDQSThread = true;
        m_backgroundThreads.emplace_back([this]() {
            DQSThread(this);
        });
        
        std::cout << "[INFO] Background threads started successfully" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to start background threads: " << e.what() << std::endl;
        throw;
    }
}

/**
 * @brief Rule thread function (static)
 * @param pParam Pointer to CMainThread instance
 */
void CMainThread::RuleThread(void* pParam) {
    auto* mainThread = static_cast<CMainThread*>(pParam);
    if (!mainThread) {
        return;
    }
    
    std::cout << "[INFO] Rule thread started" << std::endl;
    
    try {
        while (mainThread->m_bRuleThread && !mainThread->m_shutdownRequested) {
            // Rule thread processing logic would go here
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in rule thread: " << e.what() << std::endl;
    }
    
    std::cout << "[INFO] Rule thread ended" << std::endl;
}

/**
 * @brief DQS thread function (static)
 * @param pParam Pointer to CMainThread instance
 */
void CMainThread::DQSThread(void* pParam) {
    auto* mainThread = static_cast<CMainThread*>(pParam);
    if (!mainThread) {
        return;
    }
    
    std::cout << "[INFO] DQS thread started" << std::endl;
    
    try {
        while (mainThread->m_bDQSThread && !mainThread->m_shutdownRequested) {
            // DQS thread processing logic would go here
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in DQS thread: " << e.what() << std::endl;
    }
    
    std::cout << "[INFO] DQS thread ended" << std::endl;
}

/**
 * @brief Convert SystemComponent enum to string for logging
 * @param component The system component
 * @return String representation of the component
 */
std::string SystemComponentToString(SystemComponent component) {
    switch (component) {
        case SystemComponent::ExceptionReporting: return "ExceptionReporting";
        case SystemComponent::LoggingSystem: return "LoggingSystem";
        case SystemComponent::FileSystem: return "FileSystem";
        case SystemComponent::INILoader: return "INILoader";
        case SystemComponent::DefineChecker: return "DefineChecker";
        case SystemComponent::DatabaseSizeChecker: return "DatabaseSizeChecker";
        case SystemComponent::DataFileInit: return "DataFileInit";
        case SystemComponent::ObjectInit: return "ObjectInit";
        case SystemComponent::NetworkInit: return "NetworkInit";
        case SystemComponent::MapOperation: return "MapOperation";
        case SystemComponent::MonsterEventRespawn: return "MonsterEventRespawn";
        case SystemComponent::MonsterEventSet: return "MonsterEventSet";
        case SystemComponent::AsyncLogger: return "AsyncLogger";
        case SystemComponent::TimeLimitManager: return "TimeLimitManager";
        case SystemComponent::DarkHoleDungeonQuest: return "DarkHoleDungeonQuest";
        case SystemComponent::GuildBattleController: return "GuildBattleController";
        case SystemComponent::TotalGuildRankManager: return "TotalGuildRankManager";
        case SystemComponent::WeeklyGuildRankManager: return "WeeklyGuildRankManager";
        case SystemComponent::GuildRoomSystem: return "GuildRoomSystem";
        case SystemComponent::HonorGuild: return "HonorGuild";
        case SystemComponent::CandidateManager: return "CandidateManager";
        case SystemComponent::PatriarchElectProcessor: return "PatriarchElectProcessor";
        case SystemComponent::AutominePersonalManager: return "AutominePersonalManager";
        case SystemComponent::UnmannedTraderController: return "UnmannedTraderController";
        case SystemComponent::LogTypeDBTaskManager: return "LogTypeDBTaskManager";
        case SystemComponent::TimeItem: return "TimeItem";
        case SystemComponent::CashDBWorkManager: return "CashDBWorkManager";
        case SystemComponent::CashItemRemoteStore: return "CashItemRemoteStore";
        case SystemComponent::GoldenBoxItemManager: return "GoldenBoxItemManager";
        case SystemComponent::PostSystemManager: return "PostSystemManager";
        case SystemComponent::PvpUserAndGuildRankingSystem: return "PvpUserAndGuildRankingSystem";
        case SystemComponent::MoveMapLimitManager: return "MoveMapLimitManager";
        case SystemComponent::RaceBuffManager: return "RaceBuffManager";
        case SystemComponent::ExchangeEvent: return "ExchangeEvent";
        case SystemComponent::PcBangFavor: return "PcBangFavor";
        case SystemComponent::ActionPointSystemManager: return "ActionPointSystemManager";
        case SystemComponent::LuaScriptManager: return "LuaScriptManager";
        case SystemComponent::Cryptor: return "Cryptor";
        case SystemComponent::BossMonsterScheduleSystem: return "BossMonsterScheduleSystem";
        case SystemComponent::StaticMemberPlayer: return "StaticMemberPlayer";
        case SystemComponent::BillingManager: return "BillingManager";
        case SystemComponent::RuleThread: return "RuleThread";
        case SystemComponent::DQSThread: return "DQSThread";
        case SystemComponent::ConnectionMonitoring: return "ConnectionMonitoring";
        case SystemComponent::SystemTowers: return "SystemTowers";
        case SystemComponent::WebAgentServer: return "WebAgentServer";
        case SystemComponent::ControllServer: return "ControllServer";
        case SystemComponent::CheatDetection: return "CheatDetection";
        default: return "Unknown";
    }
}
