/*
 * CMonsterAttack_Core.cpp - Monster Attack System Core Implementation
 * Refactored from 0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CMonsterAttack.h"
#include "../../combat/Headers/CDamageProcessor.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CMonster.h"
#include "../../world/Headers/CMonsterSkill.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>
#include <random>

// External dependencies
extern uint32_t GetLoopTime();
extern void memcpy_0(void* dest, const void* src, size_t size);
extern float ffloor(float value);

// Static member initialization
_attack_param CAttack::s_DefParam;

/**
 * _attack_param constructor
 */
_attack_param::_attack_param() 
    : pDst(nullptr)
    , nPart(0)
    , nClass(0)
    , nTol(-1)
    , nMinAF(0)
    , nMaxAF(500)
    , nMinSel(0)
    , nMaxSel(100)
    , nExtentRange(20)
    , nShotNum(1)
    , nAddAttPnt(0)
    , pFld(nullptr)
    , byEffectCode(0)
    , nLevel(0)
    , nMastery(0)
    , nMaxAttackPnt(0)
    , bPassCount(false)
    , nAttactType(0) {
    
    fArea.fill(0.0f);
}

/**
 * Reset attack parameters
 */
void _attack_param::Reset() {
    pDst = nullptr;
    nPart = 0;
    nClass = 0;
    nTol = -1;
    nMinAF = 0;
    nMaxAF = 500;
    nMinSel = 0;
    nMaxSel = 100;
    nExtentRange = 20;
    nShotNum = 1;
    nAddAttPnt = 0;
    pFld = nullptr;
    byEffectCode = 0;
    nLevel = 0;
    nMastery = 0;
    nMaxAttackPnt = 0;
    bPassCount = false;
    nAttactType = 0;
    fArea.fill(0.0f);
}

/**
 * Validate attack parameters
 */
bool _attack_param::IsValid() const {
    return (nMinAF >= 0 && nMaxAF >= nMinAF && 
            nMinSel >= 0 && nMaxSel >= nMinSel &&
            nExtentRange >= 0 && nShotNum > 0);
}

/**
 * _be_damaged_char constructor
 */
_be_damaged_char::_be_damaged_char() 
    : m_pChar(nullptr)
    , m_nDamage(0)
    , m_bCritical(false) {
}

/**
 * _be_damaged_char constructor with parameters
 */
_be_damaged_char::_be_damaged_char(CCharacter* pChar, int damage, bool critical)
    : m_pChar(pChar)
    , m_nDamage(damage)
    , m_bCritical(critical) {
}

/**
 * Reset damage character data
 */
void _be_damaged_char::Reset() {
    m_pChar = nullptr;
    m_nDamage = 0;
    m_bCritical = false;
}

/**
 * Validate damage character data
 */
bool _be_damaged_char::IsValid() const {
    return (m_pChar != nullptr && m_nDamage >= 0);
}

/**
 * MonsterAttackStatistics constructor
 */
MonsterAttackStatistics::MonsterAttackStatistics() 
    : totalAttacks(0)
    , successfulAttacks(0)
    , failedAttacks(0)
    , criticalHits(0)
    , skillAttacks(0)
    , generalAttacks(0)
    , totalDamageDealt(0) {
}

/**
 * Reset attack statistics
 */
void MonsterAttackStatistics::Reset() {
    totalAttacks = 0;
    successfulAttacks = 0;
    failedAttacks = 0;
    criticalHits = 0;
    skillAttacks = 0;
    generalAttacks = 0;
    totalDamageDealt = 0;
}

/**
 * Get success rate
 */
float MonsterAttackStatistics::GetSuccessRate() const {
    if (totalAttacks == 0) return 0.0f;
    return static_cast<float>(successfulAttacks) / static_cast<float>(totalAttacks);
}

/**
 * Get critical rate
 */
float MonsterAttackStatistics::GetCriticalRate() const {
    if (successfulAttacks == 0) return 0.0f;
    return static_cast<float>(criticalHits) / static_cast<float>(successfulAttacks);
}

/**
 * CAttack constructor
 * Refactored from original CAttack::CAttack
 */
CAttack::CAttack(CCharacter* pAttacker) 
    : m_pAttChar(pAttacker)
    , m_pp(&s_DefParam)
    , m_nDamagedObjNum(0)
    , m_bIsCrtAtt(false)
    , m_bFailure(false) {
    
    try {
        // Initialize damage list with maximum capacity
        m_DamList.reserve(MAX_DAMAGE_LIST);
        
        // Initialize all damage list entries (equivalent to vector constructor iterator)
        for (int i = 0; i < MAX_DAMAGE_LIST; ++i) {
            m_DamList.emplace_back();
        }
        
        Logger::Debug("CAttack::CAttack - Attack system initialized for character %p", pAttacker);
        
    } catch (const std::exception& e) {
        Logger::Error("CAttack::CAttack - Exception during initialization: %s", e.what());
        throw;
    }
}

/**
 * CAttack destructor
 */
CAttack::~CAttack() {
    try {
        Reset();
        Logger::Debug("CAttack::~CAttack - Attack system destroyed");
    } catch (const std::exception& e) {
        Logger::Error("CAttack::~CAttack - Exception during destruction: %s", e.what());
    }
}

/**
 * Perform general attack
 */
bool CAttack::AttackGen(_attack_param* pParam, bool bMustMiss, bool bUseEffBullet) {
    try {
        if (!pParam || !IsValidTarget(pParam->pDst)) {
            m_bFailure = true;
            return false;
        }
        
        // Reset attack state
        Reset();
        m_pp = pParam;
        
        // Calculate damage
        int damage = CalculateDamage(pParam->pDst, pParam);
        
        // Check for forced miss
        if (bMustMiss) {
            damage = 0;
            m_bFailure = true;
        }
        
        // Determine critical hit
        float criticalProb = MonsterAttackUtils::CalculateCriticalProbability(m_pAttChar, pParam->pDst);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        m_bIsCrtAtt = (dis(gen) < criticalProb);
        
        // Apply critical multiplier
        if (m_bIsCrtAtt && damage > 0) {
            damage = static_cast<int>(damage * 1.5f); // 50% critical bonus
        }
        
        // Apply damage
        if (damage > 0 && ApplyDamage(pParam->pDst, damage, m_bIsCrtAtt)) {
            m_bFailure = false;
            return true;
        } else {
            m_bFailure = true;
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttack::AttackGen - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Perform force attack
 */
bool CAttack::AttackForce(_attack_param* pParam, bool bUseEffBullet) {
    try {
        if (!pParam || !IsValidTarget(pParam->pDst)) {
            m_bFailure = true;
            return false;
        }
        
        // Reset attack state
        Reset();
        m_pp = pParam;
        
        // Force attacks always hit with maximum damage
        int damage = pParam->nMaxAF;
        m_bIsCrtAtt = true; // Force attacks are always critical
        
        // Apply damage
        if (ApplyDamage(pParam->pDst, damage, m_bIsCrtAtt)) {
            m_bFailure = false;
            return true;
        } else {
            m_bFailure = true;
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttack::AttackForce - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Calculate damage for target
 */
int CAttack::CalculateDamage(CCharacter* pTarget, _attack_param* pParam) {
    if (!pTarget || !pParam) {
        return 0;
    }
    
    // Basic damage calculation between min and max
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dis(pParam->nMinAF, pParam->nMaxAF);
    
    int baseDamage = dis(gen);
    
    // Apply level-based modifiers if available
    if (m_pAttChar) {
        int attackerLevel = m_pAttChar->GetLevel();
        int targetLevel = pTarget->GetLevel();
        
        // Level difference modifier
        float levelMod = 1.0f + (attackerLevel - targetLevel) * 0.05f;
        levelMod = std::max(0.1f, std::min(2.0f, levelMod)); // Clamp between 0.1 and 2.0
        
        baseDamage = static_cast<int>(baseDamage * levelMod);
    }
    
    return std::max(0, baseDamage);
}

/**
 * Apply damage to target
 */
bool CAttack::ApplyDamage(CCharacter* pTarget, int damage, bool bCritical) {
    try {
        if (!pTarget || damage < 0 || m_nDamagedObjNum >= MAX_DAMAGE_LIST) {
            return false;
        }
        
        // Add to damage list
        if (m_nDamagedObjNum < static_cast<int>(m_DamList.size())) {
            m_DamList[m_nDamagedObjNum].m_pChar = pTarget;
            m_DamList[m_nDamagedObjNum].m_nDamage = damage;
            m_DamList[m_nDamagedObjNum].m_bCritical = bCritical;
            m_nDamagedObjNum++;
        }
        
        // Apply damage to target (this would call the target's SetDamage method)
        // For now, we'll just log the damage application
        Logger::Debug("CAttack::ApplyDamage - Applied %d damage to character %p (Critical: %s)", 
                     damage, pTarget, bCritical ? "Yes" : "No");
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttack::ApplyDamage - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if target is valid for attack
 */
bool CAttack::IsValidTarget(CCharacter* pTarget) const {
    if (!pTarget) {
        return false;
    }
    
    // Basic validity checks
    if (!pTarget->IsAlive()) {
        return false;
    }
    
    // Check if target is attackable (not in safe zone, etc.)
    // This would depend on game-specific logic
    
    return true;
}

/**
 * Reset attack state
 */
void CAttack::Reset() {
    m_nDamagedObjNum = 0;
    m_bIsCrtAtt = false;
    m_bFailure = false;

    // Reset damage list entries
    for (auto& damageEntry : m_DamList) {
        damageEntry.Reset();
    }
}

/**
 * Process area damage for multiple targets
 */
void CAttack::AreaDamageProc(int nLimitRadius, int nAttPower, float* pTar, int nEffAttPower, bool bUseEffBullet) {
    if (!m_pOwner || !pTar) {
        return;
    }

    try {
        // Create damage processor
        CDamageProcessor damageProcessor(m_pOwner);

        // Set up area damage parameters
        AreaDamageParams params;
        params.nLimitRadius = nLimitRadius;
        params.nAttPower = nAttPower;
        params.targetArea[0] = pTar[0];
        params.targetArea[1] = pTar[1];
        params.targetArea[2] = pTar[2];
        params.nEffAttPower = nEffAttPower;
        params.bUseEffBullet = bUseEffBullet;
        params.bIgnoreAllies = true;
        params.fDamageFalloff = 0.8f;

        // Process area damage
        std::vector<DamageResult> results = damageProcessor.ProcessAreaDamage(params, &m_AttParam);

        // Apply damage to all targets
        for (const auto& result : results) {
            if (result.nDamage > 0) {
                damageProcessor.ApplyDamage(result);

                // Update damage statistics
                if (m_nDamagedObjNum < MAX_DAMAGE_LIST) {
                    m_DamList[m_nDamagedObjNum].pTarget = result.pTarget;
                    m_DamList[m_nDamagedObjNum].nDamage = result.nDamage;
                    m_DamList[m_nDamagedObjNum].bCritical = result.bCritical;
                    m_nDamagedObjNum++;
                }
            }
        }

    } catch (const std::exception& e) {
        Logger::LogError("AreaDamageProc failed: " + std::string(e.what()));
        m_bFailure = true;
    }
}

/**
 * Process flash damage in a directional cone
 */
void CAttack::FlashDamageProc(int nLimDist, int nAttPower, int nAngle, int nEffAttPower, bool bUseEffBullet) {
    if (!m_pOwner) {
        return;
    }

    try {
        // Create damage processor
        CDamageProcessor damageProcessor(m_pOwner);

        // Set up flash damage parameters
        FlashDamageParams params;
        params.nLimDist = nLimDist;
        params.nAttPower = nAttPower;
        params.nAngle = nAngle;
        params.nEffAttPower = nEffAttPower;
        params.bUseEffBullet = bUseEffBullet;
        params.bIgnoreAllies = true;
        params.fAngleTolerance = 5.0f;

        // Process flash damage
        std::vector<DamageResult> results = damageProcessor.ProcessFlashDamage(params, &m_AttParam);

        // Apply damage to all targets
        for (const auto& result : results) {
            if (result.nDamage > 0) {
                damageProcessor.ApplyDamage(result);

                // Update damage statistics
                if (m_nDamagedObjNum < MAX_DAMAGE_LIST) {
                    m_DamList[m_nDamagedObjNum].pTarget = result.pTarget;
                    m_DamList[m_nDamagedObjNum].nDamage = result.nDamage;
                    m_DamList[m_nDamagedObjNum].bCritical = result.bCritical;
                    m_nDamagedObjNum++;
                }
            }
        }

    } catch (const std::exception& e) {
        Logger::LogError("FlashDamageProc failed: " + std::string(e.what()));
        m_bFailure = true;
    }
}

/**
 * Process sector damage in a specific area
 */
void CAttack::SectorDamageProc(int nLimitRadius, int nAttPower, int nAngle, int nEffAttPower, int nStartAngle, int nEndAngle, bool bUseEffBullet) {
    if (!m_pOwner) {
        return;
    }

    try {
        // Create damage processor
        CDamageProcessor damageProcessor(m_pOwner);

        // Set up sector damage parameters
        SectorDamageParams params;
        params.nLimitRadius = nLimitRadius;
        params.nAttPower = nAttPower;
        params.nAngle = nAngle;
        params.nEffAttPower = nEffAttPower;
        params.nStartAngle = nStartAngle;
        params.nEndAngle = nEndAngle;
        params.bUseEffBullet = bUseEffBullet;
        params.bIgnoreAllies = true;
        params.fDamageFalloff = 0.9f;

        // Process sector damage
        std::vector<DamageResult> results = damageProcessor.ProcessSectorDamage(params, &m_AttParam);

        // Apply damage to all targets
        for (const auto& result : results) {
            if (result.nDamage > 0) {
                damageProcessor.ApplyDamage(result);

                // Update damage statistics
                if (m_nDamagedObjNum < MAX_DAMAGE_LIST) {
                    m_DamList[m_nDamagedObjNum].pTarget = result.pTarget;
                    m_DamList[m_nDamagedObjNum].nDamage = result.nDamage;
                    m_DamList[m_nDamagedObjNum].bCritical = result.bCritical;
                    m_nDamagedObjNum++;
                }
            }
        }

    } catch (const std::exception& e) {
        Logger::LogError("SectorDamageProc failed: " + std::string(e.what()));
        m_bFailure = true;
    }
}
