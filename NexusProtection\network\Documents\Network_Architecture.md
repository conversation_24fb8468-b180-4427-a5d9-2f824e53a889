# Network Architecture - Modern Network Communication System

## Overview

The NexusProtection network module provides a comprehensive, modern C++ network communication system refactored from the original decompiled network code. The system is designed with high performance, thread safety, and scalability as core principles.

## Original Files Refactored

This system consolidates and modernizes the following decompiled network files:

### Core Network Classes
- `0CNetSocketQEAAXZ_14047DB60.c` → `CNetSocket`
- `CNetIndexList` (supporting class for IP management)

### Network Operations
- Socket creation and management
- Connection handling (accept/connect)
- Data transmission (send/receive)
- Error handling and recovery
- IP filtering and access control
- Network statistics and monitoring

## Architecture Overview

### Class Hierarchy

```
CNetSocket (Core Network Socket Management)
├── CNetIndexList (IP Address Management)
└── Supporting structures:
    ├── SocketInfo (Individual socket data)
    ├── SocketConfig (Configuration parameters)
    ├── NetworkStats (Performance statistics)
    └── Various enums for states and errors
```

### Core Components

#### 1. CNetSocket
**Purpose**: Foundation network socket management and communication
**Key Features**:
- Multi-socket management (up to configurable limit)
- Non-blocking I/O operations
- Automatic connection handling
- Thread-safe operations
- Comprehensive error handling
- Performance monitoring
- IP filtering support

#### 2. CNetIndexList
**Purpose**: IP address filtering and management
**Key Features**:
- Thread-safe IP list management
- Fast IP lookup using hash sets
- Dynamic IP addition/removal
- Bulk operations support

## Key Features

### 1. High Performance
- Non-blocking socket operations
- Efficient socket management
- Optimized data structures
- Minimal memory allocations
- Fast IP filtering

### 2. Thread Safety
- All operations are thread-safe
- Mutex protection for shared resources
- Atomic operations for statistics
- Lock-free read operations where possible

### 3. Scalability
- Configurable maximum socket count
- Efficient socket slot management
- Resource pooling
- Connection cleanup mechanisms

### 4. Reliability
- Comprehensive error handling
- Automatic reconnection support
- Graceful connection shutdown
- Resource leak prevention
- Network failure recovery

### 5. Monitoring
- Detailed network statistics
- Connection tracking
- Performance metrics
- Error rate monitoring
- Activity logging

## Socket Management

### Socket States
```cpp
enum class ESocketState : int {
    DISCONNECTED = 0,    // Socket not connected
    CONNECTING = 1,      // Connection in progress
    CONNECTED = 2,       // Socket connected and ready
    LISTENING = 3,       // Socket listening for connections
    ERROR = 4,           // Socket in error state
    CLOSING = 5          // Socket being closed
};
```

### Socket Configuration
```cpp
struct SocketConfig {
    int sendBufferSize;      // Send buffer size (default: 64KB)
    int recvBufferSize;      // Receive buffer size (default: 64KB)
    int sendTimeout;         // Send timeout in ms (default: 30s)
    int recvTimeout;         // Receive timeout in ms (default: 30s)
    bool keepAlive;          // Enable TCP keep-alive
    bool noDelay;            // Disable Nagle algorithm
    bool reuseAddr;          // Allow address reuse
    int backlogSize;         // Listen backlog size (default: 128)
};
```

## Usage Examples

### Server Setup
```cpp
// Initialize socket system
CNetSocket netSocket;
SocketConfig config;
config.sendBufferSize = 65536;
config.recvBufferSize = 65536;
config.backlogSize = 256;

bool success = netSocket.Initialize(1000, config);  // Max 1000 connections

// Create and start listening
if (success) {
    success = netSocket.CreateListenSocket(8080, "");  // Listen on port 8080
    if (success) {
        success = netSocket.StartListening();
    }
}
```

### Accept Connections
```cpp
// Accept incoming connections
uint32_t socketIndex;
if (netSocket.AcceptConnection(socketIndex)) {
    Logger::LogInfo("New connection accepted on socket " + std::to_string(socketIndex));
    
    // Get connection info
    const SocketInfo* info = netSocket.GetSocketInfo(socketIndex);
    if (info) {
        Logger::LogInfo("Client IP: " + info->m_szRemoteIP);
    }
}
```

### Send/Receive Data
```cpp
// Send data
const char* message = "Hello, client!";
int bytesSent;
bool sendSuccess = netSocket.Send(socketIndex, message, strlen(message), &bytesSent);

// Receive data
char buffer[1024];
int bytesReceived;
bool recvSuccess = netSocket.Recv(socketIndex, buffer, sizeof(buffer), &bytesReceived);

if (recvSuccess && bytesReceived > 0) {
    buffer[bytesReceived] = '\0';  // Null-terminate
    Logger::LogInfo("Received: " + std::string(buffer));
}
```

### IP Filtering
```cpp
// Enable IP filtering
netSocket.SetIPCheckEnabled(true);

// Add allowed IPs
netSocket.AddIPToCheckList("*************");
netSocket.AddIPToCheckList("*********");

// Remove IP from filter
netSocket.RemoveIPFromCheckList("*************");
```

## Network Statistics

### Available Metrics
- **Connection Statistics**:
  - Total connections accepted
  - Current active connections
  - Connection/disconnection counts
  
- **Data Transfer Statistics**:
  - Total bytes sent/received
  - Send/receive operation counts
  - Error counts
  - Blocked operation counts

- **Performance Metrics**:
  - Average connection duration
  - Data transfer rates
  - Error rates
  - Resource utilization

### Usage Example
```cpp
// Get network statistics
const NetworkStats& stats = netSocket.GetNetworkStats();

Logger::LogInfo("Network Statistics:");
Logger::LogInfo("  Connections accepted: " + std::to_string(stats.dwAcceptNum.load()));
Logger::LogInfo("  Total send operations: " + std::to_string(stats.dwTotalSendNum.load()));
Logger::LogInfo("  Total receive operations: " + std::to_string(stats.dwTotalRecvNum.load()));
Logger::LogInfo("  Send errors: " + std::to_string(stats.dwTotalSendErrNum.load()));
Logger::LogInfo("  Receive errors: " + std::to_string(stats.dwTotalRecvErrNum.load()));

// Get formatted statistics string
std::string statsString = netSocket.GetStatsString();
Logger::LogInfo("Detailed stats:\n" + statsString);
```

## Error Handling

### Error Types
```cpp
enum class ENetworkError : int {
    SUCCESS = 0,             // No error
    SOCKET_ERROR = 1,        // General socket error
    CONNECTION_FAILED = 2,   // Connection establishment failed
    SEND_FAILED = 3,         // Data send failed
    RECV_FAILED = 4,         // Data receive failed
    TIMEOUT = 5,             // Operation timed out
    BUFFER_FULL = 6,         // Buffer overflow
    INVALID_PARAMETER = 7,   // Invalid function parameter
    NETWORK_DOWN = 8         // Network subsystem failure
};
```

### Error Recovery
```cpp
// Check for errors after operations
if (!netSocket.Send(socketIndex, data, size, &bytesSent)) {
    std::string error = netSocket.GetLastError();
    Logger::LogError("Send failed: " + error);
    
    // Check if socket is still connected
    if (!netSocket.IsConnected(socketIndex)) {
        Logger::LogInfo("Socket disconnected, cleaning up");
        netSocket.CloseSocket(socketIndex);
    }
}
```

## Performance Optimization

### Best Practices
1. **Buffer Management**:
   - Use appropriate buffer sizes
   - Avoid frequent small sends
   - Implement message batching

2. **Connection Management**:
   - Monitor connection health
   - Implement connection pooling
   - Clean up inactive connections

3. **Error Handling**:
   - Handle WSAEWOULDBLOCK gracefully
   - Implement retry mechanisms
   - Log errors for debugging

4. **Resource Management**:
   - Limit maximum connections
   - Monitor memory usage
   - Implement connection timeouts

### Configuration Tuning
```cpp
SocketConfig highPerformanceConfig;
highPerformanceConfig.sendBufferSize = 131072;    // 128KB
highPerformanceConfig.recvBufferSize = 131072;    // 128KB
highPerformanceConfig.sendTimeout = 10000;        // 10 seconds
highPerformanceConfig.recvTimeout = 10000;        // 10 seconds
highPerformanceConfig.keepAlive = true;
highPerformanceConfig.noDelay = true;             // Disable Nagle
highPerformanceConfig.backlogSize = 512;          // Large backlog
```

## Security Considerations

### IP Filtering
- Implement whitelist/blacklist functionality
- Support for IP ranges and subnets
- Dynamic IP management
- Rate limiting per IP

### Connection Security
- Implement connection limits per IP
- Monitor for suspicious activity
- Automatic blocking of malicious IPs
- Connection timeout enforcement

## Testing Strategy

### Unit Tests
- Socket creation and configuration
- Connection establishment
- Data transmission
- Error handling
- IP filtering functionality

### Integration Tests
- Multi-client scenarios
- High-load testing
- Network failure simulation
- Memory leak detection

### Performance Tests
- Connection throughput
- Data transfer rates
- Memory usage profiling
- CPU utilization monitoring

## Future Enhancements

### Planned Features
1. **SSL/TLS Support**: Secure communication
2. **IPv6 Support**: Modern IP protocol support
3. **UDP Support**: Connectionless communication
4. **Compression**: Data compression for efficiency
5. **Load Balancing**: Connection distribution

### Advanced Features
1. **Connection Pooling**: Reusable connections
2. **Async I/O**: Asynchronous operations
3. **Message Framing**: Protocol-level message handling
4. **Bandwidth Limiting**: Traffic shaping
5. **Network Monitoring**: Real-time network analysis

## Compatibility

### Compiler Support
- Visual Studio 2022 (v143 toolset)
- C++17/C++20 standard compliance
- Windows 10/11 compatibility

### Network Support
- TCP/IPv4 (current implementation)
- Winsock 2.2 API
- Windows networking stack

### Dependencies
- Winsock2 library (ws2_32.lib)
- Standard C++ libraries
- Windows API functions
