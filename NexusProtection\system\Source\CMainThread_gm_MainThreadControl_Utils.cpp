/**
 * @file CMainThread_gm_MainThreadControl_Utils.cpp
 * @brief Utility functions for main thread control system
 * 
 * Contains helper functions for state management, monitoring,
 * and legacy compatibility.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_gm_MainThreadControl.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <thread>

/**
 * @brief Pause control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::PauseControl() {
    try {
        if (GetCurrentState() != ThreadState::Running) {
            return ThreadControlResult::InvalidState;
        }
        
        ChangeState(ThreadState::Paused);
        std::cout << "[INFO] Thread control paused" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to pause control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Resume control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::ResumeControl() {
    try {
        if (GetCurrentState() != ThreadState::Paused) {
            return ThreadControlResult::InvalidState;
        }
        
        ChangeState(ThreadState::Running);
        std::cout << "[INFO] Thread control resumed" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to resume control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Shutdown control system
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::ShutdownControl() {
    try {
        ChangeState(ThreadState::ShuttingDown);
        
        // Stop monitoring
        if (m_monitoringEnabled.load()) {
            StopMonitoring();
        }
        
        // Set shutdown flag
        m_shutdownRequested = true;
        
        // Disable world service and close world
        if (m_mainThread) {
            SetWorldServiceState(m_mainThread, false);
            SetWorldOpenState(m_mainThread, false);
        }
        
        ChangeState(ThreadState::Shutdown);
        std::cout << "[INFO] Thread control shutdown completed" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to shutdown control: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Get status information
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::GetStatusInfo() {
    try {
        ThreadState state = GetCurrentState();
        auto stats = GetControlStats();
        
        std::cout << "[INFO] Thread Control Status:" << std::endl;
        std::cout << "  - Current State: " << ThreadStateToString(state) << std::endl;
        std::cout << "  - Uptime: " << stats.GetUptime().count() << "ms" << std::endl;
        std::cout << "  - Total Commands: " << stats.totalCommands << std::endl;
        std::cout << "  - Success Rate: " << stats.GetSuccessRate() << "%" << std::endl;
        std::cout << "  - Monitoring: " << (m_monitoringEnabled.load() ? "Enabled" : "Disabled") << std::endl;
        
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to get status: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Get statistics information
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::GetStatisticsInfo() {
    try {
        auto stats = GetControlStats();
        
        std::cout << "[INFO] Thread Control Statistics:" << std::endl;
        std::cout << "  - Total Commands: " << stats.totalCommands << std::endl;
        std::cout << "  - Successful Commands: " << stats.successfulCommands << std::endl;
        std::cout << "  - Failed Commands: " << stats.failedCommands << std::endl;
        std::cout << "  - Success Rate: " << stats.GetSuccessRate() << "%" << std::endl;
        std::cout << "  - Uptime: " << stats.GetUptime().count() << "ms" << std::endl;
        
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to get statistics: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Check health status
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::CheckHealthStatus() {
    try {
        bool healthy = IsHealthy();
        ThreadState state = GetCurrentState();
        
        std::cout << "[INFO] Thread Control Health Check:" << std::endl;
        std::cout << "  - Health Status: " << (healthy ? "Healthy" : "Unhealthy") << std::endl;
        std::cout << "  - Current State: " << ThreadStateToString(state) << std::endl;
        std::cout << "  - Main Thread: " << (m_mainThread ? "Valid" : "Invalid") << std::endl;
        std::cout << "  - Monitoring: " << (m_monitoringEnabled.load() ? "Active" : "Inactive") << std::endl;
        
        return healthy ? ThreadControlResult::Success : ThreadControlResult::Failure;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to check health: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Enable world service
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::EnableWorldServiceInternal() {
    try {
        if (m_mainThread) {
            SetWorldServiceState(m_mainThread, true);
            std::cout << "[INFO] World service enabled" << std::endl;
            return ThreadControlResult::Success;
        }
        
        SetLastError("Invalid main thread pointer");
        return ThreadControlResult::SystemError;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to enable world service: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Disable world service
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::DisableWorldServiceInternal() {
    try {
        if (m_mainThread) {
            SetWorldServiceState(m_mainThread, false);
            std::cout << "[INFO] World service disabled" << std::endl;
            return ThreadControlResult::Success;
        }
        
        SetLastError("Invalid main thread pointer");
        return ThreadControlResult::SystemError;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to disable world service: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Open world
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::OpenWorldInternal() {
    try {
        if (m_mainThread) {
            SetWorldOpenState(m_mainThread, true);
            std::cout << "[INFO] World opened" << std::endl;
            return ThreadControlResult::Success;
        }
        
        SetLastError("Invalid main thread pointer");
        return ThreadControlResult::SystemError;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to open world: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Close world
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::CloseWorldInternal() {
    try {
        if (m_mainThread) {
            SetWorldOpenState(m_mainThread, false);
            std::cout << "[INFO] World closed" << std::endl;
            return ThreadControlResult::Success;
        }
        
        SetLastError("Invalid main thread pointer");
        return ThreadControlResult::SystemError;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to close world: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Emergency stop
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::EmergencyStopInternal() {
    try {
        std::cout << "[WARNING] Emergency stop initiated!" << std::endl;
        
        // Immediately change state
        ChangeState(ThreadState::Stopped);
        
        // Stop all services
        if (m_mainThread) {
            SetWorldServiceState(m_mainThread, false);
            SetWorldOpenState(m_mainThread, false);
        }
        
        // Stop monitoring
        m_monitoringEnabled = false;
        
        std::cout << "[WARNING] Emergency stop completed" << std::endl;
        return ThreadControlResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed emergency stop: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Safe shutdown
 * @return ThreadControlResult
 */
ThreadControlResult CMainThreadControl::SafeShutdownInternal() {
    try {
        std::cout << "[INFO] Safe shutdown initiated..." << std::endl;
        
        // Gracefully stop services
        DisableWorldServiceInternal();
        CloseWorldInternal();
        
        // Stop monitoring
        StopMonitoring();
        
        // Final shutdown
        return ShutdownControl();
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed safe shutdown: {}", e.what()));
        return ThreadControlResult::SystemError;
    }
}

/**
 * @brief Change thread state
 * @param newState The new state
 */
void CMainThreadControl::ChangeState(ThreadState newState) {
    ThreadState oldState = m_currentState.exchange(newState);
    
    if (oldState != newState) {
        // Update statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_controlStats.currentState = newState;
        }
        
        // Notify waiting threads
        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_stateCondition.notify_all();
            
            // Call registered callbacks
            for (const auto& callback : m_stateCallbacks) {
                try {
                    callback(oldState, newState);
                } catch (...) {
                    // Ignore callback exceptions
                }
            }
        }
        
        std::cout << "[INFO] Thread state changed: " << ThreadStateToString(oldState) 
                  << " -> " << ThreadStateToString(newState) << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void CMainThreadControl::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    
    // Also update statistics
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_controlStats.lastError = error;
    }
}

/**
 * @brief Log command execution
 * @param command The command that was executed
 * @param success Whether the command succeeded
 * @param errorMsg Error message if command failed
 */
void CMainThreadControl::LogCommand(ThreadControlCommand command, bool success, const std::string& errorMsg) {
    std::string commandStr = ThreadControlCommandToString(command);
    
    if (success) {
        std::cout << "[INFO] Command executed successfully: " << commandStr << std::endl;
    } else {
        std::cout << "[ERROR] Command failed: " << commandStr;
        if (!errorMsg.empty()) {
            std::cout << " - " << errorMsg;
        }
        std::cout << std::endl;
    }
}
