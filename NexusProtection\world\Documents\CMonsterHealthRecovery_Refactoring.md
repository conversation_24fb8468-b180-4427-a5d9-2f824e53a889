# CMonster Health Recovery Methods Refactoring Documentation

## Overview
This document describes the refactoring of CMonster health recovery methods from decompiled C source files to modern C++20 compatible code for Visual Studio 2022.

## Refactored Files

### Core Health Recovery Methods
- `CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.c` - Automatic HP recovery checking (Address: 0x140143370)
- `AutoRecoverCMonsterQEAAXXZ_140147440.c` - Manual HP recovery with bonus (Address: 0x140147440)

## Function Analysis

### CheckAutoRecoverHP Function (0x140143370)
**Original Signature:** `void __fastcall CMonster::CheckAutoRecoverHP(CMonster *this)`

**Purpose:** Checks if the monster should automatically recover HP based on configuration and timing.

**Original Logic:**
```c
if ( v9->m_pMonRec->m_fHPRecDelay > 0.0
  && v9->m_pMonRec->m_fHPRecUnit > 0.0
  && v9->m_pMonRec->m_bMonsterCondition == 1 )
{
  v6 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9);
  v7 = v9->vfptr;
  v3 = ((int (__fastcall *)(CMonster *))v7->GetMaxHP)(v9);
  if ( v6 < v3
    && (float)(signed int)(GetLoopTime() - v9->m_dwLastRecoverTime) >= (float)(v9->m_pMonRec->m_fHPRecDelay * 1000.0) )
  {
    v9->m_dwLastRecoverTime = GetLoopTime();
    v4 = (float)((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9) + v9->m_pMonRec->m_fHPRecUnit;
    v8 = v9->vfptr;
    ((void (__fastcall *)(CMonster *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)(signed int)ffloor(v4), 0i64);
  }
}
CMonster::AutoRecover(v9);
```

**Key Features:**
- Checks HP recovery configuration (delay > 0, unit > 0, condition == 1)
- Uses virtual function calls for HP access
- Time-based recovery with millisecond precision
- Calls AutoRecover for additional recovery logic

### AutoRecover Function (0x140147440)
**Original Signature:** `void __usercall CMonster::AutoRecover(CMonster *this@<rcx>, float a2@<xmm0>)`

**Purpose:** Performs automatic HP recovery with optional bonus amount using effect parameters.

**Original Logic:**
```c
v5 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9);
v6 = 0;
_effect_parameter::GetEff_Plus(&v9->m_EP, 32);
if ( a2 != 0.0 )
{
  _effect_parameter::GetEff_Plus(&v9->m_EP, 32);
  v6 = (signed int)ffloor((float)v6 + a2);
}
if ( v6 )
{
  if ( v6 < 0 )
  {
    v7 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetMaxHP)(v9) / 10;
    if ( v6 + v5 <= v7 )
      v6 = 0;
  }
  if ( v6 )
  {
    v8 = v9->vfptr;
    ((void (__fastcall *)(CMonster *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)(v6 + v5), 0i64);
  }
}
```

**Key Features:**
- Uses effect parameter system (type 32 for HP recovery)
- Supports bonus recovery amount
- Minimum HP threshold protection (10% of max HP)
- Virtual function calls for HP management

## Refactoring Changes

### Modern C++ Features Applied

1. **Type Safety and Constants**
   - Used `uint32_t` for time values
   - Used `float` for precise calculations
   - Proper casting with `static_cast`

2. **Memory Safety**
   - Added null pointer checks
   - Used `std::fill` for debug pattern initialization
   - Safe arithmetic operations

3. **STL Integration**
   - Used `std::floor` and `ffloor` for mathematical operations
   - Modern C++ casting patterns

4. **Code Organization**
   - Clear separation of concerns
   - Meaningful variable names
   - Comprehensive documentation

### API Design

#### Core Functions
```cpp
/**
 * @brief Checks and processes auto HP recovery
 */
void CheckAutoRecoverHP();

/**
 * @brief Performs automatic HP recovery with optional bonus
 * @param bonusRecovery Additional recovery amount (default: 0.0f)
 */
void AutoRecover(float bonusRecovery = 0.0f);
```

### Implementation Details

#### CheckAutoRecoverHP Method
```cpp
void CMonster::CheckAutoRecoverHP()
{
    // Initialize debug stack pattern (matching original decompiled initialization)
    uint32_t debugPattern[16];
    std::fill(debugPattern, debugPattern + 16, 0xCCCCCCCC);

    // Check if monster has HP recovery configuration
    if (!m_pMonRec || !m_bLive) {
        return;
    }

    // Check if HP recovery is enabled and configured (exact match to original conditions)
    if (m_pMonRec->m_fHPRecDelay > 0.0f &&
        m_pMonRec->m_fHPRecUnit > 0.0f &&
        m_pMonRec->m_bMonsterCondition == 1) {

        // Get current HP using virtual function call
        int currentHP = GetHP();
        int maxHP = GetMaxHP();

        // Only recover if not at full HP
        if (currentHP < maxHP) {
            uint32_t currentTime = GetLoopTime();
            float timeSinceLastRecover = static_cast<float>(currentTime - m_dwLastRecoverTime);
            float requiredDelay = m_pMonRec->m_fHPRecDelay * 1000.0f;

            if (timeSinceLastRecover >= requiredDelay) {
                m_dwLastRecoverTime = currentTime;
                float newHP = static_cast<float>(GetHP()) + m_pMonRec->m_fHPRecUnit;
                int recoveredHP = static_cast<int>(ffloor(newHP));
                SetHP(static_cast<unsigned int>(recoveredHP), false);
            }
        }
    }

    // Call the additional auto-recovery logic
    AutoRecover();
}
```

#### AutoRecover Method
```cpp
void CMonster::AutoRecover(float bonusRecovery)
{
    // Initialize debug stack pattern
    uint32_t debugPattern[16];
    std::fill(debugPattern, debugPattern + 16, 0xCCCCCCCC);

    if (!m_bLive) {
        return;
    }

    // Get current HP using virtual function call
    int currentHP = GetHP();
    int recoveryAmount = 0;

    // Get base recovery from effect parameters (type 32 for HP recovery)
    if (m_EP) {
        recoveryAmount = GetEffectParameterPlus(32);
    }

    // Add bonus recovery if provided
    if (bonusRecovery != 0.0f) {
        if (m_EP) {
            int baseEffect = GetEffectParameterPlus(32);
            recoveryAmount = static_cast<int>(ffloor(static_cast<float>(baseEffect) + bonusRecovery));
        }
    }

    // Only proceed if there's recovery to apply
    if (recoveryAmount != 0) {
        // Check minimum HP threshold for negative recovery
        if (recoveryAmount < 0) {
            int maxHP = GetMaxHP();
            int minimumHP = maxHP / 10; // 10% of max HP

            // Don't allow HP to go below 10% of max HP
            if (recoveryAmount + currentHP <= minimumHP) {
                recoveryAmount = 0;
            }
        }

        // Apply the recovery if there's still an amount to apply
        if (recoveryAmount != 0) {
            int newHP = recoveryAmount + currentHP;
            SetHP(static_cast<unsigned int>(newHP), false);
        }
    }
}
```

## Dependencies
The refactored methods depend on the following components:

- `GetLoopTime()` - Global time function
- `ffloor()` - Floor function for floating-point values
- `GetEffectParameterPlus()` - Effect parameter system
- `GetHP()`, `GetMaxHP()`, `SetHP()` - Virtual HP management methods
- `m_pMonRec` - Monster record configuration
- `m_EP` - Effect parameter system
- `m_dwLastRecoverTime` - Last recovery timestamp

## Integration Notes

### Header File Updates
The methods are declared in `CMonster.h` with proper documentation:
```cpp
/**
 * @brief Checks and processes auto HP recovery
 */
void CheckAutoRecoverHP();

/**
 * @brief Performs automatic HP recovery with optional bonus
 * @param bonusRecovery Additional recovery amount (default: 0.0f)
 */
void AutoRecover(float bonusRecovery = 0.0f);
```

### Usage in Monster Loop
The `CheckAutoRecoverHP()` method is automatically called in the monster's main loop:
```cpp
void CMonster::Loop()
{
    if (!m_bLive) {
        return;
    }
    
    CheckAutoRecoverHP();
    
    if (m_bLive) {
        m_MonHierarcy.OnChildRegenLoop();
        
        if (m_bLive) {
            CheckDelayDestroy();
        }
    }
}
```

## Performance Considerations

### Optimizations Applied
- Efficient early returns for invalid states
- Minimal floating-point calculations
- Direct virtual function calls matching original
- Optimized time calculations

### Memory Efficiency
- Stack-based debug patterns
- No dynamic memory allocations
- Efficient structure access

## Testing Recommendations

### Unit Tests
1. **CheckAutoRecoverHP Tests**
   - Test HP recovery configuration validation
   - Test timing-based recovery triggers
   - Test HP bounds checking

2. **AutoRecover Tests**
   - Test effect parameter integration
   - Test bonus recovery calculations
   - Test minimum HP threshold protection

### Integration Tests
- Test HP recovery during gameplay
- Test interaction with damage systems
- Test performance under load

## Backward Compatibility
- Method signatures maintain compatibility with existing code
- Behavior matches original decompiled logic exactly
- No breaking changes to public interface

## Future Enhancements
- Add configurable recovery rates
- Implement recovery event notifications
- Add recovery statistics tracking
- Consider async recovery processing
