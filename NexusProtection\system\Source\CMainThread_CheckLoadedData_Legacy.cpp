/**
 * @file CMainThread_CheckLoadedData_Legacy.cpp
 * @brief Legacy compatibility wrapper for check_loaded_data function
 * 
 * Provides backward compatibility for existing code that calls the original
 * check_loaded_data function. Maintains exact original behavior and signatures.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_CheckLoadedData.h"
#include "CMainThread.h"
#include "CRecordData.h"
#include <iostream>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External message box function
extern void MyMessageBox(const char* title, const char* message);

/**
 * @brief Original check_loaded_data function behavior
 * 
 * This function replicates the exact original behavior of the check_loaded_data
 * function for maximum compatibility with existing code.
 * 
 * @return bool (true for success, false for failure)
 */
bool CheckLoadedData_OriginalBehavior() {
    try {
        // Original function was very simple - just returned true
        // The actual validation was done elsewhere in the codebase
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Exact original check_loaded_data function implementation
 * 
 * This is the most faithful reproduction of the original function,
 * maintaining the exact same logic and behavior.
 * 
 * @return bool (true for success, false for failure)
 */
bool CheckLoadedData_ExactOriginal() {
    // The original function was extremely simple and just returned true
    // All the actual data validation was handled by other systems
    return true;
}

/**
 * @brief C-style function wrapper for exact original compatibility
 * 
 * Provides the exact original C function signature for maximum compatibility.
 * Original: bool __fastcall CMainThread::check_loaded_data(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance (this parameter)
 * @param unused Unused parameter (EDX register in __fastcall)
 * @return bool (true for success, false for failure)
 */
extern "C" bool __fastcall CMainThread_CheckLoadedData_Original(CMainThread* mainThread, void* unused) {
    // Security cookie setup (equivalent to original stack protection)
    uintptr_t localCookie = reinterpret_cast<uintptr_t>(mainThread) ^ _security_cookie;
    
    try {
        // Original function logic was very simple
        if (!mainThread) {
            return false;
        }
        
        // The original function just returned true after basic validation
        // All complex validation was handled by other systems during data loading
        bool result = true;
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uintptr_t>(mainThread) ^ _security_cookie) != localCookie) {
            return false;
        }
        
        return result;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Enhanced legacy wrapper with modern error handling
 * 
 * Provides the original behavior but with enhanced error handling and logging.
 * This version can be used as a drop-in replacement for the original function.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_CheckLoadedData_Enhanced(CMainThread* mainThread) {
    try {
        if (!mainThread) {
            std::cerr << "[ERROR] CMainThread pointer is null in check_loaded_data" << std::endl;
            return false;
        }
        
        // Use the modern validation system but return simple bool result
        CMainThreadCheckLoadedData validator;
        DataValidationResult result = validator.ValidateAllLoadedData(mainThread);
        
        bool success = (result == DataValidationResult::Success);
        
        if (!success) {
            std::cerr << "[ERROR] Data validation failed: " << validator.GetLastError() << std::endl;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in check_loaded_data: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in check_loaded_data" << std::endl;
        return false;
    }
}

/**
 * @brief Simple validation wrapper for basic data checking
 * 
 * Provides a simplified validation that checks only the most critical data.
 * This can be used when full validation is not needed.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_CheckLoadedData_Simple(CMainThread* mainThread) {
    if (!mainThread) {
        return false;
    }
    
    try {
        // Check only the most critical data tables
        if (!CMainThreadCheckLoadedData::ValidateDataTable_Legacy(
                mainThread->GetItemDataTable(), "Item Data")) {
            return false;
        }
        
        if (!CMainThreadCheckLoadedData::ValidateDataTable_Legacy(
                mainThread->GetSkillDataTable(), "Skill Data")) {
            return false;
        }
        
        if (!CMainThreadCheckLoadedData::ValidateDataTable_Legacy(
                mainThread->GetClassTable(), "Class Data")) {
            return false;
        }
        
        if (!CMainThreadCheckLoadedData::ValidateDataTable_Legacy(
                mainThread->GetPlayerTable(), "Player Data")) {
            return false;
        }
        
        if (!CMainThreadCheckLoadedData::ValidateDataTable_Legacy(
                mainThread->GetMonsterTable(), "Monster Data")) {
            return false;
        }
        
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Comprehensive validation wrapper with detailed logging
 * 
 * Provides comprehensive validation with detailed logging for debugging purposes.
 * This version logs all validation steps and provides detailed error information.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_CheckLoadedData_Comprehensive(CMainThread* mainThread) {
    if (!mainThread) {
        std::cout << "[ERROR] CMainThread pointer is null" << std::endl;
        return false;
    }
    
    std::cout << "[INFO] Starting comprehensive data validation..." << std::endl;
    
    try {
        CMainThreadCheckLoadedData validator;
        DataValidationResult result = validator.ValidateAllLoadedData(mainThread);
        
        // Print detailed statistics
        const auto& stats = validator.GetValidationStats();
        std::cout << "[INFO] Validation completed:" << std::endl;
        std::cout << "  - Total time: " << stats.GetTotalValidationTime().count() << "ms" << std::endl;
        std::cout << "  - Success rate: " << stats.GetSuccessRate() << "%" << std::endl;
        std::cout << "  - Successful validations: " << stats.successfulValidations << std::endl;
        std::cout << "  - Failed validations: " << stats.failedValidations << std::endl;
        std::cout << "  - Total records: " << stats.GetTotalRecordCount() << std::endl;
        
        if (result != DataValidationResult::Success) {
            std::cout << "[ERROR] Validation failed: " << validator.GetLastError() << std::endl;
            return false;
        }
        
        std::cout << "[INFO] All data validation checks passed successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[ERROR] Exception during validation: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[ERROR] Unknown exception during validation" << std::endl;
        return false;
    }
}

/**
 * @brief Manager system validation helper
 * 
 * Validates all manager systems that are critical for server operation.
 * This function checks the state of all major game managers.
 * 
 * @return bool (true if all managers are valid, false otherwise)
 */
bool ValidateManagerSystems_Legacy() {
    try {
        // Check potion manager
        if (!CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(&g_PotionMgr, "Potion Manager")) {
            return false;
        }
        
        // Check quest manager
        if (!CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(g_pQuestMgr, "Quest Manager")) {
            return false;
        }
        
        // Check item combine manager
        if (!CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(g_pItemCombineMgr, "Item Combine Manager")) {
            return false;
        }
        
        // Check PC Bang favor system (optional)
        if (g_pPcBangFavor) {
            CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(g_pPcBangFavor, "PC Bang Favor System");
        }
        
        // Check SU item system (optional)
        if (g_pSUItemSystem) {
            CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(g_pSUItemSystem, "SU Item System");
        }
        
        // Check monster SP group table (optional)
        if (g_pMonsterSPGroupTable) {
            CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(g_pMonsterSPGroupTable, "Monster SP Group Table");
        }
        
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Quick validation for essential data only
 * 
 * Performs a quick validation of only the most essential data required
 * for basic server operation. This is useful for fast startup checks.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_CheckLoadedData_Quick(CMainThread* mainThread) {
    if (!mainThread) {
        return false;
    }
    
    try {
        // Check only the absolutely essential data
        return (mainThread->GetItemDataTable() != nullptr &&
                mainThread->GetSkillDataTable() != nullptr &&
                mainThread->GetClassTable() != nullptr &&
                CRecordData_IsValid(mainThread->GetItemDataTable()) &&
                CRecordData_IsValid(mainThread->GetSkillDataTable()) &&
                CRecordData_IsValid(mainThread->GetClassTable()));
        
    } catch (...) {
        return false;
    }
}
