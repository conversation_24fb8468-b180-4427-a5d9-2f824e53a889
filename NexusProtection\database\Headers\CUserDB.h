#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <vector>
#include <unordered_map>

// Forward declarations
class CMyTimer;
class CRadarItemMgr;
struct _AVATOR_DATA;
struct _world_id;

/**
 * User database operation types
 */
enum class EUserDBOperation : int {
    LOGIN = 0,
    LOGOUT = 1,
    CHARACTER_SELECT = 2,
    CHARACTER_CREATE = 3,
    INVENTORY_UPDATE = 4,
    SKILL_UPDATE = 5,
    STAT_UPDATE = 6,
    PLAYTIME_UPDATE = 7
};

/**
 * User session data
 */
struct UserSessionData {
    uint32_t dwUserID;
    std::string szAccountName;
    std::string szCharacterName;
    uint32_t dwCharacterSerial;
    uint64_t dwLoginTime;
    uint64_t dwLastActivity;
    uint32_t dwPlayTimeMinutes;
    bool bIsActive;
    
    UserSessionData() 
        : dwUserID(0), dwCharacterSerial(0), dwLoginTime(0), 
          dwLastActivity(0), dwPlayTimeMinutes(0), bIsActive(false) {}
};

/**
 * Avatar data structure
 */
struct AvatarData {
    uint32_t dwSerial;
    std::string szName;
    uint32_t dwLevel;
    uint32_t dwClass;
    uint32_t dwHP;
    uint32_t dwMP;
    uint32_t dwExp;
    std::vector<uint32_t> equipment;
    std::vector<uint32_t> inventory;
    
    AvatarData() 
        : dwSerial(0), dwLevel(1), dwClass(0), 
          dwHP(100), dwMP(100), dwExp(0) {}
};

/**
 * Class initialization data
 */
struct ClassInitData {
    uint32_t dwClassID;
    std::string szClassName;
    uint32_t dwStartHP;
    uint32_t dwStartMP;
    uint32_t dwStartStr;
    uint32_t dwStartDex;
    uint32_t dwStartInt;
    uint32_t dwStartVit;
    std::vector<uint32_t> startingItems;
    std::vector<uint32_t> startingSkills;
    
    ClassInitData() 
        : dwClassID(0), dwStartHP(100), dwStartMP(100),
          dwStartStr(10), dwStartDex(10), dwStartInt(10), dwStartVit(10) {}
};

/**
 * CUserDB - User database and session management system
 * Refactored from 0CUserDBQEAAXZ_14010FB90.c
 */
class CUserDB {
public:
    /**
     * Constructor
     */
    CUserDB();
    
    /**
     * Virtual destructor
     */
    virtual ~CUserDB();
    
    /**
     * Initialize user database
     * @param dwIndex World index
     */
    void Init(uint32_t dwIndex);
    
    /**
     * Initialize class data
     * @param pszClassCode Class code string
     * @return true if initialization successful
     */
    bool InitClass(const std::string& pszClassCode);
    
    /**
     * Process user login
     * @param sessionData User session data
     * @return true if login successful
     */
    bool ProcessLogin(const UserSessionData& sessionData);
    
    /**
     * Process user logout
     * @param dwUserID User ID
     * @return true if logout successful
     */
    bool ProcessLogout(uint32_t dwUserID);
    
    /**
     * Update user session
     * @param dwUserID User ID
     * @return true if update successful
     */
    bool UpdateSession(uint32_t dwUserID);
    
    /**
     * Get user session data
     * @param dwUserID User ID
     * @param sessionData Output session data
     * @return true if data retrieved successfully
     */
    bool GetSessionData(uint32_t dwUserID, UserSessionData& sessionData) const;
    
    /**
     * Create new character
     * @param avatarData Character data
     * @return true if creation successful
     */
    bool CreateCharacter(const AvatarData& avatarData);
    
    /**
     * Load character data
     * @param dwSerial Character serial
     * @param avatarData Output character data
     * @return true if load successful
     */
    bool LoadCharacter(uint32_t dwSerial, AvatarData& avatarData);
    
    /**
     * Save character data
     * @param avatarData Character data to save
     * @return true if save successful
     */
    bool SaveCharacter(const AvatarData& avatarData);
    
    /**
     * Delete character
     * @param dwSerial Character serial
     * @return true if deletion successful
     */
    bool DeleteCharacter(uint32_t dwSerial);
    
    /**
     * Update play time
     * @param dwUserID User ID
     * @param dwMinutes Minutes to add
     * @return true if update successful
     */
    bool UpdatePlayTime(uint32_t dwUserID, uint32_t dwMinutes);
    
    /**
     * Get total play time
     * @param dwUserID User ID
     * @return Total play time in minutes
     */
    uint32_t GetTotalPlayTime(uint32_t dwUserID) const;
    
    /**
     * Check if user is online
     * @param dwUserID User ID
     * @return true if user is online
     */
    bool IsUserOnline(uint32_t dwUserID) const;
    
    /**
     * Get online user count
     * @return Number of online users
     */
    uint32_t GetOnlineUserCount() const;
    
    /**
     * Get class initialization data
     * @param dwClassID Class ID
     * @param classData Output class data
     * @return true if data retrieved successfully
     */
    bool GetClassInitData(uint32_t dwClassID, ClassInitData& classData) const;
    
    /**
     * Initialize character with class data
     * @param avatarData Character data to initialize
     * @param dwClassID Class ID
     * @return true if initialization successful
     */
    bool InitializeCharacterWithClass(AvatarData& avatarData, uint32_t dwClassID);
    
    /**
     * Validate character name
     * @param szName Character name to validate
     * @return true if name is valid
     */
    bool ValidateCharacterName(const std::string& szName) const;
    
    /**
     * Check if character name exists
     * @param szName Character name to check
     * @return true if name exists
     */
    bool CharacterNameExists(const std::string& szName) const;
    
    /**
     * Get user database statistics
     * @return Statistics as formatted string
     */
    std::string GetUserDBStats() const;
    
    /**
     * Cleanup inactive sessions
     * @param dwTimeoutMinutes Session timeout in minutes
     * @return Number of sessions cleaned up
     */
    uint32_t CleanupInactiveSessions(uint32_t dwTimeoutMinutes = 30);
    
    /**
     * Static loop processing method
     */
    static void OnLoop_Static();
    
    /**
     * Parameter initialization
     */
    void ParamInit();

protected:
    // World identification
    _world_id* m_pWorldID;
    uint32_t m_dwWorldIndex;
    
    // Session management
    std::unordered_map<uint32_t, UserSessionData> m_userSessions;
    mutable std::mutex m_sessionMutex;
    
    // Character data cache
    std::unordered_map<uint32_t, AvatarData> m_characterCache;
    mutable std::mutex m_characterMutex;
    
    // Class initialization data
    std::unordered_map<uint32_t, ClassInitData> m_classData;
    mutable std::mutex m_classMutex;
    
    // Avatar data (legacy compatibility)
    std::unique_ptr<_AVATOR_DATA> m_pAvatorData;
    std::unique_ptr<_AVATOR_DATA> m_pAvatorData_bk;
    
    // Timers and managers
    std::unique_ptr<CMyTimer> m_pCheckPlayMinTimer;
    std::unique_ptr<CRadarItemMgr> m_pRadarItemMgr;
    
    // Statistics
    std::atomic<uint64_t> m_nLoginCount;
    std::atomic<uint64_t> m_nLogoutCount;
    std::atomic<uint64_t> m_nCharacterOperations;
    std::atomic<uint64_t> m_nSessionUpdates;
    
    // Configuration
    uint32_t m_dwMaxPlayTimePerSession;
    uint32_t m_dwSessionTimeoutMinutes;
    bool m_bEnablePlayTimeTracking;
    
    /**
     * Initialize default class data
     */
    void InitializeDefaultClasses();
    
    /**
     * Load class data from configuration
     * @param pszClassCode Class code string
     * @return true if load successful
     */
    bool LoadClassConfiguration(const std::string& pszClassCode);
    
    /**
     * Update session activity
     * @param dwUserID User ID
     */
    void UpdateSessionActivity(uint32_t dwUserID);
    
    /**
     * Validate session data
     * @param sessionData Session data to validate
     * @return true if data is valid
     */
    bool ValidateSessionData(const UserSessionData& sessionData) const;
    
    /**
     * Validate avatar data
     * @param avatarData Avatar data to validate
     * @return true if data is valid
     */
    bool ValidateAvatarData(const AvatarData& avatarData) const;
    
    /**
     * Generate unique character serial
     * @return Unique character serial
     */
    uint32_t GenerateCharacterSerial();
    
    /**
     * Update operation statistics
     * @param operation Operation type
     * @param success Whether operation was successful
     */
    void UpdateUserStats(EUserDBOperation operation, bool success);
    
    /**
     * Cleanup expired data
     */
    void CleanupExpiredData();
    
    /**
     * Save session to persistent storage
     * @param sessionData Session data to save
     * @return true if save successful
     */
    bool SaveSessionToDB(const UserSessionData& sessionData);
    
    /**
     * Load session from persistent storage
     * @param dwUserID User ID
     * @param sessionData Output session data
     * @return true if load successful
     */
    bool LoadSessionFromDB(uint32_t dwUserID, UserSessionData& sessionData);
};
