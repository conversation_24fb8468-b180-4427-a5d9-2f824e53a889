#pragma once

#include <vector>
#include <string>
#include <mutex>
#include <unordered_set>

/**
 * CNetIndexList - Network index and IP management list
 * Simple implementation for IP filtering and index management
 */
class CNetIndexList {
public:
    /**
     * Constructor
     */
    CNetIndexList();
    
    /**
     * Virtual destructor
     */
    virtual ~CNetIndexList();
    
    /**
     * Add IP address to list
     * @param ipAddress IP address to add
     * @return true if added successfully
     */
    bool AddIP(const std::string& ipAddress);
    
    /**
     * Remove IP address from list
     * @param ipAddress IP address to remove
     * @return true if removed successfully
     */
    bool RemoveIP(const std::string& ipAddress);
    
    /**
     * Check if IP address exists in list
     * @param ipAddress IP address to check
     * @return true if IP exists in list
     */
    bool ContainsIP(const std::string& ipAddress) const;
    
    /**
     * Clear all IP addresses
     */
    void Clear();
    
    /**
     * Get number of IP addresses in list
     * @return Number of IP addresses
     */
    size_t GetCount() const;
    
    /**
     * Get all IP addresses as vector
     * @return Vector of IP addresses
     */
    std::vector<std::string> GetAllIPs() const;

protected:
    std::unordered_set<std::string> m_ipSet;
    mutable std::mutex m_mutex;
};
