/**
 * @file CMainThread_NetworkInit_Utils.cpp
 * @brief Utility functions for CMainThread network initialization
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_NetworkInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>

/**
 * @brief Get network initialization statistics
 * @return const reference to initialization statistics
 */
const NetworkInitStats& CMainThreadNetworkInit::GetInitializationStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_initStats;
}

/**
 * @brief Check if a specific network line is initialized
 * @param lineType The network line type to check
 * @return true if initialized, false otherwise
 */
bool CMainThreadNetworkInit::IsLineInitialized(NetworkLineType lineType) const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    size_t index = static_cast<size_t>(lineType);
    if (index >= m_initStats.lineStatus.size()) {
        return false;
    }
    return m_initStats.lineStatus[index];
}

/**
 * @brief Get the last initialization error message
 * @return string containing the last error message
 */
std::string CMainThreadNetworkInit::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get configuration for a specific network line type
 * @param lineType The network line type
 * @return NetworkLineConfig structure with line configuration
 */
NetworkLineConfig CMainThreadNetworkInit::GetLineConfig(NetworkLineType lineType) {
    auto it = s_lineRegistry.find(lineType);
    if (it != s_lineRegistry.end()) {
        return it->second;
    }
    return NetworkLineConfig("unknown", "Unknown line type", 0, 0);
}

/**
 * @brief Validate all initialized network lines
 * @return true if all required lines are initialized and valid
 */
bool CMainThreadNetworkInit::ValidateInitializedLines() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    // Check all required lines
    for (const auto& [lineType, lineConfig] : s_lineRegistry) {
        size_t index = static_cast<size_t>(lineType);
        if (index >= m_initStats.lineStatus.size() || !m_initStats.lineStatus[index]) {
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Log network line initialization status
 * @param lineType The network line type
 * @param success Whether initialization was successful
 * @param errorMsg Error message if failed
 */
void CMainThreadNetworkInit::LogLineInitialization(NetworkLineType lineType, bool success, const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(lineType);
    if (index >= m_initStats.lineStatus.size()) {
        return;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_initStats.startTime);
    
    m_initStats.lineStatus[index] = success;
    m_initStats.lineInitTime[index] = duration;
    
    if (success) {
        m_initStats.successfulLines++;
        
        const auto& lineConfig = GetLineConfig(lineType);
        std::cout << std::format("[INFO] {} initialized successfully: port {}, max connections {} in {}ms", 
                                NetworkLineTypeToString(lineType), lineConfig.port, 
                                lineConfig.maxConnections, duration.count()) << std::endl;
    } else {
        m_initStats.failedLines++;
        std::string fullError = std::format("{} failed: {}", 
                                          NetworkLineTypeToString(lineType), errorMsg);
        m_initStats.lastError = fullError;
        std::cerr << "[ERROR] " << fullError << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message to set
 */
void CMainThreadNetworkInit::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    std::cerr << "[ERROR] " << error << std::endl;
}

/**
 * @brief Legacy SetNetSystem wrapper
 * @param networking Pointer to CNetWorking instance
 * @param lineCount Number of network lines
 * @param params Array of network parameters
 * @param serverName Server name
 * @param logPath Log file path
 * @return true if successful, false otherwise
 */
bool CMainThreadNetworkInit::SetNetSystem_Legacy(CNetWorking* networking, uint32_t lineCount, 
                                                 _NET_TYPE_PARAM* params, const char* serverName, 
                                                 const char* logPath) {
    // This would call the actual CNetWorking::SetNetSystem function
    return CNetWorking_SetNetSystem(networking, lineCount, params, serverName, logPath);
}

/**
 * @brief Legacy AddPassablePacket wrapper
 * @param mainThread Pointer to CMainThread instance
 */
void CMainThreadNetworkInit::AddPassablePacket_Legacy(CMainThread* mainThread) {
    // This would call the actual CMainThread::AddPassablePacket function
    CMainThread_AddPassablePacket(mainThread);
}

/**
 * @brief Legacy IsReleaseServiceMode wrapper
 * @param mainThread Pointer to CMainThread instance
 * @return true if in release service mode, false otherwise
 */
bool CMainThreadNetworkInit::IsReleaseServiceMode_Legacy(CMainThread* mainThread) {
    // This would call the actual CMainThread::IsReleaseServiceMode function
    return CMainThread_IsReleaseServiceMode(mainThread);
}

/**
 * @brief Convert NetworkLineType enum to string for logging
 * @param lineType The network line type
 * @return String representation of the line type
 */
std::string NetworkLineTypeToString(NetworkLineType lineType) {
    switch (lineType) {
        case NetworkLineType::ClientLine: return "ClientLine";
        case NetworkLineType::AccountLine: return "AccountLine";
        case NetworkLineType::WebLine: return "WebLine";
        case NetworkLineType::BillingLine: return "BillingLine";
        default: return "Unknown";
    }
}

/**
 * @brief Convert NetworkInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string NetworkInitResultToString(NetworkInitResult result) {
    switch (result) {
        case NetworkInitResult::Success: return "Success";
        case NetworkInitResult::Failure: return "Failure";
        case NetworkInitResult::NetworkSystemFailed: return "NetworkSystemFailed";
        case NetworkInitResult::InvalidParameters: return "InvalidParameters";
        case NetworkInitResult::ConfigurationError: return "ConfigurationError";
        case NetworkInitResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief Initialize Client Line
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::InitializeClientLine(CMainThread* mainThread) {
    try {
        LogLineInitialization(NetworkLineType::ClientLine, false);
        
        const auto& config = GetLineConfig(NetworkLineType::ClientLine);
        
        // Client line specific initialization would go here
        // This is handled in the main SetupNetworkSystem function
        
        LogLineInitialization(NetworkLineType::ClientLine, true);
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        LogLineInitialization(NetworkLineType::ClientLine, false, e.what());
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Initialize Account Line
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::InitializeAccountLine(CMainThread* mainThread) {
    try {
        LogLineInitialization(NetworkLineType::AccountLine, false);
        
        const auto& config = GetLineConfig(NetworkLineType::AccountLine);
        
        // Account line specific initialization would go here
        // This is handled in the main SetupNetworkSystem function
        
        LogLineInitialization(NetworkLineType::AccountLine, true);
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        LogLineInitialization(NetworkLineType::AccountLine, false, e.what());
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Initialize Web Line
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::InitializeWebLine(CMainThread* mainThread) {
    try {
        LogLineInitialization(NetworkLineType::WebLine, false);
        
        const auto& config = GetLineConfig(NetworkLineType::WebLine);
        
        // Web line specific initialization would go here
        // This is handled in the main SetupNetworkSystem function
        
        LogLineInitialization(NetworkLineType::WebLine, true);
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        LogLineInitialization(NetworkLineType::WebLine, false, e.what());
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Initialize Billing Line
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::InitializeBillingLine(CMainThread* mainThread) {
    try {
        LogLineInitialization(NetworkLineType::BillingLine, false);
        
        const auto& config = GetLineConfig(NetworkLineType::BillingLine);
        
        // Billing line specific initialization would go here
        // This is handled in the main SetupNetworkSystem function
        
        LogLineInitialization(NetworkLineType::BillingLine, true);
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        LogLineInitialization(NetworkLineType::BillingLine, false, e.what());
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Configure network parameters
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::ConfigureNetworkParameters(CMainThread* mainThread) {
    try {
        std::cout << "[INFO] Configuring network parameters..." << std::endl;
        
        // Network parameter configuration would go here
        // This includes setting up buffer sizes, thread counts, etc.
        
        std::cout << "[INFO] Network parameters configured successfully" << std::endl;
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during network parameter configuration: {}", e.what()));
        return NetworkInitResult::ConfigurationError;
    }
}
