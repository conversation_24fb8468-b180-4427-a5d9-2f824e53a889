/*
 * CPlayerAttack_Unit.cpp - Player Unit Attack Implementation
 * Refactored from AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14016F330.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CPlayerAttack.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CPlayer.h"
#include "../../world/Headers/CMonster.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>
#include <random>

// External dependencies
extern float ffloor(float value);
extern int rand();
extern float GetSqrt(float* pos1, float* pos2);
extern int s_nLimitDist[];
extern int s_nLimitAngle[][8];
extern int s_nLimitRadius[];

/**
 * Perform player unit attack
 * Refactored from original CPlayerAttack::AttackUnit
 */
bool CPlayerAttack::AttackUnit(_attack_param* pParam) {
    try {
        if (!pParam || !m_pAttPlayer) {
            Logger::Error("AttackUnit - Invalid parameters");
            m_bFailure = true;
            return false;
        }
        
        if (!CheckPlayerAttackConditions(pParam)) {
            Logger::Debug("AttackUnit - Attack conditions not met");
            m_bFailure = true;
            return false;
        }
        
        // Reset attack state (equivalent to original memory initialization)
        Reset();
        m_pp = pParam;
        
        // Break stealth (equivalent to original CCharacter::BreakStealth call)
        if (m_pAttChar) {
            m_pAttChar->BreakStealth();
        }
        
        // Check if target exists
        if (!pParam->pDst) {
            Logger::Debug("AttackUnit - No target specified");
            m_bFailure = true;
            return false;
        }
        
        // Get field data (equivalent to original v27 = v57->m_pp->pFld)
        _base_fld* pFld = pParam->pFld;
        if (!pFld) {
            Logger::Error("AttackUnit - No field data");
            m_bFailure = true;
            return false;
        }
        
        // Check field index (equivalent to original v29 != 4 check)
        uint32_t fieldIndex = static_cast<uint32_t>(pFld[4].m_dwIndex);
        if (fieldIndex == 4) {
            Logger::Debug("AttackUnit - Field index 4, attack not processed");
            return true; // Not an error, just no processing needed
        }
        
        // Check dodge and avoidance
        if (!CheckDodgeAndAvoidance(pParam->pDst, pParam)) {
            // Attack missed due to dodge/avoidance
            m_DamList[0].m_pChar = pParam->pDst;
            m_DamList[0].m_nDamage = 0;
            m_nDamagedObjNum = 1;
            
            UpdatePlayerStatistics(PlayerAttackType::Unit, false, 0, false);
            Logger::Debug("AttackUnit - Attack missed due to dodge/avoidance");
            return true; // Attack processed but missed
        }
        
        // Calculate base attack power (equivalent to original CAttack::_CalcGenAttPnt call)
        int nAttPower = pParam->nAddAttPnt + _CalcGenAttPnt(false);
        
        // Apply player effect rate modifiers (equivalent to original _effect_parameter::GetEff_Rate call)
        float effectRate = m_pAttPlayer->GetEffectRate(21); // Effect parameter 21
        nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) * effectRate));
        
        // Apply PvP modifiers
        ApplyPvPModifiers(nAttPower, nAttPower);
        
        // Calculate unit attack modifiers (equivalent to original complex FC calculations)
        float unitModifiers = CalculateUnitAttackModifiers();
        nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) + unitModifiers));
        
        // Process damage based on field index (equivalent to original switch statement)
        bool damageProcessed = ProcessUnitDamage(pParam, nAttPower);
        
        if (damageProcessed) {
            // Calculate average damage (equivalent to original CAttack::CalcAvgDamage call)
            CalcAvgDamage();
            
            UpdatePlayerStatistics(PlayerAttackType::Unit, true, nAttPower, m_bIsCrtAtt);
            Logger::Debug("AttackUnit - Unit attack successful, power: %d", nAttPower);
            return true;
        } else {
            m_bFailure = true;
            UpdatePlayerStatistics(PlayerAttackType::Unit, false, 0, false);
            Logger::Debug("AttackUnit - Unit attack failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::AttackUnit - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Process unit damage calculations
 */
bool CPlayerAttack::ProcessUnitDamage(_attack_param* pParam, int nAttPower) {
    try {
        if (!pParam || !m_pAttPlayer) {
            return false;
        }
        
        // Get field data
        _base_fld* pFld = pParam->pFld;
        if (!pFld) {
            return false;
        }
        
        uint32_t fieldIndex = static_cast<uint32_t>(pFld[4].m_dwIndex);
        
        // Process damage based on field index (equivalent to original switch logic)
        if (fieldIndex <= 3) {
            // Single target attack (equivalent to original v56 <= 3 case)
            if (pParam->pDst) {
                m_DamList[0].m_pChar = pParam->pDst;
                
                // Calculate damage (equivalent to original CCharacter::GetAttackDamPoint call)
                bool bBackAttack = pParam->bBackAttack;
                int damage = m_pAttChar->GetAttackDamPoint(
                    nAttPower,
                    pParam->nPart,
                    pParam->nClass,
                    pParam->pDst,
                    bBackAttack
                );
                
                m_DamList[0].m_nDamage = damage;
                m_nDamagedObjNum = 1;
                
                Logger::Debug("ProcessUnitDamage - Single target damage: %d", damage);
                return true;
            }
        } else if (fieldIndex == 5) {
            // Flash damage (equivalent to original v56 == 5 case)
            return FlashDamageProc(s_nLimitDist[3], nAttPower, s_nLimitAngle[0][3], false, false);
        } else if (fieldIndex == 6) {
            // Area damage (equivalent to original v56 == 6 case)
            return AreaDamageProc(s_nLimitRadius[3], nAttPower, pParam->fArea, false, false);
        } else if (fieldIndex == 7) {
            // Sector damage (equivalent to original v56 == 7 case)
            int nAngle = pParam->nAngle;
            int nDirection = pParam->nDirection;
            return SectorDamageProc(3, nAttPower, s_nLimitAngle[0][3], nDirection, nAngle, false, false);
        }
        
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::ProcessUnitDamage - Exception: %s", e.what());
        return false;
    }
}

/**
 * Calculate unit attack modifiers
 */
float CPlayerAttack::CalculateUnitAttackModifiers() {
    if (!m_pAttPlayer) {
        return 0.0f;
    }
    
    try {
        float totalModifiers = 0.0f;
        
        // Calculate defense FC average (equivalent to original loop for j = 0; j < 5)
        float defFCSum = 0.0f;
        for (int j = 0; j < 5; ++j) {
            defFCSum += m_pAttPlayer->GetDefFC(j, m_pAttPlayer, 0);
        }
        float avgDefFC = defFCSum / 5.0f;
        
        // Apply effect rate 44 (equivalent to original _effect_parameter::GetEff_Rate(&v57->m_pAttPlayer->m_EP, 44))
        float effectRate44 = m_pAttPlayer->GetEffectRate(44);
        float modifier1 = avgDefFC * (effectRate44 - 1.0f);
        totalModifiers += modifier1;
        
        // Calculate attack FC for type 2 (equivalent to original CAttack::GetAttackFC calls)
        float attackFC2 = GetAttackFC(m_pAttPlayer, 2, 1, 1);
        
        // Apply effect rate 43
        float effectRate43 = m_pAttPlayer->GetEffectRate(43);
        float modifier2 = attackFC2 * (effectRate43 - 1.0f);
        totalModifiers += modifier2;
        
        // Calculate attack FC for types 0 and 1
        float attackFC0 = GetAttackFC(m_pAttPlayer, 0, 1, 1);
        float attackFC1 = GetAttackFC(m_pAttPlayer, 1, 1, 1);
        float combinedFC1 = attackFC0 + attackFC1;
        
        // Apply effect rate 41
        float effectRate41 = m_pAttPlayer->GetEffectRate(41);
        float modifier3 = combinedFC1 * (effectRate41 - 1.0f);
        totalModifiers += modifier3;
        
        // Calculate attack FC for types 0 and 1 (different parameters)
        float attackFC0_alt = GetAttackFC(m_pAttPlayer, 0, 0, 1);
        float attackFC1_alt = GetAttackFC(m_pAttPlayer, 1, 0, 1);
        float combinedFC2 = attackFC0_alt + attackFC1_alt;
        
        // Apply effect rate 42
        float effectRate42 = m_pAttPlayer->GetEffectRate(42);
        float modifier4 = combinedFC2 * (effectRate42 - 1.0f);
        totalModifiers += modifier4;
        
        Logger::Debug("CalculateUnitAttackModifiers - Total modifiers: %.2f", totalModifiers);
        return totalModifiers;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::CalculateUnitAttackModifiers - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Flash damage processing
 */
bool CPlayerAttack::FlashDamageProc(int nDistance, int nAttPower, int nAngle, bool bUseEffBullet, bool bIgnoreDefense) {
    try {
        if (!m_pAttPlayer || !m_pp) {
            return false;
        }
        
        // Flash damage affects targets in a line from the attacker
        // This is a simplified implementation - the actual implementation would
        // involve complex geometry calculations and target enumeration
        
        Logger::Debug("FlashDamageProc - Distance: %d, Power: %d, Angle: %d", nDistance, nAttPower, nAngle);
        
        // For now, we'll simulate hitting the primary target
        if (m_pp->pDst) {
            m_DamList[0].m_pChar = m_pp->pDst;
            m_DamList[0].m_nDamage = nAttPower;
            m_nDamagedObjNum = 1;
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::FlashDamageProc - Exception: %s", e.what());
        return false;
    }
}

/**
 * Area damage processing
 */
bool CPlayerAttack::AreaDamageProc(int nRadius, int nAttPower, float* fArea, bool bUseEffBullet, bool bIgnoreDefense) {
    try {
        if (!m_pAttPlayer || !m_pp || !fArea) {
            return false;
        }
        
        // Area damage affects all targets within radius of the specified area
        // This is a simplified implementation - the actual implementation would
        // involve complex area calculations and target enumeration
        
        Logger::Debug("AreaDamageProc - Radius: %d, Power: %d, Area: [%.1f, %.1f, %.1f]", 
                     nRadius, nAttPower, fArea[0], fArea[1], fArea[2]);
        
        // For now, we'll simulate hitting the primary target
        if (m_pp->pDst) {
            m_DamList[0].m_pChar = m_pp->pDst;
            m_DamList[0].m_nDamage = nAttPower;
            m_nDamagedObjNum = 1;
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::AreaDamageProc - Exception: %s", e.what());
        return false;
    }
}

/**
 * Sector damage processing
 */
bool CPlayerAttack::SectorDamageProc(int nType, int nAttPower, int nAngle, int nDirection, int nTargetAngle, 
                                    bool bUseEffBullet, bool bIgnoreDefense) {
    try {
        if (!m_pAttPlayer || !m_pp) {
            return false;
        }
        
        // Sector damage affects targets in a cone/sector from the attacker
        // This is a simplified implementation - the actual implementation would
        // involve complex sector calculations and target enumeration
        
        Logger::Debug("SectorDamageProc - Type: %d, Power: %d, Angle: %d, Direction: %d, TargetAngle: %d", 
                     nType, nAttPower, nAngle, nDirection, nTargetAngle);
        
        // For now, we'll simulate hitting the primary target
        if (m_pp->pDst) {
            m_DamList[0].m_pChar = m_pp->pDst;
            m_DamList[0].m_nDamage = nAttPower;
            m_nDamagedObjNum = 1;
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::SectorDamageProc - Exception: %s", e.what());
        return false;
    }
}

/**
 * Calculate average damage
 */
void CPlayerAttack::CalcAvgDamage() {
    try {
        if (m_nDamagedObjNum <= 0) {
            return;
        }
        
        int totalDamage = 0;
        int validTargets = 0;
        
        for (int i = 0; i < m_nDamagedObjNum; ++i) {
            if (m_DamList[i].m_pChar && m_DamList[i].m_nDamage > 0) {
                totalDamage += m_DamList[i].m_nDamage;
                validTargets++;
            }
        }
        
        if (validTargets > 0) {
            int avgDamage = totalDamage / validTargets;
            Logger::Debug("CalcAvgDamage - Total: %d, Targets: %d, Average: %d", 
                         totalDamage, validTargets, avgDamage);
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::CalcAvgDamage - Exception: %s", e.what());
    }
}

/**
 * Calculate general attack points
 */
int CPlayerAttack::_CalcGenAttPnt(bool bUseEffBullet) {
    if (!m_pAttPlayer) {
        return 0;
    }
    
    try {
        // Base attack points calculation
        int baseAttPnt = m_pAttPlayer->GetBaseAttackPoints();
        
        // Apply general attack modifiers
        float genMod = 1.0f;
        if (bUseEffBullet) {
            genMod += 0.15f; // 15% bonus for effect bullets in general attacks
        }
        
        // Apply player-specific general attack modifiers
        genMod += m_pAttPlayer->GetGeneralAttackModifier();
        
        int finalAttPnt = static_cast<int>(baseAttPnt * genMod);
        
        Logger::Debug("_CalcGenAttPnt - Base: %d, Modifier: %.2f, Final: %d", 
                     baseAttPnt, genMod, finalAttPnt);
        
        return std::max(0, finalAttPnt);
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::_CalcGenAttPnt - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Get attack FC (Force Coefficient)
 */
float CPlayerAttack::GetAttackFC(CPlayer* pPlayer, int nType, int nSubType, int nModifier) {
    if (!pPlayer) {
        return 0.0f;
    }
    
    try {
        // This would interface with the player's equipment and stats system
        // to calculate the Force Coefficient for the specified attack type
        
        float baseFC = pPlayer->GetBaseAttackFC(nType);
        float modifiedFC = baseFC;
        
        // Apply sub-type modifiers
        if (nSubType > 0) {
            modifiedFC *= (1.0f + (nSubType * 0.1f));
        }
        
        // Apply additional modifiers
        if (nModifier > 0) {
            modifiedFC *= (1.0f + (nModifier * 0.05f));
        }
        
        Logger::Debug("GetAttackFC - Type: %d, SubType: %d, Modifier: %d, FC: %.2f", 
                     nType, nSubType, nModifier, modifiedFC);
        
        return modifiedFC;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::GetAttackFC - Exception: %s", e.what());
        return 0.0f;
    }
}
