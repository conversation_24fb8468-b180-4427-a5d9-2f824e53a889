/*
 * CPlayerAttack_Params.cpp - Player Attack Parameter Generation
 * Refactored from make_unit_attack_paramCPlayerQEAAXPEAVCCharacterPE_140089230.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CPlayerAttack.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CPlayer.h"
#include "../../world/Headers/CMonster.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>

// External dependencies
extern float ffloor(float value);

/**
 * Player attack parameter generation namespace implementation
 */
namespace PlayerAttackParams {

/**
 * Generate unit attack parameters for player
 * Refactored from original CPlayer::make_unit_attack_param
 */
bool MakeUnitAttackParam(CPlayer* pPlayer, CCharacter* pTarget, _UnitPart_fld* pWeaponFld, 
                        float fAddBulletFc, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeUnitAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters (equivalent to original memory initialization)
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part (equivalent to original GetAttackRandomPart logic)
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pPlayer->GetAttackRandomPart();
        }
        
        // Set tolerance (no elemental type for unit attacks)
        pAttackParam->nTol = -1;
        
        // Set weapon class from weapon field
        if (pWeaponFld) {
            pAttackParam->nClass = pWeaponFld->m_nWPType;
            
            // Calculate minimum attack force (equivalent to original calculation)
            float minAF = static_cast<float>(pWeaponFld->m_nGAMinAF) * fAddBulletFc;
            pAttackParam->nMinAF = static_cast<int>(ffloor(minAF * pPlayer->GetUnitPvAttFc()));
            
            // Calculate maximum attack force with mastery bonus
            float maxAF = static_cast<float>(pWeaponFld->m_nGAMaxAF) * fAddBulletFc;
            int masteryBonus = pPlayer->GetMasteryPerMast(6, 0); // Mastery type 6, level 0
            pAttackParam->nMaxAF = static_cast<int>(ffloor((maxAF + static_cast<float>(masteryBonus)) * pPlayer->GetUnitPvAttFc()));
            
            // Set selection probabilities
            pAttackParam->nMinSel = pWeaponFld->m_nGAMinSelProb;
            pAttackParam->nMaxSel = pWeaponFld->m_nGAMaxSelProb;
            
            // Set attack range
            pAttackParam->nExtentRange = static_cast<int>(ffloor(pWeaponFld->m_fAttackRange));
            
            // Set field data pointer
            pAttackParam->pFld = reinterpret_cast<_base_fld*>(pWeaponFld);
        } else {
            // Default values when no weapon field
            pAttackParam->nClass = 0;
            pAttackParam->nMinAF = 0;
            pAttackParam->nMaxAF = 100;
            pAttackParam->nMinSel = 0;
            pAttackParam->nMaxSel = 100;
            pAttackParam->nExtentRange = 20;
            pAttackParam->pFld = nullptr;
        }
        
        // Set maximum attack points
        pAttackParam->nMaxAttackPnt = pPlayer->GetMaxAttackPnt();
        
        // Check for back attack (equivalent to original back attack logic)
        if (pTarget) {
            CMonster* pMonster = dynamic_cast<CMonster*>(pTarget);
            if (pMonster && pMonster->GetObjectKind() == 1) {
                // Check if monster can see the player (equivalent to original CMonster::IsViewArea call)
                if (!pMonster->IsViewArea(pPlayer)) {
                    pAttackParam->bBackAttack = true;
                }
            }
        }
        
        Logger::Debug("MakeUnitAttackParam - Parameters generated successfully for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeUnitAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate general attack parameters for player
 */
bool MakeGeneralAttackParam(CPlayer* pPlayer, CCharacter* pTarget, int nPart, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeGeneralAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target and part
        pAttackParam->pDst = pTarget;
        pAttackParam->nPart = nPart;
        
        // Set basic parameters for general attack
        pAttackParam->nTol = -1; // No elemental type
        pAttackParam->nClass = pPlayer->GetWeaponClass();
        
        // Calculate attack force based on player stats
        int baseMinAF = pPlayer->GetBaseMinAttackForce();
        int baseMaxAF = pPlayer->GetBaseMaxAttackForce();
        
        pAttackParam->nMinAF = baseMinAF;
        pAttackParam->nMaxAF = baseMaxAF;
        
        // Set selection probabilities
        pAttackParam->nMinSel = 0;
        pAttackParam->nMaxSel = 100;
        
        // Set attack range
        pAttackParam->nExtentRange = static_cast<int>(pPlayer->GetAttackRange());
        
        // Set field data (weapon data)
        pAttackParam->pFld = pPlayer->GetCurrentWeaponFld();
        
        // Set maximum attack points
        pAttackParam->nMaxAttackPnt = pPlayer->GetMaxAttackPnt();
        
        Logger::Debug("MakeGeneralAttackParam - General attack parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeGeneralAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate skill attack parameters for player
 */
bool MakeSkillAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pSkillFld, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam || !pSkillFld) {
            Logger::Error("MakeSkillAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pPlayer->GetAttackRandomPart();
        }
        
        // Set skill-specific parameters
        // This would depend on the skill field structure
        // For now, we'll use generic skill parameters
        pAttackParam->nTol = pPlayer->GetSkillElement(pSkillFld);
        pAttackParam->nClass = pPlayer->GetWeaponClass();
        
        // Calculate skill-based attack force
        int skillMinAF = pPlayer->GetSkillMinAttackForce(pSkillFld);
        int skillMaxAF = pPlayer->GetSkillMaxAttackForce(pSkillFld);
        
        pAttackParam->nMinAF = skillMinAF;
        pAttackParam->nMaxAF = skillMaxAF;
        
        // Set skill selection probabilities
        pAttackParam->nMinSel = pPlayer->GetSkillMinSelProb(pSkillFld);
        pAttackParam->nMaxSel = pPlayer->GetSkillMaxSelProb(pSkillFld);
        
        // Set skill range
        pAttackParam->nExtentRange = pPlayer->GetSkillRange(pSkillFld);
        
        // Set field data
        pAttackParam->pFld = static_cast<_base_fld*>(pSkillFld);
        
        // Set skill-specific parameters
        pAttackParam->nLevel = pPlayer->GetSkillLevel(pSkillFld);
        pAttackParam->nMastery = pPlayer->GetSkillMastery(pSkillFld);
        pAttackParam->byEffectCode = pPlayer->GetSkillEffectCode(pSkillFld);
        
        Logger::Debug("MakeSkillAttackParam - Skill attack parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeSkillAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate force attack parameters for player
 */
bool MakeForceAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pForceFld, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeForceAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pPlayer->GetAttackRandomPart();
        }
        
        // Force attack parameters
        pAttackParam->nTol = -1; // No elemental type for force attacks
        pAttackParam->nClass = pPlayer->GetWeaponClass();
        
        // Calculate force attack damage
        int forceDamage = pPlayer->GetForceAttackDamage(pForceFld);
        pAttackParam->nMinAF = forceDamage;
        pAttackParam->nMaxAF = forceDamage;
        
        // Force attacks always hit
        pAttackParam->nMinSel = 100;
        pAttackParam->nMaxSel = 100;
        
        // Set force attack range
        pAttackParam->nExtentRange = pPlayer->GetForceAttackRange(pForceFld);
        
        // Set field data
        pAttackParam->pFld = static_cast<_base_fld*>(pForceFld);
        
        // Force attack specific settings
        pAttackParam->bPassCount = true; // Force attacks bypass defenses
        pAttackParam->nAttactType = 6; // Special attack type
        pAttackParam->byEffectCode = 1; // Force effect
        pAttackParam->nLevel = 7; // Maximum level
        pAttackParam->nMastery = 99; // Maximum mastery
        
        Logger::Debug("MakeForceAttackParam - Force attack parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeForceAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate siege attack parameters for player
 */
bool MakeSiegeAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pSiegeFld, 
                         int nPart, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeSiegeAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target and part
        pAttackParam->pDst = pTarget;
        pAttackParam->nPart = nPart;
        
        // Siege attack parameters
        pAttackParam->nTol = -1; // No elemental type
        pAttackParam->nClass = 7; // Siege weapon class
        
        // Calculate siege damage
        int siegeDamage = pPlayer->GetSiegeAttackDamage(pSiegeFld);
        pAttackParam->nMinAF = siegeDamage;
        pAttackParam->nMaxAF = siegeDamage;
        
        // Siege attacks have high hit rate
        pAttackParam->nMinSel = 80;
        pAttackParam->nMaxSel = 100;
        
        // Set siege range
        pAttackParam->nExtentRange = pPlayer->GetSiegeAttackRange(pSiegeFld);
        
        // Set field data
        pAttackParam->pFld = static_cast<_base_fld*>(pSiegeFld);
        
        // Siege attack specific settings
        pAttackParam->nAttactType = 7; // Siege attack type
        pAttackParam->byEffectCode = 3; // Siege effect
        pAttackParam->nLevel = 5; // High level
        pAttackParam->nMastery = 80; // High mastery
        
        Logger::Debug("MakeSiegeAttackParam - Siege attack parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeSiegeAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate weapon active force attack parameters
 */
bool MakeWPActiveForceAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pWeaponFld, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeWPActiveForceAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pPlayer->GetAttackRandomPart();
        }
        
        // Weapon active force parameters
        pAttackParam->nTol = pPlayer->GetWeaponElement(pWeaponFld);
        pAttackParam->nClass = pPlayer->GetWeaponClass();
        
        // Calculate weapon active force damage
        int weaponForceDamage = pPlayer->GetWeaponActiveForceAttackDamage(pWeaponFld);
        pAttackParam->nMinAF = weaponForceDamage;
        pAttackParam->nMaxAF = weaponForceDamage;
        
        // Weapon active force attacks have high hit rate
        pAttackParam->nMinSel = 90;
        pAttackParam->nMaxSel = 100;
        
        // Set weapon range
        pAttackParam->nExtentRange = pPlayer->GetWeaponActiveForceRange(pWeaponFld);
        
        // Set field data
        pAttackParam->pFld = static_cast<_base_fld*>(pWeaponFld);
        
        // Weapon active force specific settings
        pAttackParam->nAttactType = 8; // Weapon active force type
        pAttackParam->byEffectCode = 4; // Weapon force effect
        pAttackParam->nLevel = pPlayer->GetWeaponLevel(pWeaponFld);
        pAttackParam->nMastery = pPlayer->GetWeaponMastery(pWeaponFld);
        
        Logger::Debug("MakeWPActiveForceAttackParam - Weapon active force parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeWPActiveForceAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate weapon active skill attack parameters
 */
bool MakeWPActiveSkillAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pWeaponFld, _attack_param* pAttackParam) {
    try {
        if (!pPlayer || !pAttackParam) {
            Logger::Error("MakeWPActiveSkillAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pPlayer->GetAttackRandomPart();
        }
        
        // Weapon active skill parameters
        pAttackParam->nTol = pPlayer->GetWeaponElement(pWeaponFld);
        pAttackParam->nClass = pPlayer->GetWeaponClass();
        
        // Calculate weapon active skill damage
        int weaponSkillMinAF = pPlayer->GetWeaponActiveSkillMinAttackForce(pWeaponFld);
        int weaponSkillMaxAF = pPlayer->GetWeaponActiveSkillMaxAttackForce(pWeaponFld);
        
        pAttackParam->nMinAF = weaponSkillMinAF;
        pAttackParam->nMaxAF = weaponSkillMaxAF;
        
        // Set weapon skill selection probabilities
        pAttackParam->nMinSel = pPlayer->GetWeaponActiveSkillMinSelProb(pWeaponFld);
        pAttackParam->nMaxSel = pPlayer->GetWeaponActiveSkillMaxSelProb(pWeaponFld);
        
        // Set weapon skill range
        pAttackParam->nExtentRange = pPlayer->GetWeaponActiveSkillRange(pWeaponFld);
        
        // Set field data
        pAttackParam->pFld = static_cast<_base_fld*>(pWeaponFld);
        
        // Weapon active skill specific settings
        pAttackParam->nAttactType = 9; // Weapon active skill type
        pAttackParam->byEffectCode = pPlayer->GetWeaponActiveSkillEffectCode(pWeaponFld);
        pAttackParam->nLevel = pPlayer->GetWeaponLevel(pWeaponFld);
        pAttackParam->nMastery = pPlayer->GetWeaponMastery(pWeaponFld);
        
        Logger::Debug("MakeWPActiveSkillAttackParam - Weapon active skill parameters generated for player %p", pPlayer);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeWPActiveSkillAttackParam - Exception: %s", e.what());
        return false;
    }
}

} // namespace PlayerAttackParams
