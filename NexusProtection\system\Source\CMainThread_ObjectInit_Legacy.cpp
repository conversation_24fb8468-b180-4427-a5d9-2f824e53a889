/**
 * @file CMainThread_ObjectInit_Legacy.cpp
 * @brief Legacy compatibility wrapper for CMainThread::ObjectInit
 * 
 * This file provides the exact original function signature for backward compatibility
 * with existing code that calls the original decompiled function.
 * 
 * Original function: ObjectInitCMainThreadAEAA_NXZ_1401EB650.c
 * Address: 0x1401EB650
 * Signature: bool __fastcall CMainThread::ObjectInit(CMainThread *this)
 */

#include "../Headers/CMainThread_ObjectInit.h"
#include "../Headers/CMainThread.h"
#include <cstdint>
#include <iostream>

// External function declarations (to be properly linked)
extern uint64_t _security_cookie;
extern void MyMessageBox(const char* title, const char* format, ...);

/**
 * @brief Legacy ObjectInit function with exact original signature
 * This maintains 100% compatibility with the original decompiled function
 * 
 * @param this_ptr Pointer to CMainThread instance (original 'this' parameter)
 * @return bool (true for success, false for failure)
 */
extern "C" bool __fastcall CMainThread_ObjectInit_Original(CMainThread* this_ptr) {
    // Security cookie setup (equivalent to original stack protection)
    uint64_t stackBuffer[184]; // Equivalent to original v13 buffer (184 * 4 bytes)
    uint64_t securityCookie = reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie;
    
    // Initialize stack buffer (equivalent to original initialization loop)
    for (size_t i = 0; i < 184; ++i) {
        reinterpret_cast<uint32_t*>(stackBuffer)[i] = 0xCCCCCCCC; // -858993460 in original
    }
    
    // Call the modern implementation
    bool result = false;
    if (this_ptr) {
        result = CMainThreadObjectInit::ObjectInit_Legacy(this_ptr);
    }
    
    // Verify security cookie (equivalent to original stack protection check)
    if ((reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie) != securityCookie) {
        // Stack corruption detected - this would trigger security handler in original
        MyMessageBox("Security Error", "Stack corruption detected in CMainThread::ObjectInit");
        return false;
    }
    
    return result;
}

/**
 * @brief Alternative legacy wrapper that can be used as a drop-in replacement
 * This provides the original calling convention and behavior
 */
bool CMainThread::ObjectInit_OriginalBehavior() {
    try {
        // Call modern object initialization
        CMainThreadObjectInit objectInit;
        ObjectInitResult result = objectInit.InitializeGameObjects(this);
        
        // Convert result to original format
        return (result == ObjectInitResult::Success);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ObjectInit_OriginalBehavior: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Stub implementations for remaining object initialization functions
 * These would be implemented based on the actual system requirements
 */

ObjectInitResult CMainThreadObjectInit::InitializeGuardTowers() {
    try {
        LogObjectInitialization(GameObjectType::GuardTower, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::GuardTower);
        
        // Allocate memory for guard towers
        g_Tower = AllocateObjectArray<CGuardTower>(objectInfo.maxCount, GameObjectType::GuardTower);
        if (!g_Tower) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all GuardTower objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id towerID;
            CreateObjectID(&towerID, objectInfo.objectType, index);
            
            if (!CGuardTower_Init(&g_Tower[index], &towerID)) {
                LogObjectInitialization(GameObjectType::GuardTower, false, 0, 0, 
                                       std::format("Failed to initialize guard tower {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::GuardTower, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::GuardTower, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeHolyStones() {
    try {
        LogObjectInitialization(GameObjectType::HolyStone, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::HolyStone);
        
        // Allocate memory for holy stones
        g_Stone = AllocateObjectArray<CHolyStone>(objectInfo.maxCount, GameObjectType::HolyStone);
        if (!g_Stone) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all HolyStone objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id stoneID;
            CreateObjectID(&stoneID, objectInfo.objectType, index);
            
            if (!CHolyStone_Init(&g_Stone[index], &stoneID)) {
                LogObjectInitialization(GameObjectType::HolyStone, false, 0, 0, 
                                       std::format("Failed to initialize holy stone {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::HolyStone, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::HolyStone, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeHolyKeepers() {
    try {
        LogObjectInitialization(GameObjectType::HolyKeeper, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::HolyKeeper);
        
        // Allocate memory for holy keepers
        g_Keeper = AllocateObjectArray<CHolyKeeper>(objectInfo.maxCount, GameObjectType::HolyKeeper);
        if (!g_Keeper) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all HolyKeeper objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id keeperID;
            CreateObjectID(&keeperID, objectInfo.objectType, index);
            
            if (!CHolyKeeper_Init(&g_Keeper[index], &keeperID)) {
                LogObjectInitialization(GameObjectType::HolyKeeper, false, 0, 0, 
                                       std::format("Failed to initialize holy keeper {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::HolyKeeper, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::HolyKeeper, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeTraps() {
    try {
        LogObjectInitialization(GameObjectType::Trap, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::Trap);
        
        // Allocate memory for traps
        g_Trap = AllocateObjectArray<CTrap>(objectInfo.maxCount, GameObjectType::Trap);
        if (!g_Trap) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all Trap objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id trapID;
            CreateObjectID(&trapID, objectInfo.objectType, index);
            
            if (!CTrap_Init(&g_Trap[index], &trapID)) {
                LogObjectInitialization(GameObjectType::Trap, false, 0, 0, 
                                       std::format("Failed to initialize trap {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::Trap, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Trap, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeItemBoxes() {
    try {
        LogObjectInitialization(GameObjectType::ItemBox, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::ItemBox);
        
        // Allocate memory for item boxes
        g_ItemBox = AllocateObjectArray<CItemBox>(objectInfo.maxCount, GameObjectType::ItemBox);
        if (!g_ItemBox) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all ItemBox objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id itemBoxID;
            CreateObjectID(&itemBoxID, objectInfo.objectType, index);
            
            if (!CItemBox_Init(&g_ItemBox[index], &itemBoxID)) {
                LogObjectInitialization(GameObjectType::ItemBox, false, 0, 0, 
                                       std::format("Failed to initialize item box {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::ItemBox, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::ItemBox, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeParkingUnits() {
    try {
        LogObjectInitialization(GameObjectType::ParkingUnit, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::ParkingUnit);
        
        // Allocate memory for parking units
        g_ParkingUnit = AllocateObjectArray<CParkingUnit>(objectInfo.maxCount, GameObjectType::ParkingUnit);
        if (!g_ParkingUnit) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all ParkingUnit objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id parkingUnitID;
            CreateObjectID(&parkingUnitID, objectInfo.objectType, index);
            
            if (!CParkingUnit_Init(&g_ParkingUnit[index], &parkingUnitID)) {
                LogObjectInitialization(GameObjectType::ParkingUnit, false, 0, 0, 
                                       std::format("Failed to initialize parking unit {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::ParkingUnit, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::ParkingUnit, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeDarkHoles() {
    try {
        LogObjectInitialization(GameObjectType::DarkHole, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::DarkHole);
        
        // Allocate memory for dark holes
        g_DarkHole = AllocateObjectArray<CDarkHole>(objectInfo.maxCount, GameObjectType::DarkHole);
        if (!g_DarkHole) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all DarkHole objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id darkHoleID;
            CreateObjectID(&darkHoleID, objectInfo.objectType, index);
            
            if (!CDarkHole_Init(&g_DarkHole[index], &darkHoleID)) {
                LogObjectInitialization(GameObjectType::DarkHole, false, 0, 0, 
                                       std::format("Failed to initialize dark hole {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::DarkHole, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::DarkHole, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

ObjectInitResult CMainThreadObjectInit::InitializeGuilds() {
    try {
        LogObjectInitialization(GameObjectType::Guild, false);
        
        const auto& objectInfo = GetObjectInfo(GameObjectType::Guild);
        
        // Allocate memory for guilds
        g_Guild = AllocateObjectArray<CGuild>(objectInfo.maxCount, GameObjectType::Guild);
        if (!g_Guild) {
            return ObjectInitResult::MemoryAllocationFailed;
        }
        
        // Initialize all Guild objects
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id guildID;
            CreateObjectID(&guildID, objectInfo.objectType, index);
            
            if (!CGuild_Init(&g_Guild[index], &guildID)) {
                LogObjectInitialization(GameObjectType::Guild, false, 0, 0, 
                                       std::format("Failed to initialize guild {}", index));
                return ObjectInitResult::InitializationError;
            }
        }
        
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount);
        LogObjectInitialization(GameObjectType::Guild, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Guild, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}
