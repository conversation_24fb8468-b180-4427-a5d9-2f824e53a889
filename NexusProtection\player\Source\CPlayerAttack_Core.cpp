/*
 * CPlayerAttack_Core.cpp - Player Attack System Core Implementation
 * Refactored from 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CPlayerAttack.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CPlayer.h"
#include "../../world/Headers/CMonster.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>
#include <random>

// External dependencies
extern float ffloor(float value);
extern int rand();
extern float GetSqrt(float* pos1, float* pos2);
extern int s_nLimitDist[];
extern int s_nLimitAngle[][8];
extern int s_nLimitRadius[];

/**
 * _UnitPart_fld constructor
 */
_UnitPart_fld::_UnitPart_fld() 
    : m_nWPType(0)
    , m_nGAMinAF(0)
    , m_nGAMaxAF(0)
    , m_nGAMinSelProb(0)
    , m_nGAMaxSelProb(100)
    , m_fAttackRange(0.0f)
    , m_dwIndex(0) {
}

/**
 * Reset unit part field data
 */
void _UnitPart_fld::Reset() {
    m_nWPType = 0;
    m_nGAMinAF = 0;
    m_nGAMaxAF = 0;
    m_nGAMinSelProb = 0;
    m_nGAMaxSelProb = 100;
    m_fAttackRange = 0.0f;
    m_dwIndex = 0;
}

/**
 * Validate unit part field data
 */
bool _UnitPart_fld::IsValid() const {
    return (m_nGAMinAF >= 0 && m_nGAMaxAF >= m_nGAMinAF && 
            m_nGAMinSelProb >= 0 && m_nGAMaxSelProb >= m_nGAMinSelProb &&
            m_fAttackRange >= 0.0f);
}

/**
 * PlayerAttackStatistics constructor
 */
PlayerAttackStatistics::PlayerAttackStatistics() 
    : totalAttacks(0)
    , successfulAttacks(0)
    , failedAttacks(0)
    , criticalHits(0)
    , skillAttacks(0)
    , generalAttacks(0)
    , unitAttacks(0)
    , siegeAttacks(0)
    , totalDamageDealt(0)
    , pvpAttacks(0)
    , pveAttacks(0) {
}

/**
 * Reset player attack statistics
 */
void PlayerAttackStatistics::Reset() {
    totalAttacks = 0;
    successfulAttacks = 0;
    failedAttacks = 0;
    criticalHits = 0;
    skillAttacks = 0;
    generalAttacks = 0;
    unitAttacks = 0;
    siegeAttacks = 0;
    totalDamageDealt = 0;
    pvpAttacks = 0;
    pveAttacks = 0;
}

/**
 * Get success rate
 */
float PlayerAttackStatistics::GetSuccessRate() const {
    if (totalAttacks == 0) return 0.0f;
    return static_cast<float>(successfulAttacks) / static_cast<float>(totalAttacks);
}

/**
 * Get critical rate
 */
float PlayerAttackStatistics::GetCriticalRate() const {
    if (successfulAttacks == 0) return 0.0f;
    return static_cast<float>(criticalHits) / static_cast<float>(successfulAttacks);
}

/**
 * Get PvP ratio
 */
float PlayerAttackStatistics::GetPvPRatio() const {
    if (totalAttacks == 0) return 0.0f;
    return static_cast<float>(pvpAttacks) / static_cast<float>(totalAttacks);
}

/**
 * CPlayerAttack constructor
 * Refactored from original CPlayerAttack::CPlayerAttack
 */
CPlayerAttack::CPlayerAttack(CCharacter* pPlayer) 
    : CAttack(pPlayer)
    , m_pAttPlayer(static_cast<CPlayer*>(pPlayer)) {
    
    try {
        m_playerStats.Reset();
        Logger::Debug("CPlayerAttack::CPlayerAttack - Player attack system initialized for player %p", pPlayer);
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::CPlayerAttack - Exception during initialization: %s", e.what());
        throw;
    }
}

/**
 * CPlayerAttack destructor
 */
CPlayerAttack::~CPlayerAttack() {
    try {
        m_pAttPlayer = nullptr;
        Logger::Debug("CPlayerAttack::~CPlayerAttack - Player attack system destroyed");
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::~CPlayerAttack - Exception during destruction: %s", e.what());
    }
}

/**
 * Calculate player-specific damage
 */
int CPlayerAttack::CalculateDamage(CCharacter* pTarget, _attack_param* pParam) {
    if (!pTarget || !pParam || !m_pAttPlayer) {
        return 0;
    }
    
    // Start with base damage calculation
    int baseDamage = CAttack::CalculateDamage(pTarget, pParam);
    
    // Apply player-specific modifiers
    if (m_pAttPlayer) {
        // Apply player level modifier
        int playerLevel = m_pAttPlayer->GetLevel();
        float levelMod = 1.0f + (playerLevel * 0.015f); // 1.5% per level
        baseDamage = static_cast<int>(baseDamage * levelMod);
        
        // Apply weapon class modifiers
        int weaponClass = PlayerAttackUtils::GetPlayerWeaponClass(m_pAttPlayer);
        if (weaponClass > 0) {
            float weaponMod = 1.0f + (weaponClass * 0.05f); // 5% per weapon class
            baseDamage = static_cast<int>(baseDamage * weaponMod);
        }
        
        // Apply mastery modifiers
        // This would depend on player's mastery system
        // For now, we'll apply a basic modifier
        float masteryMod = 1.0f + (m_pAttPlayer->GetMasteryLevel() * 0.02f); // 2% per mastery level
        baseDamage = static_cast<int>(baseDamage * masteryMod);
    }
    
    return std::max(0, baseDamage);
}

/**
 * Calculate skill attack points
 */
int CPlayerAttack::_CalcSkillAttPnt(bool bUseEffBullet) {
    if (!m_pAttPlayer) {
        return 0;
    }
    
    try {
        // Base attack points calculation
        int baseAttPnt = m_pAttPlayer->GetBaseAttackPoints();
        
        // Apply skill modifiers
        float skillMod = 1.0f;
        if (bUseEffBullet) {
            skillMod += 0.2f; // 20% bonus for effect bullets
        }
        
        // Apply player-specific skill modifiers
        skillMod += m_pAttPlayer->GetSkillAttackModifier();
        
        // Apply equipment modifiers
        skillMod += m_pAttPlayer->GetEquipmentAttackModifier();
        
        int finalAttPnt = static_cast<int>(baseAttPnt * skillMod);
        
        Logger::Debug("_CalcSkillAttPnt - Base: %d, Modifier: %.2f, Final: %d", 
                     baseAttPnt, skillMod, finalAttPnt);
        
        return std::max(0, finalAttPnt);
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::_CalcSkillAttPnt - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Apply player-specific effects
 */
bool CPlayerAttack::ApplyPlayerEffects(CCharacter* pTarget, _attack_param* pParam) {
    try {
        if (!pTarget || !pParam || !m_pAttPlayer) {
            return false;
        }
        
        // Apply status effects based on player skills and equipment
        if (pParam->byEffectCode > 0) {
            Logger::Debug("ApplyPlayerEffects - Applying effect code %d to target %p", 
                         pParam->byEffectCode, pTarget);
            
            // This would involve calling target's status effect system
            // For now, we'll just log the effect application
        }
        
        // Apply elemental effects based on weapon type
        if (pParam->nTol >= 0) {
            Logger::Debug("ApplyPlayerEffects - Applying elemental effect %d to target %p", 
                         pParam->nTol, pTarget);
        }
        
        // Apply special player effects (buffs, debuffs, etc.)
        if (m_pAttPlayer->HasSpecialEffects()) {
            m_pAttPlayer->ApplySpecialEffects(pTarget, pParam);
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::ApplyPlayerEffects - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check player attack conditions
 */
bool CPlayerAttack::CheckPlayerAttackConditions(_attack_param* pParam) const {
    if (!pParam || !m_pAttPlayer) {
        return false;
    }
    
    // Check if player is alive
    if (!m_pAttPlayer->IsAlive()) {
        Logger::Debug("CheckPlayerAttackConditions - Player is not alive");
        return false;
    }
    
    // Check if target is valid
    if (!IsValidTarget(pParam->pDst)) {
        Logger::Debug("CheckPlayerAttackConditions - Invalid target");
        return false;
    }
    
    // Check if player can attack (not stunned, silenced, etc.)
    if (m_pAttPlayer->IsStunned() || m_pAttPlayer->IsSilenced()) {
        Logger::Debug("CheckPlayerAttackConditions - Player is stunned or silenced");
        return false;
    }
    
    // Check attack range
    PlayerAttackType attackType = PlayerAttackType::General; // Default
    float attackRange = PlayerAttackUtils::CalculatePlayerAttackRange(m_pAttPlayer, attackType);
    if (!MonsterAttackUtils::IsTargetInRange(m_pAttPlayer, pParam->pDst, attackRange)) {
        Logger::Debug("CheckPlayerAttackConditions - Target out of range");
        return false;
    }
    
    // Check if player has sufficient resources (MP, stamina, etc.)
    if (!m_pAttPlayer->HasSufficientResources(attackType)) {
        Logger::Debug("CheckPlayerAttackConditions - Insufficient resources");
        return false;
    }
    
    return true;
}

/**
 * Check dodge and avoidance
 */
bool CPlayerAttack::CheckDodgeAndAvoidance(CCharacter* pTarget, _attack_param* pParam) {
    if (!pTarget || !pParam || !m_pAttPlayer) {
        return false; // Attack misses
    }
    
    try {
        // Check if target has dodge state
        if (pTarget->HasDodgeState()) {
            Logger::Debug("CheckDodgeAndAvoidance - Target has dodge state");
            return false;
        }
        
        // Calculate hit probability
        float hitChance = 100.0f; // Base 100% hit chance
        
        // Apply player accuracy modifiers
        hitChance += m_pAttPlayer->GetAccuracyModifier();
        
        // Apply target avoidance
        int targetAvoidRate = pTarget->GetAvoidRate();
        hitChance -= static_cast<float>(targetAvoidRate);
        
        // Apply additional modifiers based on player effects
        hitChance += m_pAttPlayer->GetHitRateBonus();
        
        // Clamp hit chance
        hitChance = std::max(0.0f, std::min(100.0f, hitChance));
        
        // Roll for hit
        int roll = rand() % 100;
        bool hits = (roll < static_cast<int>(hitChance));
        
        Logger::Debug("CheckDodgeAndAvoidance - Hit chance: %.1f%%, Roll: %d, Hits: %s", 
                     hitChance, roll, hits ? "Yes" : "No");
        
        return hits;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::CheckDodgeAndAvoidance - Exception: %s", e.what());
        return false;
    }
}

/**
 * Apply PvP modifiers
 */
void CPlayerAttack::ApplyPvPModifiers(int& nAttPower, int& nAttPnt) {
    if (!m_pAttPlayer) {
        return;
    }
    
    try {
        // Check if player is in guild battle
        if (m_pAttPlayer->IsInGuildBattle()) {
            Logger::Debug("ApplyPvPModifiers - Player in guild battle, no PvP modifiers applied");
            return;
        }
        
        // Get player information for PvP ranking system
        uint32_t charSerial = m_pAttPlayer->GetCharacterSerial();
        int raceCode = m_pAttPlayer->GetRaceCode();
        
        // Apply boss type modifiers (this would interface with ranking system)
        int bossType = m_pAttPlayer->GetBossType();
        
        if (bossType == 0) {
            // Regular player gets 30% bonus
            nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) * 1.3f));
            nAttPnt = static_cast<int>(ffloor(static_cast<float>(nAttPnt) * 1.3f));
            Logger::Debug("ApplyPvPModifiers - Regular player bonus applied: 30%");
        } else if (bossType == 2 || bossType == 6) {
            // Boss types 2 and 6 get 20% bonus
            nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) * 1.2f));
            nAttPnt = static_cast<int>(ffloor(static_cast<float>(nAttPnt) * 1.2f));
            Logger::Debug("ApplyPvPModifiers - Boss type %d bonus applied: 20%", bossType);
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::ApplyPvPModifiers - Exception: %s", e.what());
    }
}

/**
 * Apply boss type modifiers
 */
void CPlayerAttack::ApplyBossTypeModifiers(int& nAttPower, int& nAttPnt) {
    if (!m_pAttPlayer) {
        return;
    }
    
    try {
        // Check for special boss conditions
        if (m_pAttPlayer->IsHolyStoneDestroyer()) {
            // Holy stone destroyer gets 30% bonus
            nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) * 1.3f));
            nAttPnt = static_cast<int>(ffloor(static_cast<float>(nAttPnt) * 1.3f));
            Logger::Debug("ApplyBossTypeModifiers - Holy stone destroyer bonus applied: 30%");
        } else if (m_pAttPlayer->HasLastAttackBuff()) {
            // Last attack buff gets 30% bonus
            nAttPower = static_cast<int>(ffloor(static_cast<float>(nAttPower) * 1.3f));
            nAttPnt = static_cast<int>(ffloor(static_cast<float>(nAttPnt) * 1.3f));
            Logger::Debug("ApplyBossTypeModifiers - Last attack buff bonus applied: 30%");
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::ApplyBossTypeModifiers - Exception: %s", e.what());
    }
}

/**
 * Update player attack statistics
 */
void CPlayerAttack::UpdatePlayerStatistics(PlayerAttackType attackType, bool success, int damage, bool critical) {
    try {
        m_playerStats.totalAttacks++;
        
        if (success) {
            m_playerStats.successfulAttacks++;
            m_playerStats.totalDamageDealt += damage;
            
            if (critical) {
                m_playerStats.criticalHits++;
            }
        } else {
            m_playerStats.failedAttacks++;
        }
        
        // Update attack type statistics
        switch (attackType) {
            case PlayerAttackType::General:
                m_playerStats.generalAttacks++;
                break;
            case PlayerAttackType::Skill:
                m_playerStats.skillAttacks++;
                break;
            case PlayerAttackType::Unit:
                m_playerStats.unitAttacks++;
                break;
            case PlayerAttackType::Siege:
                m_playerStats.siegeAttacks++;
                break;
            default:
                break;
        }
        
        // Update PvP/PvE statistics
        // This would depend on target type determination
        // For now, we'll use a simple heuristic
        // if (target is player) m_playerStats.pvpAttacks++; else m_playerStats.pveAttacks++;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerAttack::UpdatePlayerStatistics - Exception: %s", e.what());
    }
}

// Player Attack Utility Functions Implementation
namespace PlayerAttackUtils {

/**
 * Convert PlayerAttackResult to string
 */
std::string AttackResultToString(PlayerAttackResult result) {
    switch (result) {
        case PlayerAttackResult::Success: return "Success";
        case PlayerAttackResult::Failed: return "Failed";
        case PlayerAttackResult::InvalidTarget: return "InvalidTarget";
        case PlayerAttackResult::InvalidSkill: return "InvalidSkill";
        case PlayerAttackResult::NotAttackable: return "NotAttackable";
        case PlayerAttackResult::OutOfRange: return "OutOfRange";
        case PlayerAttackResult::InsufficientResources: return "InsufficientResources";
        case PlayerAttackResult::Stunned: return "Stunned";
        case PlayerAttackResult::InSafeZone: return "InSafeZone";
        case PlayerAttackResult::InvalidWeapon: return "InvalidWeapon";
        default: return "Unknown";
    }
}

/**
 * Convert PlayerAttackType to string
 */
std::string AttackTypeToString(PlayerAttackType type) {
    switch (type) {
        case PlayerAttackType::General: return "General";
        case PlayerAttackType::Skill: return "Skill";
        case PlayerAttackType::Unit: return "Unit";
        case PlayerAttackType::Force: return "Force";
        case PlayerAttackType::Siege: return "Siege";
        case PlayerAttackType::SelfDestruction: return "SelfDestruction";
        case PlayerAttackType::Test: return "Test";
        default: return "Unknown";
    }
}

/**
 * Check if target is valid for player attack
 */
bool IsValidPlayerTarget(CPlayer* pPlayer, CCharacter* pTarget) {
    if (!pPlayer || !pTarget) {
        return false;
    }

    // Check if target is alive
    if (!pTarget->IsAlive()) {
        return false;
    }

    // Check if target is in safe zone
    if (pTarget->IsInSafeZone()) {
        return false;
    }

    // Check if player can attack this target type
    if (!pPlayer->CanAttackTarget(pTarget)) {
        return false;
    }

    // Check PvP restrictions
    if (pTarget->IsPlayer() && !pPlayer->CanAttackPlayer(static_cast<CPlayer*>(pTarget))) {
        return false;
    }

    return true;
}

/**
 * Calculate player attack range
 */
float CalculatePlayerAttackRange(CPlayer* pPlayer, PlayerAttackType attackType) {
    if (!pPlayer) {
        return 0.0f;
    }

    float baseRange = pPlayer->GetBaseAttackRange();

    switch (attackType) {
        case PlayerAttackType::General:
            return baseRange;
        case PlayerAttackType::Skill:
            return baseRange * 1.2f; // 20% bonus for skills
        case PlayerAttackType::Unit:
            return pPlayer->GetUnitAttackRange();
        case PlayerAttackType::Force:
            return baseRange * 1.5f; // 50% bonus for force attacks
        case PlayerAttackType::Siege:
            return pPlayer->GetSiegeAttackRange();
        default:
            return baseRange;
    }
}

/**
 * Check if player can perform attack type
 */
bool CanPerformAttackType(CPlayer* pPlayer, PlayerAttackType attackType) {
    if (!pPlayer) {
        return false;
    }

    switch (attackType) {
        case PlayerAttackType::General:
            return true; // All players can perform general attacks
        case PlayerAttackType::Skill:
            return pPlayer->HasSkills();
        case PlayerAttackType::Unit:
            return pPlayer->HasUnits();
        case PlayerAttackType::Force:
            return pPlayer->HasForceWeapons();
        case PlayerAttackType::Siege:
            return pPlayer->HasSiegeWeapons();
        case PlayerAttackType::SelfDestruction:
            return pPlayer->CanSelfDestruct();
        case PlayerAttackType::Test:
            return pPlayer->IsInTestMode();
        default:
            return false;
    }
}

/**
 * Get player weapon class
 */
int GetPlayerWeaponClass(CPlayer* pPlayer) {
    if (!pPlayer) {
        return 0;
    }

    return pPlayer->GetCurrentWeaponClass();
}

/**
 * Calculate player critical hit probability
 */
float CalculatePlayerCriticalProbability(CPlayer* pPlayer, CCharacter* pTarget) {
    if (!pPlayer || !pTarget) {
        return 0.0f;
    }

    // Base critical chance
    float baseCritical = 0.08f; // 8% base for players

    // Level difference modifier
    int levelDiff = pPlayer->GetLevel() - pTarget->GetLevel();
    float levelMod = levelDiff * 0.015f; // 1.5% per level difference

    // Equipment critical bonus
    float equipmentMod = pPlayer->GetCriticalRateBonus();

    // Skill critical bonus
    float skillMod = pPlayer->GetSkillCriticalBonus();

    // Clamp the result
    float totalCritical = baseCritical + levelMod + equipmentMod + skillMod;
    return std::max(0.0f, std::min(0.6f, totalCritical)); // Max 60% critical for players
}

} // namespace PlayerAttackUtils
