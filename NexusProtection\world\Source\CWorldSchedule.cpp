/**
 * @file CWorldSchedule.cpp
 * @brief Implementation of world scheduling system for managing timed events
 * @details Provides scheduling functionality for world events, timers, and scheduled operations
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "CWorldSchedule.h"
#include <algorithm>
#include <ctime>
#include <stdexcept>

// Forward declarations for legacy classes
class CRecordData {
public:
    CRecordData() = default;
    ~CRecordData() = default;
    // Add minimal interface as needed
};

class CMyTimer {
public:
    CMyTimer() = default;
    ~CMyTimer() = default;
    // Add minimal interface as needed
};

namespace NexusProtection {
namespace World {

// Constructor
CWorldSchedule::CWorldSchedule()
    : m_tblSch(std::make_unique<CRecordData>())
    , m_tmrCheck(std::make_unique<CMyTimer>())
    , m_bOper(false)
    , m_statistics{} {
    InitializeInternal();
}

// Destructor
CWorldSchedule::~CWorldSchedule() {
    Shutdown();
}

// Move constructor
CWorldSchedule::CWorldSchedule(CWorldSchedule&& other) noexcept
    : m_tblSch(std::move(other.m_tblSch))
    , m_tmrCheck(std::move(other.m_tmrCheck))
    , m_bOper(other.m_bOper)
    , m_schedules(std::move(other.m_schedules))
    , m_activationCallback(std::move(other.m_activationCallback))
    , m_deactivationCallback(std::move(other.m_deactivationCallback))
    , m_statistics(other.m_statistics) {
    
    // Reset the moved-from object
    other.m_bOper = false;
    other.m_statistics = {};
}

// Move assignment operator
CWorldSchedule& CWorldSchedule::operator=(CWorldSchedule&& other) noexcept {
    if (this != &other) {
        // Clean up current resources
        Shutdown();
        
        // Move data from other
        m_tblSch = std::move(other.m_tblSch);
        m_tmrCheck = std::move(other.m_tmrCheck);
        m_bOper = other.m_bOper;
        m_schedules = std::move(other.m_schedules);
        m_activationCallback = std::move(other.m_activationCallback);
        m_deactivationCallback = std::move(other.m_deactivationCallback);
        m_statistics = other.m_statistics;
        
        // Reset the moved-from object
        other.m_bOper = false;
        other.m_statistics = {};
    }
    return *this;
}

// Initialize the schedule system
bool CWorldSchedule::Initialize() {
    try {
        InitializeInternal();
        m_bOper = true;
        
        // Initialize statistics
        m_statistics = {};
        m_statistics.lastCheckTime = std::chrono::system_clock::now();
        
        return true;
    }
    catch (...) {
        m_bOper = false;
        return false;
    }
}

// Shutdown the schedule system
void CWorldSchedule::Shutdown() {
    m_bOper = false;
    Clear();
    ClearCallbacks();
}

// Check and process all schedules
void CWorldSchedule::CheckSchedules() {
    if (!m_bOper) {
        return;
    }
    
    // Get current time
    int32_t currentHour, currentMinute, currentDay;
    GetCurrentTime(currentHour, currentMinute, currentDay);
    
    // Process each schedule entry
    for (auto& entry : m_schedules) {
        ProcessScheduleEntry(entry, currentHour, currentMinute, currentDay);
    }
    
    // Update statistics
    m_statistics.totalChecks++;
    m_statistics.lastCheckTime = std::chrono::system_clock::now();
    UpdateStatistics();
}

// Calculate schedule cursor position for given time
int32_t CWorldSchedule::CalcScheduleCursor(int32_t hour, int32_t minute) const noexcept {
    // Validate input parameters
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        return -1;
    }
    
    // Calculate cursor position (minutes since midnight)
    int32_t cursor = hour * 60 + minute;
    
    // Ensure cursor is within valid range (0-1439 for 24 hours)
    if (cursor < 0 || cursor >= 1440) {
        return -1;
    }
    
    return cursor;
}

// Change schedule cursor to specified position
void CWorldSchedule::ChangeScheduleCursor(void* scheduleData) {
    if (!scheduleData || !m_bOper) {
        return;
    }
    
    // This would interact with the legacy schedule data structure
    // Implementation depends on the actual structure of scheduleData
    // For now, we'll just mark that a cursor change was requested
}

// Validate schedule data integrity
bool CWorldSchedule::DataCheck() const {
    if (!ValidateInternalState()) {
        return false;
    }
    
    // Validate all schedule entries
    for (const auto& entry : m_schedules) {
        if (!ValidateScheduleEntry(entry)) {
            return false;
        }
    }
    
    return true;
}

// Add a new schedule entry
bool CWorldSchedule::AddSchedule(const WorldScheduleEntry& entry) {
    if (!m_bOper || !ValidateScheduleEntry(entry)) {
        return false;
    }
    
    // Check if schedule already exists
    if (FindSchedule(entry.scheduleId) != nullptr) {
        return false; // Schedule ID already exists
    }
    
    // Check capacity
    if (m_schedules.size() >= GetMaxScheduleCount()) {
        return false; // At capacity
    }
    
    // Add the schedule
    m_schedules.push_back(entry);
    return true;
}

// Remove a schedule entry by ID
bool CWorldSchedule::RemoveSchedule(uint32_t scheduleId) {
    if (!m_bOper) {
        return false;
    }
    
    auto it = std::find_if(m_schedules.begin(), m_schedules.end(),
        [scheduleId](const WorldScheduleEntry& entry) {
            return entry.scheduleId == scheduleId;
        });
    
    if (it != m_schedules.end()) {
        m_schedules.erase(it);
        return true;
    }
    
    return false;
}

// Update an existing schedule entry
bool CWorldSchedule::UpdateSchedule(uint32_t scheduleId, const WorldScheduleEntry& entry) {
    if (!m_bOper || !ValidateScheduleEntry(entry)) {
        return false;
    }
    
    auto it = std::find_if(m_schedules.begin(), m_schedules.end(),
        [scheduleId](const WorldScheduleEntry& existing) {
            return existing.scheduleId == scheduleId;
        });
    
    if (it != m_schedules.end()) {
        *it = entry;
        return true;
    }
    
    return false;
}

// Find a schedule entry by ID
const WorldScheduleEntry* CWorldSchedule::FindSchedule(uint32_t scheduleId) const {
    auto it = std::find_if(m_schedules.begin(), m_schedules.end(),
        [scheduleId](const WorldScheduleEntry& entry) {
            return entry.scheduleId == scheduleId;
        });
    
    return (it != m_schedules.end()) ? &(*it) : nullptr;
}

// Get all active schedules
std::vector<const WorldScheduleEntry*> CWorldSchedule::GetActiveSchedules() const {
    std::vector<const WorldScheduleEntry*> activeSchedules;
    
    for (const auto& entry : m_schedules) {
        if (entry.isActive) {
            activeSchedules.push_back(&entry);
        }
    }
    
    return activeSchedules;
}

// Get schedules by event type
std::vector<const WorldScheduleEntry*> CWorldSchedule::GetSchedulesByType(ScheduleEventType eventType) const {
    std::vector<const WorldScheduleEntry*> matchingSchedules;
    uint32_t typeValue = static_cast<uint32_t>(eventType);
    
    for (const auto& entry : m_schedules) {
        if (entry.eventType == typeValue) {
            matchingSchedules.push_back(&entry);
        }
    }
    
    return matchingSchedules;
}

// Check if the schedule system is operational
bool CWorldSchedule::IsOperational() const noexcept {
    return m_bOper;
}

// Set the operational status
void CWorldSchedule::SetOperational(bool operational) noexcept {
    m_bOper = operational;
}

// Get the number of schedules
std::size_t CWorldSchedule::GetScheduleCount() const noexcept {
    return m_schedules.size();
}

// Check if the schedule system is empty
bool CWorldSchedule::IsEmpty() const noexcept {
    return m_schedules.empty();
}

// Get comprehensive statistics
ScheduleStatistics CWorldSchedule::GetStatistics() const {
    UpdateStatistics();
    return m_statistics;
}

// Clear all schedules
void CWorldSchedule::Clear() {
    m_schedules.clear();
    m_statistics = {};
    m_statistics.lastCheckTime = std::chrono::system_clock::now();
}

// Set callback for schedule activation events
void CWorldSchedule::SetActivationCallback(ScheduleCallback callback) {
    m_activationCallback = std::move(callback);
}

// Set callback for schedule deactivation events
void CWorldSchedule::SetDeactivationCallback(ScheduleCallback callback) {
    m_deactivationCallback = std::move(callback);
}

// Remove all callbacks
void CWorldSchedule::ClearCallbacks() {
    m_activationCallback = nullptr;
    m_deactivationCallback = nullptr;
}

// Validate a schedule entry
bool CWorldSchedule::ValidateScheduleEntry(const WorldScheduleEntry& entry) {
    // Basic validation
    if (entry.scheduleId == 0) return false;
    if (entry.startHour > 23 || entry.startMinute > 59) return false;
    if (entry.endHour > 23 || entry.endMinute > 59) return false;
    if (entry.dayMask > 127) return false; // 7 bits for days of week
    if (entry.eventType > static_cast<uint32_t>(ScheduleEventType::Custom)) return false;
    
    // Validate time range
    int32_t startTime = entry.startHour * 60 + entry.startMinute;
    int32_t endTime = entry.endHour * 60 + entry.endMinute;
    
    // Allow overnight schedules (end time can be less than start time)
    if (startTime == endTime) return false; // Zero duration not allowed
    
    return true;
}

// Check if a time falls within a schedule
bool CWorldSchedule::IsTimeInSchedule(const WorldScheduleEntry& entry, 
                                     int32_t hour, int32_t minute, int32_t dayOfWeek) {
    if (!entry.isActive) return false;
    
    // Check day of week
    if (!(entry.dayMask & (1 << dayOfWeek))) return false;
    
    int32_t currentTime = hour * 60 + minute;
    int32_t startTime = entry.startHour * 60 + entry.startMinute;
    int32_t endTime = entry.endHour * 60 + entry.endMinute;
    
    // Handle overnight schedules
    if (startTime <= endTime) {
        // Same day schedule
        return (currentTime >= startTime && currentTime < endTime);
    } else {
        // Overnight schedule
        return (currentTime >= startTime || currentTime < endTime);
    }
}

// Get current system time components
void CWorldSchedule::GetCurrentTime(int32_t& hour, int32_t& minute, int32_t& dayOfWeek) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto* tm = std::localtime(&time_t);
    
    hour = tm->tm_hour;
    minute = tm->tm_min;
    dayOfWeek = tm->tm_wday; // 0 = Sunday
}

// Get memory usage of the schedule system
std::size_t CWorldSchedule::GetMemoryUsage() const {
    std::size_t size = sizeof(*this);
    
    // Add vector capacity
    size += m_schedules.capacity() * sizeof(WorldScheduleEntry);
    
    // Add string sizes
    for (const auto& entry : m_schedules) {
        size += entry.description.capacity();
    }
    
    return size;
}

// Initialize internal data structures
void CWorldSchedule::InitializeInternal() {
    // Reserve some initial capacity to avoid frequent reallocations
    m_schedules.reserve(100);
    
    // Initialize statistics
    m_statistics = {};
    m_statistics.lastCheckTime = std::chrono::system_clock::now();
}

// Process a single schedule entry
void CWorldSchedule::ProcessScheduleEntry(WorldScheduleEntry& entry, int32_t currentHour, 
                                         int32_t currentMinute, int32_t currentDay) {
    bool wasActive = entry.isActive;
    bool shouldBeActive = IsTimeInSchedule(entry, currentHour, currentMinute, currentDay);
    
    if (!wasActive && shouldBeActive) {
        // Schedule is becoming active
        entry.isActive = true;
        m_statistics.totalActivations++;
        
        if (m_activationCallback) {
            m_activationCallback(entry);
        }
    } else if (wasActive && !shouldBeActive) {
        // Schedule is becoming inactive
        entry.isActive = false;
        
        if (m_deactivationCallback) {
            m_deactivationCallback(entry);
        }
    }
}

// Update statistics
void CWorldSchedule::UpdateStatistics() const {
    m_statistics.totalSchedules = m_schedules.size();
    m_statistics.activeSchedules = 0;
    m_statistics.pendingSchedules = 0;
    m_statistics.expiredSchedules = 0;
    m_statistics.errorSchedules = 0;
    
    for (const auto& entry : m_schedules) {
        if (entry.isActive) {
            m_statistics.activeSchedules++;
        } else {
            m_statistics.pendingSchedules++;
        }
    }
}

// Validate internal state
bool CWorldSchedule::ValidateInternalState() const {
    // Check if essential components are valid
    if (!m_tblSch || !m_tmrCheck) {
        return false;
    }
    
    // Check schedule count is within limits
    if (m_schedules.size() > GetMaxScheduleCount()) {
        return false;
    }
    
    return true;
}

} // namespace World
} // namespace NexusProtection
