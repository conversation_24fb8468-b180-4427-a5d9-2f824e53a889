/**
 * @file CMainThread_DataFileInit_Legacy.cpp
 * @brief Legacy compatibility wrapper for CMainThread::DataFileInit
 * 
 * This file provides the exact original function signature for backward compatibility
 * with existing code that calls the original decompiled function.
 * 
 * Original function: DataFileInitCMainThreadAEAA_NXZ_1401E5BF0.c
 * Address: 0x1401E5BF0
 * Signature: char __fastcall CMainThread::DataFileInit(CMainThread *this)
 */

#include "../Headers/CMainThread_DataFileInit.h"
#include "../Headers/CMainThread.h"
#include <cstdint>
#include <iostream>

// External function declarations (to be properly linked)
extern uint64_t _security_cookie;
extern void MyMessageBox(const char* title, const char* format, ...);

/**
 * @brief Legacy DataFileInit function with exact original signature
 * This maintains 100% compatibility with the original decompiled function
 * 
 * @param this_ptr Pointer to CMainThread instance (original 'this' parameter)
 * @return char (1 for success, 0 for failure)
 */
extern "C" char __fastcall CMainThread_DataFileInit_Original(CMainThread* this_ptr) {
    // Security cookie setup (equivalent to original stack protection)
    uint64_t stackBuffer[176]; // Equivalent to original v8 buffer (176 * 4 bytes)
    uint64_t securityCookie = reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie;
    
    // Initialize stack buffer (equivalent to original initialization loop)
    for (size_t i = 0; i < 176; ++i) {
        reinterpret_cast<uint32_t*>(stackBuffer)[i] = 0xCCCCCCCC; // -858993460 in original
    }
    
    // Call the modern implementation
    char result = 0;
    if (this_ptr) {
        result = CMainThreadDataFileInit::DataFileInit_Legacy(this_ptr);
    }
    
    // Verify security cookie (equivalent to original stack protection check)
    if ((reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie) != securityCookie) {
        // Stack corruption detected - this would trigger security handler in original
        MyMessageBox("Security Error", "Stack corruption detected in CMainThread::DataFileInit");
        return 0;
    }
    
    return result;
}

/**
 * @brief Alternative legacy wrapper that can be used as a drop-in replacement
 * This provides the original calling convention and behavior
 */
char CMainThread::DataFileInit_OriginalBehavior() {
    try {
        // Replicate original variable setup
        char szErrCode[512] = {0};
        
        // Call modern data file initialization
        CMainThreadDataFileInit dataFileInit;
        DataFileLoadResult result = dataFileInit.InitializeDataFiles(this);
        
        // Convert result to original format
        return (result == DataFileLoadResult::Success) ? 1 : 0;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in DataFileInit_OriginalBehavior: " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Stub implementations for remaining loading phases
 * These would be implemented based on the actual system requirements
 */

DataFileLoadResult CMainThreadDataFileInit::LoadMessageData(CMainThread* mainThread) {
    try {
        return LoadMobMessageData(mainThread);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during message data loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}

DataFileLoadResult CMainThreadDataFileInit::LoadMobMessageData(CMainThread* mainThread) {
    try {
        LogFileLoading(DataFileType::MobMessageData, false);
        
        const auto& fileInfo = GetFileInfo(DataFileType::MobMessageData);
        char errorCode[512] = {0};
        
        // Create temporary CRecordData for mob message loading
        CRecordData mobMessageRecord;
        CRecordData_Constructor(&mobMessageRecord);
        
        if (!CRecordData_ReadRecord(&mobMessageRecord, fileInfo.filename.c_str(), fileInfo.structSize, errorCode)) {
            LogFileLoading(DataFileType::MobMessageData, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            CRecordData_Destructor(&mobMessageRecord);
            return DataFileLoadResult::Failure;
        }
        
        // Process mob message data (equivalent to original loop)
        uint32_t recordNum = CRecordData_GetRecordNum(&mobMessageRecord);
        
        // Allocate memory for mob messages
        _mob_message* mobMessages = static_cast<_mob_message*>(operator new[](saturated_mul(4, recordNum)));
        mainThread->SetMobMessage(mobMessages);
        
        for (uint32_t i = 0; i < recordNum; ++i) {
            _base_fld* record = static_cast<_base_fld*>(CRecordData_GetRecord_Index(&mobMessageRecord, i));
            if (!record) {
                LogFileLoading(DataFileType::MobMessageData, false, "Failed to get record");
                MyMessageBox("DatafileInit", errorCode);
                CRecordData_Destructor(&mobMessageRecord);
                return DataFileLoadResult::Failure;
            }
            
            // Process record data (equivalent to original processing)
            uint16_t index = static_cast<uint16_t>(atoi(record->m_strCode));
            mobMessages[i].wIndex = index;
            mobMessages[i].byUsingNum = (record + 1)->m_dwIndex;
        }
        
        CRecordData_Destructor(&mobMessageRecord);
        LogFileLoading(DataFileType::MobMessageData, true);
        return DataFileLoadResult::Success;
        
    } catch (const std::exception& e) {
        LogFileLoading(DataFileType::MobMessageData, false, e.what());
        return DataFileLoadResult::Failure;
    }
}

DataFileLoadResult CMainThreadDataFileInit::LoadItemSystems(CMainThread* mainThread) {
    try {
        // Load NPC character data
        if (auto result = LoadSingleFile(mainThread->GetNPCTable(), 
                                        GetFileInfo(DataFileType::NPCharacterData).filename,
                                        GetFileInfo(DataFileType::NPCharacterData).structSize,
                                        DataFileType::NPCharacterData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load animus item data
        if (auto result = LoadSingleFile(mainThread->GetAnimusTable(), 
                                        GetFileInfo(DataFileType::AnimusItemData).filename,
                                        GetFileInfo(DataFileType::AnimusItemData).structSize,
                                        DataFileType::AnimusItemData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load experience data
        if (auto result = LoadSingleFile(mainThread->GetExpTable(), 
                                        GetFileInfo(DataFileType::ExpData).filename,
                                        GetFileInfo(DataFileType::ExpData).structSize,
                                        DataFileType::ExpData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load item looting data
        LogFileLoading(DataFileType::ItemLootingData, false);
        char errorCode[512] = {0};
        if (!CItemLootTable_ReadRecord(mainThread->GetItemLootTable(), 
                                      GetFileInfo(DataFileType::ItemLootingData).filename.c_str(),
                                      mainThread->GetItemDataTable(), errorCode)) {
            LogFileLoading(DataFileType::ItemLootingData, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            return DataFileLoadResult::Failure;
        }
        LogFileLoading(DataFileType::ItemLootingData, true);
        
        // Load ore cutting data
        LogFileLoading(DataFileType::OreCuttingData, false);
        if (!COreCuttingTable_ReadRecord(mainThread->GetOreCuttingTable(), 
                                        GetFileInfo(DataFileType::OreCuttingData).filename.c_str(),
                                        &mainThread->GetItemDataTable()[17],
                                        &mainThread->GetItemDataTable()[18], errorCode)) {
            LogFileLoading(DataFileType::OreCuttingData, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            return DataFileLoadResult::Failure;
        }
        LogFileLoading(DataFileType::OreCuttingData, true);
        
        // Load item make data
        if (auto result = LoadSingleFile(mainThread->GetItemMakeDataTable(), 
                                        GetFileInfo(DataFileType::ItemMakeData).filename,
                                        GetFileInfo(DataFileType::ItemMakeData).structSize,
                                        DataFileType::ItemMakeData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load item combine data
        if (auto result = LoadSingleFile(mainThread->GetItemCombineDataTable(), 
                                        GetFileInfo(DataFileType::ItemCombineData).filename,
                                        GetFileInfo(DataFileType::ItemCombineData).structSize,
                                        DataFileType::ItemCombineData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load item exchange data
        if (auto result = LoadSingleFile(mainThread->GetItemExchangeDataTable(), 
                                        GetFileInfo(DataFileType::ItemExchangeData).filename,
                                        GetFileInfo(DataFileType::ItemExchangeData).structSize,
                                        DataFileType::ItemExchangeData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load item upgrade data
        LogFileLoading(DataFileType::ItemUpgradeData, false);
        if (!CItemUpgradeTable_ReadRecord(mainThread->GetItemUpgradeTable(), 
                                         GetFileInfo(DataFileType::ItemUpgradeData).filename.c_str(),
                                         &mainThread->GetItemDataTable()[18], errorCode)) {
            LogFileLoading(DataFileType::ItemUpgradeData, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            return DataFileLoadResult::Failure;
        }
        LogFileLoading(DataFileType::ItemUpgradeData, true);
        
        return DataFileLoadResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during item systems loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}

DataFileLoadResult CMainThreadDataFileInit::LoadUnitData(CMainThread* mainThread) {
    try {
        return LoadUnitPartFiles(mainThread);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during unit data loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}

DataFileLoadResult CMainThreadDataFileInit::LoadUnitPartFiles(CMainThread* mainThread) {
    try {
        // Define unit part file types
        const std::array<DataFileType, 6> unitPartTypes = {
            DataFileType::UnitHeadData,
            DataFileType::UnitUpperData,
            DataFileType::UnitLowerData,
            DataFileType::UnitArmsData,
            DataFileType::UnitShoulderData,
            DataFileType::UnitBackData
        };
        
        // Load each unit part file
        for (size_t i = 0; i < unitPartTypes.size(); ++i) {
            const auto& fileType = unitPartTypes[i];
            const auto& fileInfo = GetFileInfo(fileType);
            
            if (auto result = LoadSingleFile(&mainThread->GetUnitPartTable()[i], 
                                            fileInfo.filename,
                                            fileInfo.structSize,
                                            fileType); 
                result != DataFileLoadResult::Success) {
                return result;
            }
        }
        
        // Load unit bullet data
        if (auto result = LoadSingleFile(mainThread->GetUnitBulletTable(), 
                                        GetFileInfo(DataFileType::UnitBulletData).filename,
                                        GetFileInfo(DataFileType::UnitBulletData).structSize,
                                        DataFileType::UnitBulletData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        // Load unit frame data
        if (auto result = LoadSingleFile(mainThread->GetUnitFrameTable(), 
                                        GetFileInfo(DataFileType::UnitFrameData).filename,
                                        GetFileInfo(DataFileType::UnitFrameData).structSize,
                                        DataFileType::UnitFrameData); 
            result != DataFileLoadResult::Success) {
            return result;
        }
        
        return DataFileLoadResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during unit part files loading: {}", e.what()));
        return DataFileLoadResult::Failure;
    }
}
