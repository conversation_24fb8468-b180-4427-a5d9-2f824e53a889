/**
 * @file CMainThread_SetGlobalDataName.cpp
 * @brief Implementation of global data name assignment system for CMainThread
 * 
 * Refactored from SetGlobalDataName function
 * This handles the assignment of global data names and identifiers
 * used throughout the server for data table references and naming conventions.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_SetGlobalDataName.h"
#include "CMainThread.h"
#include "CRecordData.h"
#include <iostream>
#include <format>
#include <algorithm>
#include <cassert>
#include <regex>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External message box function
extern void MyMessageBox(const char* title, const char* message);

// Global data names storage
std::unordered_map<std::string, std::string> g_GlobalDataNames;
std::mutex g_GlobalDataNamesMutex;

// Static member initialization
std::unordered_map<GlobalDataNameType, GlobalDataNameInfo> CMainThreadSetGlobalDataName::s_nameRegistry;

/**
 * @brief Constructor
 */
CMainThreadSetGlobalDataName::CMainThreadSetGlobalDataName() {
    InitializeNameRegistry();
    m_assignmentStats.Reset();
}

/**
 * @brief Destructor
 */
CMainThreadSetGlobalDataName::~CMainThreadSetGlobalDataName() = default;

/**
 * @brief Main global data name assignment function
 * 
 * Modern C++20 implementation of the original SetGlobalDataName function.
 * Assigns all global data names with comprehensive validation and error handling.
 * 
 * @param mainThread Pointer to CMainThread instance for accessing data
 * @return GlobalDataNameResult indicating success or failure
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignAllGlobalDataNames(CMainThread* mainThread) {
    try {
        m_assignmentStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting global data name assignment..." << std::endl;
        
        if (!mainThread) {
            SetLastError("Invalid CMainThread pointer");
            return GlobalDataNameResult::SystemError;
        }
        
        // Phase 1: Assign core data names
        auto result = AssignCoreDataNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 2: Assign character data names
        result = AssignCharacterDataNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 3: Assign item system names
        result = AssignItemSystemNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 4: Assign unit data names
        result = AssignUnitDataNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 5: Assign system data names
        result = AssignSystemDataNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 6: Assign manager system names
        result = AssignManagerSystemNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 7: Assign configuration names
        result = AssignConfigurationNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Phase 8: Assign special global names
        result = AssignSpecialGlobalNames(mainThread);
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Finalize assignment statistics
        m_assignmentStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return GlobalDataNameResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Global data name assignment completed successfully in {}ms", 
                                m_assignmentStats.GetTotalAssignmentTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} assignments)", 
                                m_assignmentStats.GetSuccessRate(), 
                                m_assignmentStats.successfulAssignments) << std::endl;
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during global data name assignment: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Legacy SetGlobalDataName function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: bool __fastcall CMainThread::SetGlobalDataName(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThreadSetGlobalDataName::SetGlobalDataName_Legacy(CMainThread* mainThread) {
    try {
        CMainThreadSetGlobalDataName nameAssigner;
        GlobalDataNameResult result = nameAssigner.AssignAllGlobalDataNames(mainThread);
        return (result == GlobalDataNameResult::Success);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in SetGlobalDataName_Legacy: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Assign core data names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignCoreDataNames(CMainThread* mainThread) {
    try {
        // Assign item data name
        auto result = AssignDataTableName(GlobalDataNameType::ItemDataName, "ItemData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign skill data name
        result = AssignDataTableName(GlobalDataNameType::SkillDataName, "SkillData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign force data name
        result = AssignDataTableName(GlobalDataNameType::ForceDataName, "ForceData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign class skill data name
        result = AssignDataTableName(GlobalDataNameType::ClassSkillDataName, "ClassSkillData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign bullet item effect data name
        result = AssignDataTableName(GlobalDataNameType::BulletItemEffectDataName, "BulletItemEffectData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning core data names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign character data names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignCharacterDataNames(CMainThread* mainThread) {
    try {
        // Assign class data name
        auto result = AssignDataTableName(GlobalDataNameType::ClassDataName, "ClassData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign grade data name
        result = AssignDataTableName(GlobalDataNameType::GradeDataName, "GradeData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign player character data name
        result = AssignDataTableName(GlobalDataNameType::PlayerCharacterDataName, "PlayerCharacterData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign monster character data name
        result = AssignDataTableName(GlobalDataNameType::MonsterCharacterDataName, "MonsterCharacterData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign NPC character data name
        result = AssignDataTableName(GlobalDataNameType::NPCharacterDataName, "NPCharacterData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning character data names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign item system names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignItemSystemNames(CMainThread* mainThread) {
    try {
        // Assign animus item data name
        auto result = AssignDataTableName(GlobalDataNameType::AnimusItemDataName, "AnimusItemData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign experience data name
        result = AssignDataTableName(GlobalDataNameType::ExpDataName, "ExpData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item looting data name
        result = AssignDataTableName(GlobalDataNameType::ItemLootingDataName, "ItemLootingData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign ore cutting data name
        result = AssignDataTableName(GlobalDataNameType::OreCuttingDataName, "OreCuttingData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item make data name
        result = AssignDataTableName(GlobalDataNameType::ItemMakeDataName, "ItemMakeData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item combine data name
        result = AssignDataTableName(GlobalDataNameType::ItemCombineDataName, "ItemCombineData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item exchange data name
        result = AssignDataTableName(GlobalDataNameType::ItemExchangeDataName, "ItemExchangeData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item upgrade data name
        result = AssignDataTableName(GlobalDataNameType::ItemUpgradeDataName, "ItemUpgradeData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning item system names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign unit data names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignUnitDataNames(CMainThread* mainThread) {
    try {
        // Assign all unit part data names
        const std::array<std::pair<GlobalDataNameType, std::string>, 8> unitNames = {{
            {GlobalDataNameType::UnitHeadDataName, "UnitHeadData"},
            {GlobalDataNameType::UnitUpperDataName, "UnitUpperData"},
            {GlobalDataNameType::UnitLowerDataName, "UnitLowerData"},
            {GlobalDataNameType::UnitArmsDataName, "UnitArmsData"},
            {GlobalDataNameType::UnitShoulderDataName, "UnitShoulderData"},
            {GlobalDataNameType::UnitBackDataName, "UnitBackData"},
            {GlobalDataNameType::UnitBulletDataName, "UnitBulletData"},
            {GlobalDataNameType::UnitFrameDataName, "UnitFrameData"}
        }};
        
        for (const auto& [nameType, tableName] : unitNames) {
            auto result = AssignDataTableName(nameType, tableName);
            if (result != GlobalDataNameResult::Success) {
                return result;
            }
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning unit data names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign system data names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignSystemDataNames(CMainThread* mainThread) {
    try {
        // Assign edit data name
        auto result = AssignDataTableName(GlobalDataNameType::EditDataName, "EditData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign monster character AI data name
        result = AssignDataTableName(GlobalDataNameType::MonsterCharacterAIDataName, "MonsterCharacterAIData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign mob message data name
        result = AssignDataTableName(GlobalDataNameType::MobMessageDataName, "MobMessageData");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning system data names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign manager system names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignManagerSystemNames(CMainThread* mainThread) {
    try {
        // Assign potion manager name
        auto result = AssignManagerName(GlobalDataNameType::PotionManagerName, "PotionManager");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign quest manager name
        result = AssignManagerName(GlobalDataNameType::QuestManagerName, "QuestManager");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign item combine manager name
        result = AssignManagerName(GlobalDataNameType::ItemCombineManagerName, "ItemCombineManager");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign PC Bang system name
        result = AssignSystemName(GlobalDataNameType::PcBangSystemName, "PcBangSystem");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign SU item system name
        result = AssignSystemName(GlobalDataNameType::SUItemSystemName, "SUItemSystem");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign monster SP group table name
        result = AssignDataTableName(GlobalDataNameType::MonsterSPGroupTableName, "MonsterSPGroupTable");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning manager system names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign configuration names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignConfigurationNames(CMainThread* mainThread) {
    try {
        // Assign aggro calculate configuration name
        auto result = AssignConfigName(GlobalDataNameType::AggroCalculateConfigName, "AggroCalculateConfig");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign monster set configuration name
        result = AssignConfigName(GlobalDataNameType::MonsterSetConfigName, "MonsterSetConfig");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning configuration names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign special global names
 * @param mainThread Pointer to CMainThread instance
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignSpecialGlobalNames(CMainThread* mainThread) {
    try {
        // Assign server instance name
        auto result = AssignSystemName(GlobalDataNameType::ServerInstanceName, "NexusProtectionServer");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign database connection name
        result = AssignSystemName(GlobalDataNameType::DatabaseConnectionName, "MainDatabaseConnection");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign network configuration name
        result = AssignConfigName(GlobalDataNameType::NetworkConfigName, "NetworkConfiguration");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign security configuration name
        result = AssignConfigName(GlobalDataNameType::SecurityConfigName, "SecurityConfiguration");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        // Assign logging configuration name
        result = AssignConfigName(GlobalDataNameType::LoggingConfigName, "LoggingConfiguration");
        if (result != GlobalDataNameResult::Success) {
            return result;
        }
        
        return GlobalDataNameResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Exception assigning special global names: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Assign a data table name
 * @param nameType The type of name to assign
 * @param tableName The table name to assign
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignDataTableName(GlobalDataNameType nameType, const std::string& tableName) {
    return AssignGlobalDataName(nameType, tableName);
}

/**
 * @brief Assign a manager name
 * @param nameType The type of name to assign
 * @param managerName The manager name to assign
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignManagerName(GlobalDataNameType nameType, const std::string& managerName) {
    return AssignGlobalDataName(nameType, managerName);
}

/**
 * @brief Assign a configuration name
 * @param nameType The type of name to assign
 * @param configName The configuration name to assign
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignConfigName(GlobalDataNameType nameType, const std::string& configName) {
    return AssignGlobalDataName(nameType, configName);
}

/**
 * @brief Assign a system name
 * @param nameType The type of name to assign
 * @param systemName The system name to assign
 * @return GlobalDataNameResult
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignSystemName(GlobalDataNameType nameType, const std::string& systemName) {
    return AssignGlobalDataName(nameType, systemName);
}
