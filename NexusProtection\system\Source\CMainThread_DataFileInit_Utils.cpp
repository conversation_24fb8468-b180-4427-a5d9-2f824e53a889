/**
 * @file CMainThread_DataFileInit_Utils.cpp
 * @brief Utility functions for CMainThread data file initialization
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_DataFileInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>
#include <fstream>
#include <filesystem>

/**
 * @brief Get data file loading statistics
 * @return const reference to loading statistics
 */
const DataFileLoadStats& CMainThreadDataFileInit::GetLoadingStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_loadStats;
}

/**
 * @brief Check if a specific data file is loaded
 * @param fileType The data file type to check
 * @return true if loaded, false otherwise
 */
bool CMainThreadDataFileInit::IsFileLoaded(DataFileType fileType) const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    size_t index = static_cast<size_t>(fileType);
    if (index >= m_loadStats.fileStatus.size()) {
        return false;
    }
    return m_loadStats.fileStatus[index];
}

/**
 * @brief Get the last loading error message
 * @return string containing the last error message
 */
std::string CMainThreadDataFileInit::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get information about a specific data file type
 * @param fileType The data file type
 * @return DataFileInfo structure with file information
 */
DataFileInfo CMainThreadDataFileInit::GetFileInfo(DataFileType fileType) {
    auto it = s_fileRegistry.find(fileType);
    if (it != s_fileRegistry.end()) {
        return it->second;
    }
    return DataFileInfo("unknown", "Unknown file type", 0, false);
}

/**
 * @brief Validate all loaded data files
 * @return true if all required files are loaded and valid
 */
bool CMainThreadDataFileInit::ValidateLoadedData() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    // Check all required files
    for (const auto& [fileType, fileInfo] : s_fileRegistry) {
        if (fileInfo.isRequired) {
            size_t index = static_cast<size_t>(fileType);
            if (index >= m_loadStats.fileStatus.size() || !m_loadStats.fileStatus[index]) {
                return false;
            }
        }
    }
    
    return true;
}

/**
 * @brief Log file loading status
 * @param fileType The data file type
 * @param success Whether loading was successful
 * @param errorMsg Error message if failed
 */
void CMainThreadDataFileInit::LogFileLoading(DataFileType fileType, bool success, const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(fileType);
    if (index >= m_loadStats.fileStatus.size()) {
        return;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_loadStats.startTime);
    
    m_loadStats.fileStatus[index] = success;
    m_loadStats.fileLoadTime[index] = duration;
    
    if (success) {
        m_loadStats.successfulFiles++;
        
        // Get file size if possible
        const auto& fileInfo = GetFileInfo(fileType);
        if (!fileInfo.filename.empty() && fileInfo.filename != "WriteTableData(37)") {
            m_loadStats.fileSize[index] = GetFileSize(fileInfo.filename);
        }
        
        std::cout << std::format("[INFO] {} loaded successfully in {}ms", 
                                DataFileTypeToString(fileType), duration.count()) << std::endl;
    } else {
        m_loadStats.failedFiles++;
        std::string fullError = std::format("{} failed: {}", 
                                          DataFileTypeToString(fileType), errorMsg);
        m_loadStats.lastError = fullError;
        std::cerr << "[ERROR] " << fullError << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message to set
 */
void CMainThreadDataFileInit::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    std::cerr << "[ERROR] " << error << std::endl;
}

/**
 * @brief Validate that a file exists
 * @param filename The filename to check
 * @return true if file exists, false otherwise
 */
bool CMainThreadDataFileInit::ValidateFileExists(const std::string& filename) {
    try {
        if (filename.empty() || filename == "WriteTableData(37)") {
            return true; // Special case for table data
        }
        
        return std::filesystem::exists(filename);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception checking file existence for " << filename << ": " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Get the size of a file
 * @param filename The filename to check
 * @return File size in bytes, 0 if file doesn't exist or error
 */
size_t CMainThreadDataFileInit::GetFileSize(const std::string& filename) {
    try {
        if (filename.empty() || filename == "WriteTableData(37)") {
            return 0; // Special case for table data
        }
        
        if (std::filesystem::exists(filename)) {
            return std::filesystem::file_size(filename);
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception getting file size for " << filename << ": " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Legacy WriteTableData wrapper
 * @param tableId Table identifier
 * @param tableData Pointer to table data
 * @param param Additional parameter
 * @param errorCode Error code buffer
 * @return true if successful, false otherwise
 */
bool CMainThreadDataFileInit::WriteTableData_Legacy(int tableId, void* tableData, int param, char* errorCode) {
    // This would call the actual WriteTableData function
    // For now, we'll return true as a placeholder
    return WriteTableData(tableId, tableData, param, errorCode);
}

/**
 * @brief Convert DataFileType enum to string for logging
 * @param fileType The data file type
 * @return String representation of the file type
 */
std::string DataFileTypeToString(DataFileType fileType) {
    switch (fileType) {
        case DataFileType::ItemData: return "ItemData";
        case DataFileType::SkillData: return "SkillData";
        case DataFileType::ForceData: return "ForceData";
        case DataFileType::ClassSkillData: return "ClassSkillData";
        case DataFileType::BulletItemEffectData: return "BulletItemEffectData";
        case DataFileType::ClassData: return "ClassData";
        case DataFileType::GradeData: return "GradeData";
        case DataFileType::PlayerCharacterData: return "PlayerCharacterData";
        case DataFileType::MonsterCharacterData: return "MonsterCharacterData";
        case DataFileType::NPCharacterData: return "NPCharacterData";
        case DataFileType::MobMessageData: return "MobMessageData";
        case DataFileType::AnimusItemData: return "AnimusItemData";
        case DataFileType::ExpData: return "ExpData";
        case DataFileType::ItemLootingData: return "ItemLootingData";
        case DataFileType::OreCuttingData: return "OreCuttingData";
        case DataFileType::ItemMakeData: return "ItemMakeData";
        case DataFileType::ItemCombineData: return "ItemCombineData";
        case DataFileType::ItemExchangeData: return "ItemExchangeData";
        case DataFileType::ItemUpgradeData: return "ItemUpgradeData";
        case DataFileType::UnitHeadData: return "UnitHeadData";
        case DataFileType::UnitUpperData: return "UnitUpperData";
        case DataFileType::UnitLowerData: return "UnitLowerData";
        case DataFileType::UnitArmsData: return "UnitArmsData";
        case DataFileType::UnitShoulderData: return "UnitShoulderData";
        case DataFileType::UnitBackData: return "UnitBackData";
        case DataFileType::UnitBulletData: return "UnitBulletData";
        case DataFileType::UnitFrameData: return "UnitFrameData";
        case DataFileType::EditData: return "EditData";
        case DataFileType::MonsterCharacterAIData: return "MonsterCharacterAIData";
        case DataFileType::PotionSystem: return "PotionSystem";
        case DataFileType::QuestSystem: return "QuestSystem";
        case DataFileType::ItemCombineSystem: return "ItemCombineSystem";
        case DataFileType::PcBangSystem: return "PcBangSystem";
        case DataFileType::SUItemSystem: return "SUItemSystem";
        case DataFileType::AggroCalculateConfig: return "AggroCalculateConfig";
        case DataFileType::MonsterSetConfig: return "MonsterSetConfig";
        default: return "Unknown";
    }
}

/**
 * @brief Convert DataFileLoadResult enum to string for logging
 * @param result The load result
 * @return String representation of the result
 */
std::string DataFileLoadResultToString(DataFileLoadResult result) {
    switch (result) {
        case DataFileLoadResult::Success: return "Success";
        case DataFileLoadResult::Failure: return "Failure";
        case DataFileLoadResult::FileNotFound: return "FileNotFound";
        case DataFileLoadResult::InvalidFormat: return "InvalidFormat";
        case DataFileLoadResult::MemoryError: return "MemoryError";
        case DataFileLoadResult::DependencyError: return "DependencyError";
        case DataFileLoadResult::ValidationError: return "ValidationError";
        case DataFileLoadResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief Load a single data file
 * @param recordData Pointer to CRecordData structure
 * @param filename Filename to load
 * @param structSize Structure size for the data
 * @param fileType Data file type for logging
 * @return DataFileLoadResult
 */
DataFileLoadResult CMainThreadDataFileInit::LoadSingleFile(CRecordData* recordData, const std::string& filename, 
                                                           uint32_t structSize, DataFileType fileType) {
    try {
        LogFileLoading(fileType, false);
        
        if (!ValidateFileExists(filename)) {
            LogFileLoading(fileType, false, "File not found");
            return DataFileLoadResult::FileNotFound;
        }
        
        char errorCode[512] = {0};
        
        if (!CRecordData_ReadRecord(recordData, filename.c_str(), structSize, errorCode)) {
            LogFileLoading(fileType, false, errorCode);
            MyMessageBox("DatafileInit", errorCode);
            return DataFileLoadResult::Failure;
        }
        
        LogFileLoading(fileType, true);
        return DataFileLoadResult::Success;
        
    } catch (const std::exception& e) {
        LogFileLoading(fileType, false, e.what());
        return DataFileLoadResult::Failure;
    }
}
