/**
 * @file CMainThread_ObjectInit.h
 * @brief Game object initialization system for CMainThread
 * 
 * Refactored from ObjectInitCMainThreadAEAA_NXZ_1401EB650.c
 * This handles the initialization of all game objects including players, monsters,
 * NPCs, merchants, towers, stones, traps, and other interactive world objects.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <atomic>

// Forward declarations for game object classes
class CUserDB;
class CPartyPlayer;
class CPlayer;
class CMonster;
class CMerchant;
class CAnimus;
class CGuardTower;
class CHolyStone;
class CHolyKeeper;
class CTrap;
class CItemBox;
class CParkingUnit;
class CDarkHole;
class CGuild;
class RFEventBase;
class RFEvent_ClassRefine;
class CRaceBossMsgController;
class CReturnGateController;
class CRecallEffectController;
class LendItemMng;
class CNuclearBombMgr;
class TimeLimitJadeMng;
class CPvpCashMng;
class CMonsterEventSet;
class DfAIMgr;

// Legacy structures for compatibility
struct _object_id;

/**
 * @enum GameObjectType
 * @brief Enumeration of all game object types for initialization tracking
 */
enum class GameObjectType : uint32_t {
    // Core Player Objects
    UserDatabase = 0,
    PartyPlayer,
    Player,
    
    // World Objects
    Monster,
    Merchant,
    Animus,
    GuardTower,
    HolyStone,
    HolyKeeper,
    Trap,
    ItemBox,
    ParkingUnit,
    DarkHole,
    Guild,
    
    // Event Objects
    RFEventBase,
    RFEventClassRefine,
    
    // Controller Objects
    RaceBossMsgController,
    ReturnGateController,
    RecallEffectController,
    
    // Manager Objects
    LendItemManager,
    NuclearBombManager,
    TimeLimitJadeManager,
    PvpCashManager,
    MonsterEventSet,
    DfAIManager,
    
    MAX_OBJECT_TYPES
};

/**
 * @enum ObjectInitResult
 * @brief Result codes for object initialization operations
 */
enum class ObjectInitResult : int32_t {
    Success = 1,
    Failure = 0,
    MemoryAllocationFailed = -1,
    InvalidParameters = -2,
    DependencyError = -3,
    InitializationError = -4,
    SecurityError = -5
};

/**
 * @struct ObjectPoolInfo
 * @brief Information about an object pool
 */
struct ObjectPoolInfo {
    std::string name;
    std::string description;
    uint32_t maxCount;
    size_t objectSize;
    uint8_t objectType;  // Object type ID for _object_id
    bool isRequired;
    
    ObjectPoolInfo() = default;
    ObjectPoolInfo(const std::string& n, const std::string& desc, uint32_t count, 
                   size_t size, uint8_t type, bool required = true)
        : name(n), description(desc), maxCount(count), objectSize(size), 
          objectType(type), isRequired(required) {}
};

/**
 * @struct ObjectInitStats
 * @brief Statistics for object initialization process
 */
struct ObjectInitStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(GameObjectType::MAX_OBJECT_TYPES)> objectStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(GameObjectType::MAX_OBJECT_TYPES)> objectInitTime;
    std::array<uint32_t, static_cast<size_t>(GameObjectType::MAX_OBJECT_TYPES)> objectCount;
    std::array<size_t, static_cast<size_t>(GameObjectType::MAX_OBJECT_TYPES)> memoryAllocated;
    uint32_t successfulObjects;
    uint32_t failedObjects;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        objectStatus.fill(false);
        objectInitTime.fill(std::chrono::milliseconds::zero());
        objectCount.fill(0);
        memoryAllocated.fill(0);
        successfulObjects = 0;
        failedObjects = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalInitTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulObjects + failedObjects;
        return total > 0 ? (static_cast<double>(successfulObjects) / total) * 100.0 : 0.0;
    }
    
    size_t GetTotalMemoryAllocated() const {
        size_t total = 0;
        for (const auto& mem : memoryAllocated) {
            total += mem;
        }
        return total;
    }
    
    uint32_t GetTotalObjectCount() const {
        uint32_t total = 0;
        for (const auto& count : objectCount) {
            total += count;
        }
        return total;
    }
};

/**
 * @class CMainThreadObjectInit
 * @brief Game object initialization and management system
 * 
 * This class handles the initialization of all game objects including players,
 * monsters, NPCs, and interactive world objects. It provides modern C++20 
 * interfaces while maintaining compatibility with the original object structures.
 * 
 * Original function: CMainThread::ObjectInit (Address: 0x1401EB650)
 * Refactored to modern C++20 with comprehensive error handling and monitoring.
 */
class CMainThreadObjectInit {
public:
    /**
     * @brief Constructor
     */
    CMainThreadObjectInit();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadObjectInit();
    
    // Delete copy constructor and assignment operator
    CMainThreadObjectInit(const CMainThreadObjectInit&) = delete;
    CMainThreadObjectInit& operator=(const CMainThreadObjectInit&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadObjectInit(CMainThreadObjectInit&&) = default;
    CMainThreadObjectInit& operator=(CMainThreadObjectInit&&) = default;
    
    /**
     * @brief Main object initialization function
     * 
     * Initializes all game objects in the correct order with comprehensive error handling.
     * This is the modern refactored version of the original ObjectInit function.
     * 
     * @param mainThread Pointer to CMainThread instance for accessing global objects
     * @return ObjectInitResult indicating success or failure
     */
    ObjectInitResult InitializeGameObjects(class CMainThread* mainThread);
    
    /**
     * @brief Legacy ObjectInit function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: bool __fastcall CMainThread::ObjectInit(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return bool (true for success, false for failure)
     */
    static bool ObjectInit_Legacy(class CMainThread* mainThread);
    
    /**
     * @brief Get object initialization statistics
     * @return const reference to initialization statistics
     */
    const ObjectInitStats& GetInitializationStats() const;
    
    /**
     * @brief Check if a specific object type is initialized
     * @param objectType The object type to check
     * @return true if initialized, false otherwise
     */
    bool IsObjectInitialized(GameObjectType objectType) const;
    
    /**
     * @brief Get the last initialization error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get information about a specific object type
     * @param objectType The object type
     * @return ObjectPoolInfo structure with object information
     */
    static ObjectPoolInfo GetObjectInfo(GameObjectType objectType);
    
    /**
     * @brief Validate all initialized objects
     * @return true if all required objects are initialized and valid
     */
    bool ValidateInitializedObjects() const;

private:
    // Initialization statistics and monitoring
    ObjectInitStats m_initStats;
    mutable std::mutex m_statsMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // Object pool registry
    static std::unordered_map<GameObjectType, ObjectPoolInfo> s_objectRegistry;
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Core object initialization phases
    ObjectInitResult InitializePlayerObjects(class CMainThread* mainThread);
    ObjectInitResult InitializeWorldObjects(class CMainThread* mainThread);
    ObjectInitResult InitializeEventObjects(class CMainThread* mainThread);
    ObjectInitResult InitializeControllerObjects(class CMainThread* mainThread);
    ObjectInitResult InitializeManagerObjects(class CMainThread* mainThread);
    
    // Individual object pool initialization functions
    ObjectInitResult InitializeUserDatabase();
    ObjectInitResult InitializePartyPlayers();
    ObjectInitResult InitializePlayers();
    ObjectInitResult InitializeMonsters();
    ObjectInitResult InitializeMerchants();
    ObjectInitResult InitializeAnimus();
    ObjectInitResult InitializeGuardTowers();
    ObjectInitResult InitializeHolyStones();
    ObjectInitResult InitializeHolyKeepers();
    ObjectInitResult InitializeTraps();
    ObjectInitResult InitializeItemBoxes();
    ObjectInitResult InitializeParkingUnits();
    ObjectInitResult InitializeDarkHoles();
    ObjectInitResult InitializeGuilds();
    
    // Event and controller initialization functions
    ObjectInitResult InitializeRFEventBase(class CMainThread* mainThread);
    ObjectInitResult InitializeRFEventClassRefine(class CMainThread* mainThread);
    ObjectInitResult InitializeRaceBossMsgController();
    ObjectInitResult InitializeReturnGateController();
    ObjectInitResult InitializeRecallEffectController();
    
    // Manager initialization functions
    ObjectInitResult InitializeLendItemManager();
    ObjectInitResult InitializeNuclearBombManager();
    ObjectInitResult InitializeTimeLimitJadeManager();
    ObjectInitResult InitializePvpCashManager();
    ObjectInitResult InitializeMonsterEventSetManager();
    ObjectInitResult InitializeDfAIManager();
    
    // Utility functions
    void LogObjectInitialization(GameObjectType objectType, bool success, 
                                uint32_t count = 0, size_t memorySize = 0, 
                                const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    
    // Memory allocation helpers
    template<typename T>
    T* AllocateObjectArray(uint32_t count, GameObjectType objectType);
    
    // Object ID creation helper
    void CreateObjectID(_object_id* pID, uint8_t objectType, uint32_t index);
    
    // Static initialization
    static void InitializeObjectRegistry();
    
    // Legacy compatibility functions
    static void InitializeObjectID_Legacy(_object_id* pID, uint8_t type1, uint8_t type2, uint32_t index);
};

/**
 * @brief Convert GameObjectType enum to string for logging
 * @param objectType The object type
 * @return String representation of the object type
 */
std::string GameObjectTypeToString(GameObjectType objectType);

/**
 * @brief Convert ObjectInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string ObjectInitResultToString(ObjectInitResult result);
