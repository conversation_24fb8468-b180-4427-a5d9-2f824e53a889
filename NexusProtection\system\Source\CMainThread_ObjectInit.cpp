/**
 * @file CMainThread_ObjectInit.cpp
 * @brief Game object initialization implementation for CMainThread
 * 
 * Refactored from ObjectInitCMainThreadAEAA_NXZ_1401EB650.c
 * This implements the game object initialization system.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_ObjectInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>
#include <Windows.h>

// External function declarations (to be properly linked)
extern void MyMessageBox(const char* title, const char* format, ...);
extern void* operator new[](size_t size);
extern void operator delete[](void* ptr);
extern void ServerProgramExit(const char* function, int code);
extern uint64_t _security_cookie;

// External global objects
extern CUserDB g_UserDB[2532];
extern CPartyPlayer g_PartyPlayer[2532];
extern CPlayer g_Player[2532];
extern CMonster* g_Monster;
extern CMerchant* g_NPC;
extern CAnimus* g_Animus;
extern CGuardTower* g_Tower;
extern CHolyStone* g_Stone;
extern CHolyKeeper* g_Keeper;
extern CTrap* g_Trap;
extern CItemBox* g_ItemBox;
extern CParkingUnit* g_ParkingUnit;
extern CDarkHole* g_DarkHole;
extern CGuild* g_Guild;
extern CMonsterEventSet* g_MonsterEventSet;

// External class methods (to be properly linked)
extern void CUserDB_Init(CUserDB* userDB, uint32_t index);
extern void CPartyPlayer_Init(CPartyPlayer* partyPlayer, uint16_t index);
extern bool CPlayer_Init(CPlayer* player, _object_id* pID);
extern bool CMonster_Init(CMonster* monster, _object_id* pID);
extern bool CMerchant_Init(CMerchant* merchant, _object_id* pID);
extern bool CAnimus_Init(CAnimus* animus, _object_id* pID);
extern bool CGuardTower_Init(CGuardTower* tower, _object_id* pID);
extern bool CHolyStone_Init(CHolyStone* stone, _object_id* pID);
extern bool CHolyKeeper_Init(CHolyKeeper* keeper, _object_id* pID);
extern bool CTrap_Init(CTrap* trap, _object_id* pID);
extern bool CItemBox_Init(CItemBox* itemBox, _object_id* pID);
extern bool CParkingUnit_Init(CParkingUnit* parkingUnit, _object_id* pID);
extern bool CDarkHole_Init(CDarkHole* darkHole, _object_id* pID);
extern bool CGuild_Init(CGuild* guild, _object_id* pID);

extern void ObjectID_Constructor(_object_id* pID, uint8_t type1, uint8_t type2, uint32_t index);

// External manager instances and methods
extern CRaceBossMsgController* CRaceBossMsgController_Instance();
extern CReturnGateController* CReturnGateController_Instance();
extern CRecallEffectController* CRecallEffectController_Instance();
extern LendItemMng* LendItemMng_Instance();
extern CNuclearBombMgr* CNuclearBombMgr_Instance();
extern TimeLimitJadeMng* TimeLimitJadeMng_Instance();
extern CPvpCashMng* CPvpCashMng_Instance();

extern bool CRaceBossMsgController_Init(CRaceBossMsgController* controller);
extern bool CReturnGateController_Init(CReturnGateController* controller);
extern bool CRecallEffectController_Init(CRecallEffectController* controller);
extern bool LendItemMng_Initialize(LendItemMng* manager);
extern bool CNuclearBombMgr_MissileInit(CNuclearBombMgr* manager);
extern bool TimeLimitJadeMng_Init(TimeLimitJadeMng* manager);
extern bool CPvpCashMng_LoadData(CPvpCashMng* manager);
extern bool DfAIMgr_OnUsStateTBLInit();

extern void CMonsterEventSet_Constructor(CMonsterEventSet* eventSet);

// Static object registry initialization
std::unordered_map<GameObjectType, ObjectPoolInfo> CMainThreadObjectInit::s_objectRegistry;

/**
 * @brief Constructor
 */
CMainThreadObjectInit::CMainThreadObjectInit() {
    m_initStats.Reset();
    InitializeObjectRegistry();
}

/**
 * @brief Destructor
 */
CMainThreadObjectInit::~CMainThreadObjectInit() {
    // Cleanup if needed
}

/**
 * @brief Initialize the object registry with all object pool information
 */
void CMainThreadObjectInit::InitializeObjectRegistry() {
    if (!s_objectRegistry.empty()) {
        return; // Already initialized
    }
    
    // Core Player Objects
    s_objectRegistry[GameObjectType::UserDatabase] = ObjectPoolInfo("UserDatabase", "User database objects", 2532, sizeof(CUserDB), 0, true);
    s_objectRegistry[GameObjectType::PartyPlayer] = ObjectPoolInfo("PartyPlayer", "Party player objects", 2532, sizeof(CPartyPlayer), 0, true);
    s_objectRegistry[GameObjectType::Player] = ObjectPoolInfo("Player", "Player character objects", 2532, sizeof(CPlayer), 0, true);
    
    // World Objects
    s_objectRegistry[GameObjectType::Monster] = ObjectPoolInfo("Monster", "Monster objects", 30000, 0x1918, 1, true);
    s_objectRegistry[GameObjectType::Merchant] = ObjectPoolInfo("Merchant", "Merchant/NPC objects", 500, 0x768, 2, true);
    s_objectRegistry[GameObjectType::Animus] = ObjectPoolInfo("Animus", "Animus objects", 500, 0x9A0, 3, true);
    s_objectRegistry[GameObjectType::GuardTower] = ObjectPoolInfo("GuardTower", "Guard tower objects", 500, 0x798, 4, true);
    s_objectRegistry[GameObjectType::HolyStone] = ObjectPoolInfo("HolyStone", "Holy stone objects", 500, 0x7C0, 5, true);
    s_objectRegistry[GameObjectType::HolyKeeper] = ObjectPoolInfo("HolyKeeper", "Holy keeper objects", 500, 0x7C0, 6, true);
    s_objectRegistry[GameObjectType::Trap] = ObjectPoolInfo("Trap", "Trap objects", 500, 0x7C0, 7, true);
    s_objectRegistry[GameObjectType::ItemBox] = ObjectPoolInfo("ItemBox", "Item box objects", 500, 0x7C0, 8, true);
    s_objectRegistry[GameObjectType::ParkingUnit] = ObjectPoolInfo("ParkingUnit", "Parking unit objects", 500, 0x7C0, 9, true);
    s_objectRegistry[GameObjectType::DarkHole] = ObjectPoolInfo("DarkHole", "Dark hole objects", 500, 0x7C0, 10, true);
    s_objectRegistry[GameObjectType::Guild] = ObjectPoolInfo("Guild", "Guild objects", 500, 0x7C0, 11, true);
    
    // Event Objects
    s_objectRegistry[GameObjectType::RFEventBase] = ObjectPoolInfo("RFEventBase", "RF event base", 1, sizeof(RFEventBase), 0, true);
    s_objectRegistry[GameObjectType::RFEventClassRefine] = ObjectPoolInfo("RFEventClassRefine", "RF event class refine", 1, sizeof(RFEvent_ClassRefine), 0, true);
    
    // Controller Objects
    s_objectRegistry[GameObjectType::RaceBossMsgController] = ObjectPoolInfo("RaceBossMsgController", "Race boss message controller", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::ReturnGateController] = ObjectPoolInfo("ReturnGateController", "Return gate controller", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::RecallEffectController] = ObjectPoolInfo("RecallEffectController", "Recall effect controller", 1, 0, 0, true);
    
    // Manager Objects
    s_objectRegistry[GameObjectType::LendItemManager] = ObjectPoolInfo("LendItemManager", "Lend item manager", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::NuclearBombManager] = ObjectPoolInfo("NuclearBombManager", "Nuclear bomb manager", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::TimeLimitJadeManager] = ObjectPoolInfo("TimeLimitJadeManager", "Time limit jade manager", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::PvpCashManager] = ObjectPoolInfo("PvpCashManager", "PvP cash manager", 1, 0, 0, true);
    s_objectRegistry[GameObjectType::MonsterEventSet] = ObjectPoolInfo("MonsterEventSet", "Monster event set", 1, 0x93F00, 0, true);
    s_objectRegistry[GameObjectType::DfAIManager] = ObjectPoolInfo("DfAIManager", "Df AI manager", 1, 0, 0, true);
}

/**
 * @brief Main object initialization function
 * 
 * Modern C++20 implementation of the original ObjectInit function.
 * Initializes all game objects in the correct order with comprehensive error handling.
 * 
 * @param mainThread Pointer to CMainThread instance for accessing global objects
 * @return ObjectInitResult indicating success or failure
 */
ObjectInitResult CMainThreadObjectInit::InitializeGameObjects(CMainThread* mainThread) {
    try {
        m_initStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting game object initialization..." << std::endl;
        
        // Phase 1: Initialize player objects
        if (auto result = InitializePlayerObjects(mainThread); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Phase 2: Initialize world objects
        if (auto result = InitializeWorldObjects(mainThread); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Phase 3: Initialize event objects
        if (auto result = InitializeEventObjects(mainThread); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Phase 4: Initialize controller objects
        if (auto result = InitializeControllerObjects(mainThread); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Phase 5: Initialize manager objects
        if (auto result = InitializeManagerObjects(mainThread); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Finalize initialization statistics
        m_initStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return ObjectInitResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Game object initialization completed successfully in {}ms", 
                                m_initStats.GetTotalInitTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} objects, {} MB)", 
                                m_initStats.GetSuccessRate(), 
                                m_initStats.GetTotalObjectCount(),
                                m_initStats.GetTotalMemoryAllocated() / (1024 * 1024)) << std::endl;
        
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during object initialization: {}", e.what()));
        m_initStats.endTime = std::chrono::steady_clock::now();
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Legacy ObjectInit function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: bool __fastcall CMainThread::ObjectInit(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThreadObjectInit::ObjectInit_Legacy(CMainThread* mainThread) {
    try {
        CMainThreadObjectInit objectInit;
        ObjectInitResult result = objectInit.InitializeGameObjects(mainThread);
        return (result == ObjectInitResult::Success);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ObjectInit_Legacy: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Initialize player objects (UserDB, PartyPlayer, Player)
 * @param mainThread Pointer to CMainThread instance
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializePlayerObjects(CMainThread* mainThread) {
    try {
        // Initialize UserDB objects
        if (auto result = InitializeUserDatabase(); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Initialize PartyPlayer objects
        if (auto result = InitializePartyPlayers(); result != ObjectInitResult::Success) {
            return result;
        }
        
        // Initialize Player objects
        if (auto result = InitializePlayers(); result != ObjectInitResult::Success) {
            return result;
        }
        
        return ObjectInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during player objects initialization: {}", e.what()));
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize UserDB objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializeUserDatabase() {
    try {
        LogObjectInitialization(GameObjectType::UserDatabase, false);

        const auto& objectInfo = GetObjectInfo(GameObjectType::UserDatabase);

        // Initialize all UserDB objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            CUserDB_Init(&g_UserDB[index], index);
        }

        LogObjectInitialization(GameObjectType::UserDatabase, true, objectInfo.maxCount,
                               objectInfo.maxCount * objectInfo.objectSize);
        return ObjectInitResult::Success;

    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::UserDatabase, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize PartyPlayer objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializePartyPlayers() {
    try {
        LogObjectInitialization(GameObjectType::PartyPlayer, false);

        const auto& objectInfo = GetObjectInfo(GameObjectType::PartyPlayer);

        // Initialize all PartyPlayer objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            CPartyPlayer_Init(&g_PartyPlayer[index], static_cast<uint16_t>(index));
        }

        LogObjectInitialization(GameObjectType::PartyPlayer, true, objectInfo.maxCount,
                               objectInfo.maxCount * objectInfo.objectSize);
        return ObjectInitResult::Success;

    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::PartyPlayer, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize Player objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializePlayers() {
    try {
        LogObjectInitialization(GameObjectType::Player, false);

        const auto& objectInfo = GetObjectInfo(GameObjectType::Player);

        // Initialize all Player objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id playerID;
            CreateObjectID(&playerID, objectInfo.objectType, index);

            if (!CPlayer_Init(&g_Player[index], &playerID)) {
                LogObjectInitialization(GameObjectType::Player, false, 0, 0,
                                       std::format("Failed to initialize player {}", index));
                return ObjectInitResult::InitializationError;
            }
        }

        LogObjectInitialization(GameObjectType::Player, true, objectInfo.maxCount,
                               objectInfo.maxCount * objectInfo.objectSize);
        return ObjectInitResult::Success;

    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Player, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize world objects (Monsters, NPCs, etc.)
 * @param mainThread Pointer to CMainThread instance
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializeWorldObjects(CMainThread* mainThread) {
    try {
        // Initialize Monsters
        if (auto result = InitializeMonsters(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Merchants/NPCs
        if (auto result = InitializeMerchants(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Animus
        if (auto result = InitializeAnimus(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Guard Towers
        if (auto result = InitializeGuardTowers(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Holy Stones
        if (auto result = InitializeHolyStones(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Holy Keepers
        if (auto result = InitializeHolyKeepers(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Traps
        if (auto result = InitializeTraps(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Item Boxes
        if (auto result = InitializeItemBoxes(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Parking Units
        if (auto result = InitializeParkingUnits(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Dark Holes
        if (auto result = InitializeDarkHoles(); result != ObjectInitResult::Success) {
            return result;
        }

        // Initialize Guilds
        if (auto result = InitializeGuilds(); result != ObjectInitResult::Success) {
            return result;
        }

        return ObjectInitResult::Success;

    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during world objects initialization: {}", e.what()));
        return ObjectInitResult::Failure;
    }
}

/**
 * @brief Initialize Monster objects
 * @return ObjectInitResult
 */
ObjectInitResult CMainThreadObjectInit::InitializeMonsters() {
    try {
        LogObjectInitialization(GameObjectType::Monster, false);

        const auto& objectInfo = GetObjectInfo(GameObjectType::Monster);

        // Allocate memory for monsters (equivalent to original operator new[])
        size_t totalSize = 4 + (objectInfo.objectSize * objectInfo.maxCount); // 4 bytes for count + objects
        void* monsterMemory = operator new[](totalSize);

        if (!monsterMemory) {
            LogObjectInitialization(GameObjectType::Monster, false, 0, 0, "Memory allocation failed");
            return ObjectInitResult::MemoryAllocationFailed;
        }

        // Set count and get object array pointer
        *static_cast<uint32_t*>(monsterMemory) = objectInfo.maxCount;
        g_Monster = static_cast<CMonster*>(static_cast<char*>(monsterMemory) + 8);

        // Initialize all Monster objects (equivalent to original loop)
        for (uint32_t index = 0; index < objectInfo.maxCount; ++index) {
            _object_id monsterID;
            CreateObjectID(&monsterID, objectInfo.objectType, index);

            if (!CMonster_Init(&g_Monster[index], &monsterID)) {
                LogObjectInitialization(GameObjectType::Monster, false, 0, 0,
                                       std::format("Failed to initialize monster {}", index));
                return ObjectInitResult::InitializationError;
            }
        }

        LogObjectInitialization(GameObjectType::Monster, true, objectInfo.maxCount, totalSize);
        return ObjectInitResult::Success;

    } catch (const std::exception& e) {
        LogObjectInitialization(GameObjectType::Monster, false, 0, 0, e.what());
        return ObjectInitResult::Failure;
    }
}
