#pragma once

#include "CRFNewDatabase.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>

/**
 * World database operation types
 */
enum class EWorldDBOperation : int {
    CHARACTER_CREATE = 0,
    CHARACTER_UPDATE = 1,
    CHARACTER_DELETE = 2,
    INVENTORY_UPDATE = 3,
    QUEST_UPDATE = 4,
    GUILD_UPDATE = 5,
    BUDDY_UPDATE = 6,
    POST_UPDATE = 7,
    AUTOMINE_UPDATE = 8,
    REBIRTH_UPDATE = 9
};

/**
 * Character data structure for database operations
 */
struct CharacterDBData {
    uint32_t dwSerial;
    std::string szName;
    uint32_t dwLevel;
    uint32_t dwExp;
    uint32_t dwClass;
    uint32_t dwHP;
    uint32_t dwMP;
    float fPosX;
    float fPosY;
    float fPosZ;
    uint32_t dwMapIndex;
    
    CharacterDBData() 
        : dwSerial(0), dwLevel(1), dwExp(0), dwClass(0), 
          dwHP(100), dwMP(100), fPosX(0.0f), fPosY(0.0f), fPosZ(0.0f), 
          dwMapIndex(0) {}
};

/**
 * Quest data structure for database operations
 */
struct QuestDBData {
    uint32_t dwCharacterSerial;
    uint32_t dwQuestID;
    uint32_t dwState;
    std::string szProgress;
    uint64_t dwStartTime;
    uint64_t dwEndTime;
    
    QuestDBData() 
        : dwCharacterSerial(0), dwQuestID(0), dwState(0), 
          dwStartTime(0), dwEndTime(0) {}
};

/**
 * Guild data structure for database operations
 */
struct GuildDBData {
    uint32_t dwGuildID;
    std::string szGuildName;
    uint32_t dwMasterSerial;
    uint32_t dwLevel;
    uint32_t dwExp;
    uint32_t dwMemberCount;
    std::string szNotice;
    
    GuildDBData() 
        : dwGuildID(0), dwMasterSerial(0), dwLevel(1), 
          dwExp(0), dwMemberCount(0) {}
};

/**
 * CRFWorldDatabase - World-specific database operations
 * Refactored from 0CRFWorldDatabaseQEAAXZ_140489680.c
 */
class CRFWorldDatabase : public CRFNewDatabase {
public:
    /**
     * Constructor
     */
    CRFWorldDatabase();
    
    /**
     * Virtual destructor
     */
    virtual ~CRFWorldDatabase();
    
    // Character management operations
    /**
     * Create character record
     * @param characterData Character data to create
     * @return true if creation successful
     */
    bool CreateCharacter(const CharacterDBData& characterData);
    
    /**
     * Update character data
     * @param characterData Character data to update
     * @return true if update successful
     */
    bool UpdateCharacter(const CharacterDBData& characterData);
    
    /**
     * Delete character record
     * @param dwSerial Character serial number
     * @return true if deletion successful
     */
    bool DeleteCharacter(uint32_t dwSerial);
    
    /**
     * Load character data
     * @param dwSerial Character serial number
     * @param characterData Output character data
     * @return true if load successful
     */
    bool LoadCharacter(uint32_t dwSerial, CharacterDBData& characterData);
    
    // Quest management operations
    /**
     * Insert quest record
     * @param dwSerial Character serial number
     * @return true if insertion successful
     */
    bool Insert_Quest(uint32_t dwSerial);
    
    /**
     * Update quest progress
     * @param questData Quest data to update
     * @return true if update successful
     */
    bool UpdateQuest(const QuestDBData& questData);
    
    /**
     * Load quest data for character
     * @param dwSerial Character serial number
     * @param questList Output quest list
     * @return true if load successful
     */
    bool LoadQuests(uint32_t dwSerial, std::vector<QuestDBData>& questList);
    
    // Buddy system operations
    /**
     * Insert buddy record
     * @param dwSerial Character serial number
     * @return true if insertion successful
     */
    bool Insert_Buddy(uint32_t dwSerial);
    
    /**
     * Add buddy relationship
     * @param dwSerial Character serial number
     * @param dwBuddySerial Buddy character serial number
     * @return true if addition successful
     */
    bool AddBuddy(uint32_t dwSerial, uint32_t dwBuddySerial);
    
    /**
     * Remove buddy relationship
     * @param dwSerial Character serial number
     * @param dwBuddySerial Buddy character serial number
     * @return true if removal successful
     */
    bool RemoveBuddy(uint32_t dwSerial, uint32_t dwBuddySerial);
    
    // Guild management operations
    /**
     * Create guild
     * @param guildData Guild data to create
     * @return true if creation successful
     */
    bool CreateGuild(const GuildDBData& guildData);
    
    /**
     * Update guild information
     * @param guildData Guild data to update
     * @return true if update successful
     */
    bool UpdateGuild(const GuildDBData& guildData);
    
    /**
     * Delete guild
     * @param dwGuildID Guild ID to delete
     * @return true if deletion successful
     */
    bool DeleteGuild(uint32_t dwGuildID);
    
    // Post system operations
    /**
     * Update post data
     * @param szPostQuery Post query string
     * @return true if update successful
     */
    bool Update_Post(const std::string& szPostQuery);
    
    /**
     * Insert post message
     * @param dwSenderSerial Sender character serial
     * @param dwReceiverSerial Receiver character serial
     * @param szMessage Message content
     * @return true if insertion successful
     */
    bool InsertPost(uint32_t dwSenderSerial, uint32_t dwReceiverSerial, const std::string& szMessage);
    
    // Auto-mining system operations
    /**
     * Create auto-mine table
     * @return true if creation successful
     */
    bool create_automine_table();
    
    /**
     * Create amine personal inventory
     * @return true if creation successful
     */
    bool create_amine_personal();
    
    /**
     * Update auto-mine data
     * @param dwSerial Character serial number
     * @param nMineType Mine type
     * @param nAmount Amount mined
     * @return true if update successful
     */
    bool UpdateAutoMine(uint32_t dwSerial, int nMineType, int nAmount);
    
    // Rebirth system operations
    /**
     * Process character rebirth
     * @param dwCharacterSerial Character serial number
     * @param pwszName Character name
     * @return true if rebirth successful
     */
    bool Rebirth_Base(uint32_t dwCharacterSerial, const std::string& pwszName);
    
    /**
     * Update rebirth statistics
     * @param dwSerial Character serial number
     * @param nRebirthCount Rebirth count
     * @return true if update successful
     */
    bool UpdateRebirthStats(uint32_t dwSerial, int nRebirthCount);
    
    // Boss and special events
    /**
     * Insert boss cry record
     * @param dwSerial Character serial number
     * @return true if insertion successful
     */
    bool Insert_BossCryRecord(uint32_t dwSerial);
    
    /**
     * Update boss kill statistics
     * @param dwSerial Character serial number
     * @param dwBossID Boss ID
     * @param dwKillTime Kill timestamp
     * @return true if update successful
     */
    bool UpdateBossKillStats(uint32_t dwSerial, uint32_t dwBossID, uint64_t dwKillTime);
    
    // Utility operations
    /**
     * Execute world-specific stored procedure
     * @param procedureName Procedure name
     * @param parameters Procedure parameters
     * @return Query result
     */
    DBQueryResult ExecWorldProcedure(const std::string& procedureName, 
                                   const std::vector<std::string>& parameters = {});
    
    /**
     * Backup character data
     * @param dwSerial Character serial number
     * @return true if backup successful
     */
    bool BackupCharacterData(uint32_t dwSerial);
    
    /**
     * Restore character data
     * @param dwSerial Character serial number
     * @param backupTimestamp Backup timestamp
     * @return true if restore successful
     */
    bool RestoreCharacterData(uint32_t dwSerial, uint64_t backupTimestamp);
    
    /**
     * Get world database statistics
     * @return Statistics as formatted string
     */
    std::string GetWorldDBStats() const;

protected:
    // World-specific statistics
    std::atomic<uint64_t> m_nCharacterOperations;
    std::atomic<uint64_t> m_nQuestOperations;
    std::atomic<uint64_t> m_nGuildOperations;
    std::atomic<uint64_t> m_nInventoryOperations;
    
    // Cached data for performance
    std::unordered_map<uint32_t, CharacterDBData> m_characterCache;
    std::unordered_map<uint32_t, std::vector<QuestDBData>> m_questCache;
    mutable std::mutex m_cacheMutex;
    
    /**
     * Build character insert query
     * @param characterData Character data
     * @return SQL query string
     */
    std::string BuildCharacterInsertQuery(const CharacterDBData& characterData) const;
    
    /**
     * Build character update query
     * @param characterData Character data
     * @return SQL query string
     */
    std::string BuildCharacterUpdateQuery(const CharacterDBData& characterData) const;
    
    /**
     * Parse character data from query result
     * @param result Query result
     * @param characterData Output character data
     * @return true if parsing successful
     */
    bool ParseCharacterData(const DBQueryResult& result, CharacterDBData& characterData) const;
    
    /**
     * Update operation statistics
     * @param operation Operation type
     * @param success Whether operation was successful
     */
    void UpdateWorldStats(EWorldDBOperation operation, bool success);
    
    /**
     * Validate character data
     * @param characterData Character data to validate
     * @return true if data is valid
     */
    bool ValidateCharacterData(const CharacterDBData& characterData) const;
    
    /**
     * Sanitize string for SQL query
     * @param input Input string
     * @return Sanitized string
     */
    std::string SanitizeString(const std::string& input) const;
};
