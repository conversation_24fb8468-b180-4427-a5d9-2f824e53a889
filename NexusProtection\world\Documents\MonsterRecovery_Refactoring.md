# MonsterRecovery Refactoring Documentation

## Overview
This document describes the refactoring of monster recovery functionality from decompiled C source to modern C++20 compatible code for Visual Studio 2022.

## Original Files Refactored
The following decompiled source files were analyzed and refactored into the new MonsterRecovery class:

### Core Methods
- `AutoRecoverCMonsterQEAAXXZ_140147440.c` - Manual recovery with effect parameters (Address: 0x140147440)
- `CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.c` - Automatic HP recovery checks (Address: 0x140143370)

## Function Analysis

### AutoRecover Function (0x140147440)
**Original Signature:** `void __usercall CMonster::AutoRecover(CMonster *this@<rcx>, float a2@<xmm0>)`

**Functionality:**
- Performs manual HP recovery with optional bonus amount
- Integrates with effect parameter system for base recovery
- Implements minimum HP threshold (10% of max HP) for negative recovery
- Uses virtual function table for HP getter/setter operations

**Key Features:**
- Stack initialization with debug pattern (-858993460)
- Effect parameter integration (type 32 for HP recovery)
- Floor function for HP calculation precision
- Minimum HP protection mechanism

### CheckAutoRecoverHP Function (0x140143370)
**Original Signature:** `void __fastcall CMonster::CheckAutoRecoverHP(CMonster *this)`

**Functionality:**
- Checks automatic HP recovery conditions
- Validates monster recovery configuration
- Implements timing-based recovery system
- Calls AutoRecover for additional processing

**Key Features:**
- Monster record validation (m_pMonRec fields)
- Timing system using GetLoopTime()
- HP bounds checking
- Recovery delay and unit configuration

## Refactoring Changes

### Modern C++ Features Applied
1. **RAII (Resource Acquisition Is Initialization)**
   - Automatic memory management with smart pointers
   - Exception-safe resource handling

2. **Type Safety**
   - Strong typing with enum classes
   - Const-correctness throughout the API
   - Template-based configuration system

3. **STL Integration**
   - `std::chrono` for time management
   - `std::clamp` for value bounds checking
   - Standard containers for configuration

4. **Exception Safety**
   - Try-catch blocks for error handling
   - RAII for automatic cleanup
   - Graceful degradation on errors

### Architecture Improvements
1. **Separation of Concerns**
   - Recovery logic separated from monster class
   - Configuration management isolated
   - Statistics tracking as separate component

2. **Configurable System**
   - Runtime configuration updates
   - Validation of configuration parameters
   - Default configurations for different monster types

3. **Monitoring and Statistics**
   - Recovery performance tracking
   - Statistical analysis capabilities
   - Debug logging integration

## Class Structure

### MonsterRecovery Class
```cpp
class MonsterRecovery {
public:
    struct RecoveryConfig { /* Configuration parameters */ };
    struct RecoveryStats { /* Statistics tracking */ };
    
    // Core functionality
    void Initialize(CMonster* monster, const RecoveryConfig& config);
    bool CheckAutoRecoverHP(CMonster* monster);
    void AutoRecover(CMonster* monster, float recoveryAmount = 0.0f);
    
    // Configuration management
    void UpdateConfig(const RecoveryConfig& config);
    const RecoveryConfig& GetConfig() const;
    
    // Statistics and monitoring
    const RecoveryStats& GetStats() const;
    void ResetStats();
    bool IsRecoveryActive() const;
};
```

### Utility Namespace
```cpp
namespace MonsterRecoveryUtils {
    RecoveryConfig CreateDefaultConfig(int32_t monsterType);
    bool ValidateConfig(const RecoveryConfig& config);
    float CalculateOptimalRecoveryRate(int32_t maxHP, int32_t level);
    std::string FormatRecoveryStats(const RecoveryStats& stats);
}
```

## Integration with CMonster

The refactored recovery system integrates with the existing CMonster class through:

1. **Method Delegation**
   - `CMonster::CheckAutoRecoverHP()` delegates to `MonsterRecovery::CheckAutoRecoverHP()`
   - `CMonster::AutoRecover()` delegates to `MonsterRecovery::AutoRecover()`

2. **Configuration Management**
   - Monster-specific recovery configurations
   - Runtime configuration updates
   - Validation and error handling

3. **Statistics Integration**
   - Recovery performance monitoring
   - Debug logging and diagnostics
   - Performance optimization data

## Compilation Notes
- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++20 standard for chrono and other features
- Uses modern STL features and exception handling
- Integrates with existing game time systems

## Testing Recommendations
1. Unit tests for recovery calculation logic
2. Integration tests with CMonster class
3. Performance tests for recovery timing
4. Configuration validation tests
5. Statistics accuracy tests
6. Error handling and edge case tests
7. Memory usage and leak detection tests

## Performance Considerations
1. **Timing Optimization**
   - Efficient time calculation using game loop time
   - Minimal overhead for recovery checks
   - Configurable recovery intervals

2. **Memory Management**
   - Stack-based configuration structures
   - Minimal dynamic allocation
   - RAII for automatic cleanup

3. **CPU Efficiency**
   - Early exit conditions for invalid states
   - Cached calculations where possible
   - Optimized mathematical operations

## Backward Compatibility
This refactored version maintains the core functionality of the original decompiled methods while providing enhanced features:

- Same recovery timing behavior
- Compatible HP calculation methods
- Preserved effect parameter integration
- Maintained minimum HP threshold logic

The enhanced functionality provides a solid foundation for monster recovery operations while maintaining compatibility with the existing game systems.
