/*
 * CMonsterAttack.h - Monster Attack System
 * Refactored for Visual Studio 2022 compatibility
 * Original: 0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0.c, AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>

// Forward declarations
class CCharacter;
class CMonster;
class CMonsterSkill;
class CAttack;
class CGameObject;

/**
 * Attack parameter structure for monster attacks
 */
struct _attack_param {
    CCharacter* pDst;               // Target character
    int nPart;                      // Attack part/location
    int nClass;                     // Attack class/type
    int nTol;                       // Tolerance/element type
    int nMinAF;                     // Minimum attack force
    int nMaxAF;                     // Maximum attack force
    int nMinSel;                    // Minimum selection probability
    int nMaxSel;                    // Maximum selection probability
    int nExtentRange;               // Attack extent range
    int nShotNum;                   // Number of shots
    int nAddAttPnt;                 // Additional attack points
    void* pFld;                     // Field data pointer
    uint8_t byEffectCode;           // Effect code
    int nLevel;                     // Attack level
    int nMastery;                   // Mastery level
    std::array<float, 3> fArea;     // Attack area coordinates
    int nMaxAttackPnt;              // Maximum attack points
    bool bPassCount;                // Pass count flag
    int nAttactType;                // Attack type
    
    _attack_param();
    ~_attack_param() = default;
    
    void Reset();
    bool IsValid() const;
};

/**
 * Damage list entry for tracking damaged characters
 */
struct _be_damaged_char {
    CCharacter* m_pChar;            // Damaged character
    int m_nDamage;                  // Damage amount
    bool m_bCritical;               // Critical hit flag
    
    _be_damaged_char();
    _be_damaged_char(CCharacter* pChar, int damage, bool critical = false);
    ~_be_damaged_char() = default;
    
    void Reset();
    bool IsValid() const;
};

/**
 * Monster attack result enumeration
 */
enum class MonsterAttackResult : int32_t {
    Success = 1,                    // Attack successful
    Failed = 0,                     // Attack failed
    InvalidTarget = -1,             // Invalid target
    InvalidSkill = -2,              // Invalid skill
    NotAttackable = -3,             // Target not attackable
    OutOfRange = -4,                // Target out of range
    InsufficientResources = -5,     // Insufficient resources
    Stunned = -6                    // Monster is stunned
};

/**
 * Monster attack type enumeration
 */
enum class MonsterAttackType : int32_t {
    General = 0,                    // General attack
    Skill = 1,                      // Skill attack type 1
    AreaSkill = 2,                  // Area skill attack type 2
    Force = 3                       // Force attack
};

/**
 * Monster attack statistics
 */
struct MonsterAttackStatistics {
    uint32_t totalAttacks;          // Total attacks performed
    uint32_t successfulAttacks;     // Successful attacks
    uint32_t failedAttacks;         // Failed attacks
    uint32_t criticalHits;          // Critical hits
    uint32_t skillAttacks;          // Skill-based attacks
    uint32_t generalAttacks;        // General attacks
    uint32_t totalDamageDealt;      // Total damage dealt
    
    MonsterAttackStatistics();
    void Reset();
    float GetSuccessRate() const;
    float GetCriticalRate() const;
};

/**
 * CAttack base class for attack functionality
 */
class CAttack {
public:
    // Constants
    static constexpr int MAX_DAMAGE_LIST = 30;
    static constexpr int DEFAULT_EXTENT_RANGE = 20;
    
    /**
     * Constructor
     * @param pAttacker Attacking character
     */
    CAttack(CCharacter* pAttacker);
    
    /**
     * Destructor
     */
    virtual ~CAttack();
    
    /**
     * Perform general attack
     * @param pParam Attack parameters
     * @param bMustMiss Force miss flag
     * @param bUseEffBullet Use effect bullet flag
     * @return true if attack successful
     */
    virtual bool AttackGen(_attack_param* pParam, bool bMustMiss = false, bool bUseEffBullet = false);
    
    /**
     * Perform force attack
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @return true if attack successful
     */
    virtual bool AttackForce(_attack_param* pParam, bool bUseEffBullet = false);
    
    /**
     * Get damage list
     * @return Reference to damage list
     */
    const std::vector<_be_damaged_char>& GetDamageList() const { return m_DamList; }
    
    /**
     * Get number of damaged objects
     * @return Number of damaged objects
     */
    int GetDamagedObjectCount() const { return m_nDamagedObjNum; }
    
    /**
     * Check if attack was critical
     * @return true if critical attack
     */
    bool IsCriticalAttack() const { return m_bIsCrtAtt; }
    
    /**
     * Check if attack failed
     * @return true if attack failed
     */
    bool IsFailure() const { return m_bFailure; }

public:
    // Member variables (public for compatibility)
    std::vector<_be_damaged_char> m_DamList;    // Damage list
    CCharacter* m_pAttChar;                     // Attacking character
    _attack_param* m_pp;                        // Attack parameters pointer
    int m_nDamagedObjNum;                       // Number of damaged objects
    bool m_bIsCrtAtt;                           // Critical attack flag
    bool m_bFailure;                            // Failure flag
    
    // Static default parameters
    static _attack_param s_DefParam;

protected:
    /**
     * Calculate damage for target
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return Calculated damage
     */
    virtual int CalculateDamage(CCharacter* pTarget, _attack_param* pParam);
    
    /**
     * Apply damage to target
     * @param pTarget Target character
     * @param damage Damage amount
     * @param bCritical Critical hit flag
     * @return true if damage applied successfully
     */
    virtual bool ApplyDamage(CCharacter* pTarget, int damage, bool bCritical = false);
    
    /**
     * Check if target is valid for attack
     * @param pTarget Target character
     * @return true if target is valid
     */
    virtual bool IsValidTarget(CCharacter* pTarget) const;
    
    /**
     * Reset attack state
     */
    virtual void Reset();
};

/**
 * CMonsterAttack class for monster-specific attack functionality
 */
class CMonsterAttack : public CAttack {
public:
    /**
     * Constructor
     * @param pMonster Attacking monster
     */
    CMonsterAttack(CCharacter* pMonster);
    
    /**
     * Destructor
     */
    ~CMonsterAttack() override;
    
    /**
     * Perform monster general attack
     * @param pParam Attack parameters
     * @param bMustMiss Force miss flag
     * @return true if attack successful
     */
    bool AttackMonsterGen(_attack_param* pParam, bool bMustMiss = false);
    
    /**
     * Perform monster skill attack
     * @param pParam Attack parameters
     * @return true if attack successful
     */
    bool AttackMonsterSkill(_attack_param* pParam);
    
    /**
     * Perform monster force attack
     * @param pParam Attack parameters
     * @return true if attack successful
     */
    bool AttackMonsterForce(_attack_param* pParam);

    /**
     * Process area damage for multiple targets
     * @param nLimitRadius Maximum damage radius
     * @param nAttPower Base attack power
     * @param pTar Target area coordinates
     * @param nEffAttPower Effective attack power
     * @param bUseEffBullet Use effect bullet flag
     */
    void AreaDamageProc(int nLimitRadius, int nAttPower, float* pTar, int nEffAttPower, bool bUseEffBullet = false);

    /**
     * Process flash damage in a directional cone
     * @param nLimDist Maximum damage distance
     * @param nAttPower Base attack power
     * @param nAngle Attack angle in degrees
     * @param nEffAttPower Effective attack power
     * @param bUseEffBullet Use effect bullet flag
     */
    void FlashDamageProc(int nLimDist, int nAttPower, int nAngle, int nEffAttPower, bool bUseEffBullet = false);

    /**
     * Process sector damage in a specific area
     * @param nLimitRadius Maximum damage radius
     * @param nAttPower Base attack power
     * @param nAngle Attack angle
     * @param nEffAttPower Effective attack power
     * @param nStartAngle Starting angle
     * @param nEndAngle Ending angle
     * @param bUseEffBullet Use effect bullet flag
     */
    void SectorDamageProc(int nLimitRadius, int nAttPower, int nAngle, int nEffAttPower, int nStartAngle, int nEndAngle, bool bUseEffBullet = false);
    
    /**
     * Get attacking monster
     * @return Pointer to attacking monster
     */
    CMonster* GetAttackingMonster() const { return m_pAttMonster; }
    
    /**
     * Set attacking monster
     * @param pMonster Attacking monster
     */
    void SetAttackingMonster(CMonster* pMonster) { m_pAttMonster = pMonster; }

public:
    // Member variables
    CMonster* m_pAttMonster;                    // Attacking monster

protected:
    /**
     * Calculate monster-specific damage
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return Calculated damage
     */
    int CalculateDamage(CCharacter* pTarget, _attack_param* pParam) override;
    
    /**
     * Apply monster-specific effects
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return true if effects applied successfully
     */
    bool ApplyMonsterEffects(CCharacter* pTarget, _attack_param* pParam);
    
    /**
     * Check monster attack conditions
     * @param pParam Attack parameters
     * @return true if conditions are met
     */
    bool CheckAttackConditions(_attack_param* pParam) const;
};

/**
 * Monster attack utility functions
 */
namespace MonsterAttackUtils {
    /**
     * Convert MonsterAttackResult to string
     * @param result Attack result
     * @return String representation
     */
    std::string AttackResultToString(MonsterAttackResult result);
    
    /**
     * Convert MonsterAttackType to string
     * @param type Attack type
     * @return String representation
     */
    std::string AttackTypeToString(MonsterAttackType type);
    
    /**
     * Validate attack parameters
     * @param pParam Attack parameters
     * @return true if parameters are valid
     */
    bool ValidateAttackParameters(_attack_param* pParam);
    
    /**
     * Calculate attack range
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @return Distance between attacker and target
     */
    float CalculateAttackRange(CCharacter* pAttacker, CCharacter* pTarget);
    
    /**
     * Check if target is in attack range
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param maxRange Maximum attack range
     * @return true if target is in range
     */
    bool IsTargetInRange(CCharacter* pAttacker, CCharacter* pTarget, float maxRange);
    
    /**
     * Generate random attack part
     * @return Random attack part value
     */
    int GenerateRandomAttackPart();
    
    /**
     * Calculate critical hit probability
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @return Critical hit probability (0.0 to 1.0)
     */
    float CalculateCriticalProbability(CCharacter* pAttacker, CCharacter* pTarget);
}

// External function declarations (to be properly linked)
extern uint32_t GetLoopTime();
extern void memcpy_0(void* dest, const void* src, size_t size);
extern float ffloor(float value);

#endif // CMONSTERATTACK_H
