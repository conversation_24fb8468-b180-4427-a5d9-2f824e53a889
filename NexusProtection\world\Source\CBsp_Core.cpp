/*
 * CBsp_Core.cpp - Core BSP Implementation
 * Core functionality and utility methods for CBsp
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CBsp.h"
#include "../Headers/CEntity.h"
#include "../Headers/CParticle.h"
#include "../Headers/CMergeFileManager.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <sstream>
#include <iomanip>

/**
 * Initialize BSP system
 */
bool CBsp::Initialize() {
    try {
        LogDebug("CBsp::Initialize - Initializing BSP system");
        
        if (m_initialized) {
            LogDebug("Initialize - BSP system already initialized");
            return true;
        }
        
        // Initialize member variables
        mEntity = nullptr;
        mParticle = nullptr;
        mEntityList = nullptr;
        mMapEntitiesList = nullptr;
        mEntityCache = nullptr;
        mEntityListNum = 0;
        mMapEntitiesListNum = 0;
        mTotalAllocSize = 0;
        
        // Reset statistics
        m_statistics.Reset();
        
        m_initialized = true;
        LogDebug("Initialize - BSP system initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::Initialize - Exception: %s", e.what());
        return false;
    }
}

/**
 * Cleanup BSP system
 */
void CBsp::Cleanup() {
    try {
        LogDebug("CBsp::Cleanup - Cleaning up BSP system");
        
        // Cleanup map entities with particles
        if (mMapEntitiesList) {
            for (uint32_t i = 0; i < mMapEntitiesListNum; ++i) {
                if (mMapEntitiesList[i].Particle) {
                    delete mMapEntitiesList[i].Particle;
                    mMapEntitiesList[i].Particle = nullptr;
                }
            }
        }
        
        // Note: mEntity and mParticle are allocated with Dmalloc,
        // so they should be freed with corresponding Dfree function
        // For now, we just set them to nullptr
        mEntity = nullptr;
        mParticle = nullptr;
        mEntityList = nullptr;
        mMapEntitiesList = nullptr;
        mEntityCache = nullptr;
        
        mEntityListNum = 0;
        mMapEntitiesListNum = 0;
        mTotalAllocSize = 0;
        
        m_statistics.Reset();
        m_initialized = false;
        
        LogDebug("Cleanup - BSP system cleaned up successfully");
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::Cleanup - Exception: %s", e.what());
    }
}

/**
 * Frame move for map entities
 */
bool CBsp::FrameMoveMapEntities() {
    try {
        if (!m_initialized || !mMapEntitiesList) {
            return false;
        }
        
        // Process frame movement for all map entities
        for (uint32_t i = 0; i < mMapEntitiesListNum; ++i) {
            _MAP_ENTITIES_LIST* pMapEntity = &mMapEntitiesList[i];
            
            // Update particle if present
            if (pMapEntity->Particle) {
                // Update particle frame
                CParticle::FrameMove(pMapEntity->Particle);
            }
            
            // Update entity frame if present
            if (pMapEntity->ID < mEntityListNum && mEntityList[pMapEntity->ID].IsFileExist) {
                if (!mEntityList[pMapEntity->ID].IsParticle) {
                    // Update entity frame
                    CEntity::FrameMove(&mEntity[pMapEntity->ID]);
                }
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::FrameMoveMapEntities - Exception: %s", e.what());
        return false;
    }
}

/**
 * Draw map entities for rendering
 */
bool CBsp::DrawMapEntitiesRender() {
    try {
        if (!m_initialized || !mMapEntitiesList) {
            return false;
        }
        
        // Render all map entities
        for (uint32_t i = 0; i < mMapEntitiesListNum; ++i) {
            _MAP_ENTITIES_LIST* pMapEntity = &mMapEntitiesList[i];
            
            // Render particle if present
            if (pMapEntity->Particle) {
                CParticle::Render(pMapEntity->Particle);
            }
            
            // Render entity if present
            if (pMapEntity->ID < mEntityListNum && mEntityList[pMapEntity->ID].IsFileExist) {
                if (!mEntityList[pMapEntity->ID].IsParticle) {
                    CEntity::Render(&mEntity[pMapEntity->ID]);
                }
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::DrawMapEntitiesRender - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get entity by index
 */
CEntity* CBsp::GetEntity(uint32_t index) const {
    if (index >= mEntityListNum || !mEntity) {
        return nullptr;
    }
    
    return &mEntity[index];
}

/**
 * Get particle by index
 */
CParticle* CBsp::GetParticle(uint32_t index) const {
    if (index >= mEntityListNum || !mParticle) {
        return nullptr;
    }
    
    return &mParticle[index];
}

/**
 * Get map entity by index
 */
_MAP_ENTITIES_LIST* CBsp::GetMapEntity(uint32_t index) const {
    if (index >= mMapEntitiesListNum || !mMapEntitiesList) {
        return nullptr;
    }
    
    return &mMapEntitiesList[index];
}

// BSP Utility Functions Implementation
namespace BSPUtils {

/**
 * Convert BSPLoadResult to string
 */
std::string LoadResultToString(BSPLoadResult result) {
    switch (result) {
        case BSPLoadResult::Success: return "Success";
        case BSPLoadResult::InvalidParameters: return "InvalidParameters";
        case BSPLoadResult::MemoryAllocationFailed: return "MemoryAllocationFailed";
        case BSPLoadResult::FileNotFound: return "FileNotFound";
        case BSPLoadResult::LoadingFailed: return "LoadingFailed";
        case BSPLoadResult::ParticleLoadFailed: return "ParticleLoadFailed";
        default: return "Unknown";
    }
}

/**
 * Validate entity name
 */
bool IsValidEntityName(const char* entityName) {
    if (!entityName) {
        return false;
    }
    
    size_t len = strlen(entityName);
    if (len == 0 || len >= CBsp::MAX_ENTITY_NAME_LENGTH) {
        return false;
    }
    
    // Check for valid characters (basic validation)
    for (size_t i = 0; i < len; ++i) {
        char c = entityName[i];
        if (c < 32 || c > 126) { // Printable ASCII range
            return false;
        }
    }
    
    return true;
}

/**
 * Get entity file extension
 */
std::string GetEntityExtension(const char* entityName) {
    if (!entityName) {
        return "";
    }
    
    std::string name(entityName);
    size_t dotPos = name.find_last_of('.');
    if (dotPos == std::string::npos) {
        return "";
    }
    
    return name.substr(dotPos);
}

/**
 * Format entity path for logging
 */
std::string FormatEntityPath(const std::string& entityPath) {
    if (entityPath.length() > 50) {
        return "..." + entityPath.substr(entityPath.length() - 47);
    }
    return entityPath;
}

} // namespace BSPUtils

// Additional utility methods for CBsp class

/**
 * Get entity statistics summary
 */
std::string CBsp::GetStatisticsSummary() const {
    std::ostringstream oss;
    oss << "BSP Statistics: ";
    oss << "Total=" << m_statistics.totalEntities;
    oss << " Loaded=" << m_statistics.loadedEntities;
    oss << " Failed=" << m_statistics.failedEntities;
    oss << " Particles=" << m_statistics.particleEntities;
    oss << " Regular=" << m_statistics.regularEntities;
    oss << " Memory=" << m_statistics.memoryAllocated << "B";
    
    return oss.str();
}

/**
 * Validate BSP system state
 */
bool CBsp::ValidateSystemState() const {
    if (!m_initialized) {
        Logger::Error("ValidateSystemState - BSP system not initialized");
        return false;
    }
    
    if (mEntityListNum > 0) {
        if (!mEntity) {
            Logger::Error("ValidateSystemState - Entity array is null but count is non-zero");
            return false;
        }
        
        if (!mParticle) {
            Logger::Error("ValidateSystemState - Particle array is null but count is non-zero");
            return false;
        }
        
        if (!mEntityList) {
            Logger::Error("ValidateSystemState - Entity list is null but count is non-zero");
            return false;
        }
    }
    
    if (mMapEntitiesListNum > 0 && !mMapEntitiesList) {
        Logger::Error("ValidateSystemState - Map entities list is null but count is non-zero");
        return false;
    }
    
    return true;
}

/**
 * Get memory usage information
 */
uint32_t CBsp::GetMemoryUsage() const {
    uint32_t totalMemory = 0;
    
    // Entity memory
    totalMemory += ENTITY_SIZE * mEntityListNum;
    
    // Particle memory
    totalMemory += PARTICLE_SIZE * mEntityListNum;
    
    // Map entities memory
    totalMemory += sizeof(_MAP_ENTITIES_LIST) * mMapEntitiesListNum;
    
    // Entity list memory
    totalMemory += sizeof(_ENTITY_LIST) * mEntityListNum;
    
    return totalMemory;
}

/**
 * Reset BSP system
 */
bool CBsp::Reset() {
    try {
        LogDebug("CBsp::Reset - Resetting BSP system");
        
        Cleanup();
        return Initialize();
        
    } catch (const std::exception& e) {
        Logger::Error("CBsp::Reset - Exception: %s", e.what());
        return false;
    }
}
