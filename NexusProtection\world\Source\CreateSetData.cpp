/**
 * @file CreateSetData.cpp
 * @brief Implementation of Create SetData Structures
 * @details Implements utility functions and constructors for creation data structures
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "../Headers/CreateSetData.h"
#include <stdexcept>
#include <algorithm>
#include <cstring>

// Forward declarations for legacy compatibility
extern "C" {
    uint32_t timeGetTime();  // Windows API function
}

namespace NexusProtection {
namespace World {

namespace CreateSetDataUtils {

MonsterCreateSetData CreateDefaultMonsterData(
    const std::string& monsterCode,
    CMapData* pMap,
    float x, float y, float z
) {
    MonsterCreateSetData data;
    
    // Set basic object data
    data.m_pMap = pMap;
    data.SetPosition(x, y, z);
    data.m_nLayerIndex = 0;
    data.m_dwFlags = 0;
    
    // Set character data
    data.m_dwCharacterType = 1; // Monster type
    data.m_dwCharacterFlags = 0;
    data.m_fRotation = 0.0f;
    data.m_dwLevel = 1;
    
    // Set monster-specific data
    data.m_strMonsterCode = monsterCode;
    data.pActiveRec = nullptr;
    data.pDumPosition = nullptr;
    data.bDungeon = false;
    data.pParent = nullptr;
    data.bRobExp = true;
    data.bRewardExp = true;
    data.bStdItemLoot = true;
    data.m_dwMonsterSerial = 0; // Will be assigned during creation
    
    return data;
}

NPCCreateSetData CreateDefaultNPCData(
    const std::string& npcName,
    uint8_t raceCode,
    CMapData* pMap,
    float x, float y, float z
) {
    NPCCreateSetData data;
    
    // Set basic object data
    data.m_pMap = pMap;
    data.m_fStartPos[0] = x;
    data.m_fStartPos[1] = y;
    data.m_fStartPos[2] = z;
    data.m_nLayerIndex = 0;
    data.m_dwFlags = 0;
    
    // Set character data
    data.m_dwCharacterType = 2; // NPC type
    data.m_dwCharacterFlags = 0;
    data.m_fRotation = 0.0f;
    data.m_dwLevel = 1;
    
    // Set NPC-specific data
    data.SetName(npcName);
    data.SetRaceCode(raceCode);
    data.m_pLinkItemStore = nullptr;
    data.m_dwNPCFlags = 0;
    data.m_dwNPCType = 0;
    
    return data;
}

} // namespace CreateSetDataUtils

} // namespace World
} // namespace NexusProtection

// Legacy C-style constructor implementations for backward compatibility
extern "C" {

/**
 * @brief Legacy constructor for _object_create_setdata
 * @param pThis Pointer to the structure to initialize
 * @note Maintains compatibility with decompiled code patterns
 */
void _object_create_setdata_constructor(void* pThis) {
    if (!pThis) return;
    
    // Initialize with debug pattern (equivalent to decompiled -858993460 pattern)
    std::memset(pThis, 0xCC, sizeof(NexusProtection::World::ObjectCreateSetData));
    
    // Properly construct the object
    new(pThis) NexusProtection::World::ObjectCreateSetData();
}

/**
 * @brief Legacy constructor for _character_create_setdata
 * @param pThis Pointer to the structure to initialize
 * @note Maintains compatibility with decompiled code patterns
 */
void _character_create_setdata_constructor(void* pThis) {
    if (!pThis) return;
    
    // Initialize with debug pattern
    std::memset(pThis, 0xCC, sizeof(NexusProtection::World::CharacterCreateSetData));
    
    // Call base constructor first (equivalent to decompiled pattern)
    _object_create_setdata_constructor(pThis);
    
    // Properly construct the character data
    new(pThis) NexusProtection::World::CharacterCreateSetData();
}

/**
 * @brief Legacy constructor for _monster_create_setdata
 * @param pThis Pointer to the structure to initialize
 * @note Maintains compatibility with decompiled code patterns
 */
void _monster_create_setdata_constructor(void* pThis) {
    if (!pThis) return;
    
    // Initialize with debug pattern (8 iterations of 4 bytes = 32 bytes)
    std::memset(pThis, 0xCC, sizeof(NexusProtection::World::MonsterCreateSetData));
    
    // Call base constructor first
    _character_create_setdata_constructor(pThis);
    
    // Properly construct the monster data
    auto* pMonsterData = new(pThis) NexusProtection::World::MonsterCreateSetData();
    
    // Set specific values as per decompiled code
    pMonsterData->pActiveRec = nullptr;      // v4->pActiveRec = 0i64;
    pMonsterData->pDumPosition = nullptr;    // v4->pDumPosition = 0i64;
    pMonsterData->bDungeon = false;          // v4->bDungeon = 0;
    pMonsterData->pParent = nullptr;         // v4->pParent = 0i64;
    pMonsterData->bRobExp = true;            // v4->bRobExp = 1;
    pMonsterData->bRewardExp = true;         // v4->bRewardExp = 1;
}

/**
 * @brief Legacy constructor for _npc_create_setdata
 * @param pThis Pointer to the structure to initialize
 * @note Maintains compatibility with decompiled code patterns
 */
void _npc_create_setdata_constructor(void* pThis) {
    if (!pThis) return;
    
    // Initialize with debug pattern (8 iterations of 4 bytes = 32 bytes)
    std::memset(pThis, 0xCC, sizeof(NexusProtection::World::NPCCreateSetData));
    
    // Call base constructor first
    _character_create_setdata_constructor(pThis);
    
    // Properly construct the NPC data
    auto* pNPCData = new(pThis) NexusProtection::World::NPCCreateSetData();
    
    // Set specific values as per decompiled code
    pNPCData->m_pLinkItemStore = nullptr;    // v4->m_pLinkItemStore = 0i64;
    pNPCData->m_byRaceCode = 0xFF;           // v4->m_byRaceCode = -1; (as unsigned)
}

} // extern "C"

// C++ style constructors that match the decompiled function signatures
namespace NexusProtection {
namespace World {

// These functions provide the exact same interface as the decompiled code
// but use modern C++ internally

/**
 * @brief Constructor function matching decompiled signature
 * @param pThis Pointer to _object_create_setdata structure
 */
void __fastcall object_create_setdata_ctor(ObjectCreateSetData* pThis) {
    _object_create_setdata_constructor(pThis);
}

/**
 * @brief Constructor function matching decompiled signature
 * @param pThis Pointer to _character_create_setdata structure
 */
void __fastcall character_create_setdata_ctor(CharacterCreateSetData* pThis) {
    _character_create_setdata_constructor(pThis);
}

/**
 * @brief Constructor function matching decompiled signature
 * @param pThis Pointer to _monster_create_setdata structure
 */
void __fastcall monster_create_setdata_ctor(MonsterCreateSetData* pThis) {
    _monster_create_setdata_constructor(pThis);
}

/**
 * @brief Constructor function matching decompiled signature
 * @param pThis Pointer to _npc_create_setdata structure
 */
void __fastcall npc_create_setdata_ctor(NPCCreateSetData* pThis) {
    _npc_create_setdata_constructor(pThis);
}

} // namespace World
} // namespace NexusProtection

// Global utility functions for validation and debugging

namespace {

/**
 * @brief Validates memory pattern for debugging
 * @param pData Pointer to data to check
 * @param size Size of data to check
 * @return true if memory pattern is valid
 */
bool ValidateMemoryPattern(const void* pData, std::size_t size) {
    if (!pData || size == 0) return false;
    
    // Check for common corruption patterns
    const uint8_t* bytes = static_cast<const uint8_t*>(pData);
    
    // Check for all zeros (uninitialized)
    bool allZeros = true;
    for (std::size_t i = 0; i < size; ++i) {
        if (bytes[i] != 0) {
            allZeros = false;
            break;
        }
    }
    
    // Check for debug pattern (0xCC)
    bool allDebug = true;
    for (std::size_t i = 0; i < size; ++i) {
        if (bytes[i] != 0xCC) {
            allDebug = false;
            break;
        }
    }
    
    // Valid if not all zeros or all debug pattern
    return !allZeros && !allDebug;
}

} // anonymous namespace

/**
 * @brief Global validation function for any create setdata structure
 * @param pData Pointer to the structure
 * @param structSize Size of the structure
 * @return true if the structure appears valid
 */
extern "C" bool ValidateCreateSetDataStructure(const void* pData, std::size_t structSize) {
    if (!pData || structSize == 0) return false;
    
    // Basic memory validation
    if (!ValidateMemoryPattern(pData, structSize)) return false;
    
    // Try to cast to base type and validate
    try {
        const auto* baseData = static_cast<const NexusProtection::World::ObjectCreateSetData*>(pData);
        return baseData->IsValid();
    }
    catch (...) {
        return false;
    }
}

/**
 * @brief Debug function to print create setdata information
 * @param pData Pointer to the structure
 * @param structType Type identifier string
 */
extern "C" void DebugPrintCreateSetData(const void* pData, const char* structType) {
    if (!pData || !structType) return;
    
    try {
        const auto* baseData = static_cast<const NexusProtection::World::ObjectCreateSetData*>(pData);
        
        // Print basic information (would use proper logging in real implementation)
        // This is a placeholder for debugging functionality
        
    }
    catch (...) {
        // Handle any exceptions during debug printing
    }
}
