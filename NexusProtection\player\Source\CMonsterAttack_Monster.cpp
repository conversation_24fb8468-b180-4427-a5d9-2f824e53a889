/*
 * CMonsterAttack_Monster.cpp - Monster-Specific Attack Implementation
 * Refactored from AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CMonsterAttack.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CMonster.h"
#include "../../world/Headers/CMonsterSkill.h"
#include "../../world/Headers/CMonsterSkillPool.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>
#include <random>

// External dependencies
extern uint32_t GetLoopTime();
extern void memcpy_0(void* dest, const void* src, size_t size);
extern float ffloor(float value);

/**
 * CMonsterAttack constructor
 * Refactored from original CMonsterAttack::CMonsterAttack
 */
CMonsterAttack::CMonsterAttack(CCharacter* pMonster) 
    : CAttack(pMonster)
    , m_pAttMonster(static_cast<CMonster*>(pMonster)) {
    
    try {
        Logger::Debug("CMonsterAttack::CMonsterAttack - Monster attack system initialized for monster %p", pMonster);
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::CMonsterAttack - Exception during initialization: %s", e.what());
        throw;
    }
}

/**
 * CMonsterAttack destructor
 */
CMonsterAttack::~CMonsterAttack() {
    try {
        m_pAttMonster = nullptr;
        Logger::Debug("CMonsterAttack::~CMonsterAttack - Monster attack system destroyed");
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::~CMonsterAttack - Exception during destruction: %s", e.what());
    }
}

/**
 * Perform monster general attack
 * Refactored from original AttackMonsterGen implementation
 */
bool CMonsterAttack::AttackMonsterGen(_attack_param* pParam, bool bMustMiss) {
    try {
        if (!pParam || !m_pAttMonster) {
            Logger::Error("AttackMonsterGen - Invalid parameters");
            m_bFailure = true;
            return false;
        }
        
        if (!CheckAttackConditions(pParam)) {
            Logger::Debug("AttackMonsterGen - Attack conditions not met");
            m_bFailure = true;
            return false;
        }
        
        // Reset attack state
        Reset();
        m_pp = pParam;
        
        // Check for apparition (forced miss equivalent to original m_bApparition check)
        if (bMustMiss || m_pAttMonster->IsApparition()) {
            m_bFailure = true;
            Logger::Debug("AttackMonsterGen - Attack forced to miss (apparition or forced miss)");
            return false;
        }
        
        // Calculate damage using monster-specific logic
        int damage = CalculateDamage(pParam->pDst, pParam);
        
        // Apply monster-specific effects
        if (!ApplyMonsterEffects(pParam->pDst, pParam)) {
            Logger::Warning("AttackMonsterGen - Failed to apply monster effects");
        }
        
        // Determine critical hit based on monster stats
        float criticalProb = MonsterAttackUtils::CalculateCriticalProbability(m_pAttChar, pParam->pDst);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        m_bIsCrtAtt = (dis(gen) < criticalProb);
        
        // Apply critical multiplier
        if (m_bIsCrtAtt && damage > 0) {
            damage = static_cast<int>(damage * 1.5f); // 50% critical bonus
        }
        
        // Apply damage
        if (damage > 0 && ApplyDamage(pParam->pDst, damage, m_bIsCrtAtt)) {
            m_bFailure = false;
            Logger::Debug("AttackMonsterGen - Attack successful, damage: %d, critical: %s", 
                         damage, m_bIsCrtAtt ? "Yes" : "No");
            return true;
        } else {
            m_bFailure = true;
            Logger::Debug("AttackMonsterGen - Attack failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::AttackMonsterGen - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Perform monster skill attack
 */
bool CMonsterAttack::AttackMonsterSkill(_attack_param* pParam) {
    try {
        if (!pParam || !m_pAttMonster) {
            Logger::Error("AttackMonsterSkill - Invalid parameters");
            m_bFailure = true;
            return false;
        }
        
        if (!CheckAttackConditions(pParam)) {
            Logger::Debug("AttackMonsterSkill - Attack conditions not met");
            m_bFailure = true;
            return false;
        }
        
        // Reset attack state
        Reset();
        m_pp = pParam;
        
        // Skill attacks have enhanced damage and effects
        int damage = CalculateDamage(pParam->pDst, pParam);
        
        // Apply skill-specific multiplier
        damage = static_cast<int>(damage * 1.2f); // 20% skill bonus
        
        // Apply monster-specific effects
        if (!ApplyMonsterEffects(pParam->pDst, pParam)) {
            Logger::Warning("AttackMonsterSkill - Failed to apply monster effects");
        }
        
        // Skill attacks have higher critical chance
        float criticalProb = MonsterAttackUtils::CalculateCriticalProbability(m_pAttChar, pParam->pDst) * 1.3f;
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        m_bIsCrtAtt = (dis(gen) < criticalProb);
        
        // Apply critical multiplier
        if (m_bIsCrtAtt && damage > 0) {
            damage = static_cast<int>(damage * 1.8f); // 80% critical bonus for skills
        }
        
        // Apply damage
        if (damage > 0 && ApplyDamage(pParam->pDst, damage, m_bIsCrtAtt)) {
            m_bFailure = false;
            Logger::Debug("AttackMonsterSkill - Skill attack successful, damage: %d, critical: %s", 
                         damage, m_bIsCrtAtt ? "Yes" : "No");
            return true;
        } else {
            m_bFailure = true;
            Logger::Debug("AttackMonsterSkill - Skill attack failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::AttackMonsterSkill - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Perform monster force attack
 */
bool CMonsterAttack::AttackMonsterForce(_attack_param* pParam) {
    try {
        if (!pParam || !m_pAttMonster) {
            Logger::Error("AttackMonsterForce - Invalid parameters");
            m_bFailure = true;
            return false;
        }
        
        // Force attacks bypass most conditions
        Reset();
        m_pp = pParam;
        
        // Force attacks always hit with maximum damage
        int damage = pParam->nMaxAF;
        m_bIsCrtAtt = true; // Force attacks are always critical
        
        // Apply monster-specific effects
        if (!ApplyMonsterEffects(pParam->pDst, pParam)) {
            Logger::Warning("AttackMonsterForce - Failed to apply monster effects");
        }
        
        // Apply damage
        if (ApplyDamage(pParam->pDst, damage, m_bIsCrtAtt)) {
            m_bFailure = false;
            Logger::Debug("AttackMonsterForce - Force attack successful, damage: %d", damage);
            return true;
        } else {
            m_bFailure = true;
            Logger::Debug("AttackMonsterForce - Force attack failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::AttackMonsterForce - Exception: %s", e.what());
        m_bFailure = true;
        return false;
    }
}

/**
 * Calculate monster-specific damage
 */
int CMonsterAttack::CalculateDamage(CCharacter* pTarget, _attack_param* pParam) {
    if (!pTarget || !pParam || !m_pAttMonster) {
        return 0;
    }
    
    // Start with base damage calculation
    int baseDamage = CAttack::CalculateDamage(pTarget, pParam);
    
    // Apply monster-specific modifiers
    if (m_pAttMonster->GetMonsterRecord()) {
        // Apply monster level modifier
        int monsterLevel = m_pAttMonster->GetLevel();
        float levelMod = 1.0f + (monsterLevel * 0.02f); // 2% per level
        baseDamage = static_cast<int>(baseDamage * levelMod);
        
        // Apply monster type modifiers
        // This would depend on monster type, attack type, etc.
        // For now, we'll apply a basic modifier based on attack range type
        if (m_pAttMonster->GetMonsterRecord()->m_bAttRangeType > 2) {
            baseDamage = static_cast<int>(baseDamage * 1.1f); // 10% bonus for special attack types
        }
    }
    
    return std::max(0, baseDamage);
}

/**
 * Apply monster-specific effects
 */
bool CMonsterAttack::ApplyMonsterEffects(CCharacter* pTarget, _attack_param* pParam) {
    try {
        if (!pTarget || !pParam || !m_pAttMonster) {
            return false;
        }
        
        // Apply status effects based on monster type and attack parameters
        if (pParam->byEffectCode > 0) {
            // Apply special effects based on effect code
            Logger::Debug("ApplyMonsterEffects - Applying effect code %d to target %p", 
                         pParam->byEffectCode, pTarget);
            
            // This would involve calling target's status effect system
            // For now, we'll just log the effect application
        }
        
        // Apply elemental effects based on tolerance (nTol)
        if (pParam->nTol >= 0) {
            Logger::Debug("ApplyMonsterEffects - Applying elemental effect %d to target %p", 
                         pParam->nTol, pTarget);
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CMonsterAttack::ApplyMonsterEffects - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check monster attack conditions
 */
bool CMonsterAttack::CheckAttackConditions(_attack_param* pParam) const {
    if (!pParam || !m_pAttMonster) {
        return false;
    }
    
    // Check if monster is alive
    if (!m_pAttMonster->IsAlive()) {
        Logger::Debug("CheckAttackConditions - Monster is not alive");
        return false;
    }
    
    // Check if target is valid
    if (!IsValidTarget(pParam->pDst)) {
        Logger::Debug("CheckAttackConditions - Invalid target");
        return false;
    }
    
    // Check if monster is in combat state
    if (!m_pAttMonster->IsInCombat()) {
        Logger::Debug("CheckAttackConditions - Monster is not in combat");
        return false;
    }
    
    // Check attack range
    if (!MonsterAttackUtils::IsTargetInRange(m_pAttMonster, pParam->pDst, 
                                           m_pAttMonster->GetAttackRange())) {
        Logger::Debug("CheckAttackConditions - Target out of range");
        return false;
    }
    
    // Check if monster is stunned or disabled
    if (m_pAttMonster->IsStunned()) {
        Logger::Debug("CheckAttackConditions - Monster is stunned");
        return false;
    }
    
    return true;
}

// Monster Attack Utility Functions Implementation
namespace MonsterAttackUtils {

/**
 * Convert MonsterAttackResult to string
 */
std::string AttackResultToString(MonsterAttackResult result) {
    switch (result) {
        case MonsterAttackResult::Success: return "Success";
        case MonsterAttackResult::Failed: return "Failed";
        case MonsterAttackResult::InvalidTarget: return "InvalidTarget";
        case MonsterAttackResult::InvalidSkill: return "InvalidSkill";
        case MonsterAttackResult::NotAttackable: return "NotAttackable";
        case MonsterAttackResult::OutOfRange: return "OutOfRange";
        case MonsterAttackResult::InsufficientResources: return "InsufficientResources";
        case MonsterAttackResult::Stunned: return "Stunned";
        default: return "Unknown";
    }
}

/**
 * Convert MonsterAttackType to string
 */
std::string AttackTypeToString(MonsterAttackType type) {
    switch (type) {
        case MonsterAttackType::General: return "General";
        case MonsterAttackType::Skill: return "Skill";
        case MonsterAttackType::AreaSkill: return "AreaSkill";
        case MonsterAttackType::Force: return "Force";
        default: return "Unknown";
    }
}

/**
 * Validate attack parameters
 */
bool ValidateAttackParameters(_attack_param* pParam) {
    if (!pParam) {
        return false;
    }
    
    return pParam->IsValid();
}

/**
 * Calculate attack range
 */
float CalculateAttackRange(CCharacter* pAttacker, CCharacter* pTarget) {
    if (!pAttacker || !pTarget) {
        return -1.0f;
    }
    
    // Calculate 3D distance between attacker and target
    float dx = pAttacker->GetPosition().x - pTarget->GetPosition().x;
    float dy = pAttacker->GetPosition().y - pTarget->GetPosition().y;
    float dz = pAttacker->GetPosition().z - pTarget->GetPosition().z;
    
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Check if target is in attack range
 */
bool IsTargetInRange(CCharacter* pAttacker, CCharacter* pTarget, float maxRange) {
    float distance = CalculateAttackRange(pAttacker, pTarget);
    return (distance >= 0.0f && distance <= maxRange);
}

/**
 * Generate random attack part
 */
int GenerateRandomAttackPart() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dis(0, 7); // 8 possible attack parts
    return dis(gen);
}

/**
 * Calculate critical hit probability
 */
float CalculateCriticalProbability(CCharacter* pAttacker, CCharacter* pTarget) {
    if (!pAttacker || !pTarget) {
        return 0.0f;
    }
    
    // Base critical chance
    float baseCritical = 0.05f; // 5% base
    
    // Level difference modifier
    int levelDiff = pAttacker->GetLevel() - pTarget->GetLevel();
    float levelMod = levelDiff * 0.01f; // 1% per level difference
    
    // Clamp the result
    float totalCritical = baseCritical + levelMod;
    return std::max(0.0f, std::min(0.5f, totalCritical)); // Max 50% critical
}

} // namespace MonsterAttackUtils
