/*
 * CQuestMgr_Core.cpp - Core Quest Manager Implementation
 * Core functionality and utility methods for CQuestMgr
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CQuestMgr.h"
#include "../Headers/QuestStructures.h"
#include "../Headers/CRecordData.h"
#include "../Headers/CPlayer.h"
#include "../Headers/CPlayerDB.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <sstream>
#include <iomanip>

// Static member initialization
CRecordData* CQuestMgr::s_tblQuest = nullptr;
CRecordData* CQuestMgr::s_tblQuestHappenEvent = nullptr;

// External dependencies
extern uint32_t timeGetTime();
extern int strncmp(const char* str1, const char* str2, size_t count);

/**
 * Constructor
 */
CQuestMgr::CQuestMgr() 
    : m_pMaster(nullptr)
    , m_pQuestData(nullptr)
    , m_dwOldTimeoutChecktime(0)
    , m_lastCacheCleanup(std::chrono::system_clock::now()) {
    
    InitializeHappenEventContainers();
    Logger::Debug("CQuestMgr::CQuestMgr - Quest manager constructed");
}

/**
 * Destructor
 */
CQuestMgr::~CQuestMgr() {
    CleanConditionCache();
    Logger::Debug("CQuestMgr::~CQuestMgr - Quest manager destroyed");
}

/**
 * Initialize quest manager
 */
void CQuestMgr::InitMgr(CPlayer* pMaster, QuestDBBase* pQuestData) {
    try {
        Logger::Info("CQuestMgr::InitMgr - Initializing quest manager");
        
        if (!pMaster) {
            throw std::invalid_argument("Master player cannot be null");
        }
        
        if (!pQuestData) {
            throw std::invalid_argument("Quest data cannot be null");
        }
        
        m_pMaster = pMaster;
        m_pQuestData = pQuestData;
        
        // Initialize happen event containers
        InitializeHappenEventContainers();
        
        // Set initial timeout check time
        m_dwOldTimeoutChecktime = timeGetTime();
        
        // Clear condition cache
        CleanConditionCache();
        
        Logger::Info("CQuestMgr::InitMgr - Quest manager initialized successfully");
        
    } catch (const std::exception& e) {
        Logger::Error("CQuestMgr::InitMgr - Exception: %s", e.what());
        throw;
    }
}

/**
 * Check if NPC quest is completed
 */
bool CQuestMgr::IsCompleteNpcQuest(const char* pszCode, int bQuestRepeat) {
    if (!pszCode || !m_pQuestData) {
        Logger::Error("IsCompleteNpcQuest - Invalid parameters");
        return false;
    }
    
    try {
        Logger::Debug("IsCompleteNpcQuest - Checking quest: %s, repeat: %d", pszCode, bQuestRepeat);
        
        // Search through quest history
        for (const auto& historyEntry : m_pQuestData->m_History) {
            if (!historyEntry.IsUsed()) {
                continue;
            }
            
            // Compare quest codes (first 7 characters)
            if (strncmp(historyEntry.szQuestCode, pszCode, 7) == 0) {
                // If quest is not repeatable, it's completed
                if (bQuestRepeat != static_cast<int>(QuestRepeatType::Daily) &&
                    bQuestRepeat != static_cast<int>(QuestRepeatType::Weekly) &&
                    bQuestRepeat != static_cast<int>(QuestRepeatType::Monthly) &&
                    bQuestRepeat != static_cast<int>(QuestRepeatType::Unlimited)) {
                    
                    Logger::Debug("IsCompleteNpcQuest - Quest completed (non-repeatable): %s", pszCode);
                    return true;
                }
            }
        }
        
        Logger::Debug("IsCompleteNpcQuest - Quest not completed: %s", pszCode);
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("IsCompleteNpcQuest - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if NPC quest is in progress
 */
bool CQuestMgr::IsProcNpcQuest(const char* pszCode) {
    if (!pszCode || !m_pQuestData) {
        Logger::Error("IsProcNpcQuest - Invalid parameters");
        return false;
    }
    
    try {
        Logger::Debug("IsProcNpcQuest - Checking quest in progress: %s", pszCode);
        
        // Search through active quest list
        for (const auto& questList : m_pQuestData->m_List) {
            if (!questList.IsValid()) {
                continue;
            }
            
            // Check if this is an active quest (type 1)
            if (questList.byQuestType == 1) {
                // Get quest record
                auto* pQuestRecord = CRecordData::GetRecord(s_tblQuest, questList.wIndex);
                if (pQuestRecord) {
                    // Compare quest codes (first 7 characters)
                    if (strncmp(pQuestRecord->m_strCode, pszCode, 7) == 0) {
                        Logger::Debug("IsProcNpcQuest - Quest in progress: %s", pszCode);
                        return true;
                    }
                }
            }
        }
        
        Logger::Debug("IsProcNpcQuest - Quest not in progress: %s", pszCode);
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("IsProcNpcQuest - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if linked NPC quest is in progress
 */
bool CQuestMgr::IsProcLinkNpcQuest(const char* pszCode, uint32_t dwLinkIndex) {
    if (!pszCode) {
        Logger::Error("IsProcLinkNpcQuest - Invalid quest code");
        return false;
    }
    
    try {
        Logger::Debug("IsProcLinkNpcQuest - Checking linked quest: %s, link: %u", pszCode, dwLinkIndex);
        
        // For now, delegate to regular quest progress check
        // This can be enhanced with specific link checking logic
        bool result = IsProcNpcQuest(pszCode);
        
        Logger::Debug("IsProcLinkNpcQuest - Result: %s", result ? "true" : "false");
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("IsProcLinkNpcQuest - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if repeatable NPC quest is possible
 */
bool CQuestMgr::IsPossibleRepeatNpcQuest(const char* pszCode, uint32_t dwRepeatIndex) {
    if (!pszCode) {
        Logger::Error("IsPossibleRepeatNpcQuest - Invalid quest code");
        return false;
    }
    
    try {
        Logger::Debug("IsPossibleRepeatNpcQuest - Checking repeat possibility: %s, repeat: %u", 
                     pszCode, dwRepeatIndex);
        
        // Check if quest is not currently in progress
        if (IsProcNpcQuest(pszCode)) {
            Logger::Debug("IsPossibleRepeatNpcQuest - Quest already in progress");
            return false;
        }
        
        // Additional repeat-specific logic can be added here
        // For example: time-based restrictions, completion count limits, etc.
        
        Logger::Debug("IsPossibleRepeatNpcQuest - Repeat possible: %s", pszCode);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("IsPossibleRepeatNpcQuest - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get last happen event
 */
QuestHappenEventContainer* CQuestMgr::GetLastHappenEvent() {
    if (m_LastHappenEvent.IsValid()) {
        Logger::Debug("GetLastHappenEvent - Returning valid last happen event");
        return &m_LastHappenEvent;
    }
    
    Logger::Debug("GetLastHappenEvent - No valid last happen event");
    return nullptr;
}

/**
 * Return quest item
 */
bool CQuestMgr::ReturnItem(const char* pszItemCode, int nEndReturnItemCnt, 
                          uint8_t byQuestDBSlot, bool bCheckOnly) {
    if (!pszItemCode) {
        Logger::Error("ReturnItem - Invalid item code");
        return false;
    }
    
    try {
        Logger::Debug("ReturnItem - Item: %s, Count: %d, Slot: %d, CheckOnly: %s", 
                     pszItemCode, nEndReturnItemCnt, byQuestDBSlot, bCheckOnly ? "true" : "false");
        
        // For now, always return true (placeholder implementation)
        // This should be implemented with actual item management logic
        
        Logger::Debug("ReturnItem - Success");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("ReturnItem - Exception: %s", e.what());
        return false;
    }
}

/**
 * Delete quest item
 */
bool CQuestMgr::DeleteQuestItem(const char* pszItemCode, uint16_t wCount) {
    if (!pszItemCode) {
        Logger::Error("DeleteQuestItem - Invalid item code");
        return false;
    }
    
    try {
        Logger::Debug("DeleteQuestItem - Item: %s, Count: %d", pszItemCode, wCount);
        
        // Placeholder implementation
        // This should be implemented with actual item management logic
        
        Logger::Debug("DeleteQuestItem - Success");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("DeleteQuestItem - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check quest condition
 */
bool CQuestMgr::CheckCondition(const QuestConditionNode* pCond) {
    if (!pCond) {
        Logger::Error("CheckCondition - Invalid condition node");
        return false;
    }
    
    try {
        // Generate cache key
        std::string cacheKey = GenerateConditionCacheKey(pCond);
        
        // Check cache first
        auto cacheIt = m_conditionCache.find(cacheKey);
        if (cacheIt != m_conditionCache.end()) {
            Logger::Debug("CheckCondition - Cache hit for condition");
            return cacheIt->second;
        }
        
        // Evaluate condition
        bool result = EvaluateCondition(pCond);
        
        // Cache result
        m_conditionCache[cacheKey] = result;
        
        // Clean cache periodically
        auto now = std::chrono::system_clock::now();
        if (std::chrono::duration_cast<std::chrono::minutes>(now - m_lastCacheCleanup).count() > 5) {
            CleanConditionCache();
            m_lastCacheCleanup = now;
        }
        
        Logger::Debug("CheckCondition - Condition result: %s", result ? "true" : "false");
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("CheckCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Static condition checker (for compatibility)
 */
bool CQuestMgr::_CheckCondition(CQuestMgr* pQuestMgr, const QuestConditionNode* pCond) {
    if (!pQuestMgr) {
        Logger::Error("_CheckCondition - Invalid quest manager");
        return false;
    }

    return pQuestMgr->CheckCondition(pCond);
}

/**
 * Evaluate quest condition
 */
bool CQuestMgr::EvaluateCondition(const QuestConditionNode* pCond) {
    if (!pCond || !pCond->IsValid()) {
        Logger::Debug("EvaluateCondition - Invalid or empty condition");
        return true; // Empty conditions are considered true
    }

    try {
        bool result = false;

        switch (pCond->m_nCondType) {
            case QuestConditionType::Level:
                result = EvaluateLevelCondition(pCond);
                break;

            case QuestConditionType::Class:
                result = EvaluateClassCondition(pCond);
                break;

            case QuestConditionType::Item:
                result = EvaluateItemCondition(pCond);
                break;

            case QuestConditionType::Quest:
                result = EvaluateQuestCondition(pCond);
                break;

            case QuestConditionType::Skill:
                result = EvaluateSkillCondition(pCond);
                break;

            case QuestConditionType::Stat:
                result = EvaluateStatCondition(pCond);
                break;

            case QuestConditionType::Time:
                result = EvaluateTimeCondition(pCond);
                break;

            case QuestConditionType::Custom:
                result = EvaluateCustomCondition(pCond);
                break;

            default:
                Logger::Warning("EvaluateCondition - Unknown condition type: %d",
                               static_cast<int>(pCond->m_nCondType));
                result = false;
                break;
        }

        // Apply negation if specified
        if (pCond->m_bNegate) {
            result = !result;
        }

        Logger::Debug("EvaluateCondition - Type: %s, Result: %s, Negated: %s",
                     QuestUtils::QuestConditionTypeToString(pCond->m_nCondType).c_str(),
                     result ? "true" : "false",
                     pCond->m_bNegate ? "true" : "false");

        return result;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate level condition
 */
bool CQuestMgr::EvaluateLevelCondition(const QuestConditionNode* pCond) {
    if (!m_pMaster) {
        Logger::Error("EvaluateLevelCondition - No master player");
        return false;
    }

    try {
        int playerLevel = CPlayerDB::GetLevel(&m_pMaster->m_Param);
        int requiredLevel = pCond->m_nParam1;

        bool result = (playerLevel >= requiredLevel);

        Logger::Debug("EvaluateLevelCondition - Player: %d, Required: %d, Result: %s",
                     playerLevel, requiredLevel, result ? "true" : "false");

        return result;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateLevelCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate class condition
 */
bool CQuestMgr::EvaluateClassCondition(const QuestConditionNode* pCond) {
    if (!m_pMaster) {
        Logger::Error("EvaluateClassCondition - No master player");
        return false;
    }

    try {
        // Placeholder implementation
        // This should check player's class against required class
        Logger::Debug("EvaluateClassCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateClassCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate item condition
 */
bool CQuestMgr::EvaluateItemCondition(const QuestConditionNode* pCond) {
    if (!m_pMaster) {
        Logger::Error("EvaluateItemCondition - No master player");
        return false;
    }

    try {
        // Placeholder implementation
        // This should check if player has required items
        Logger::Debug("EvaluateItemCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateItemCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate quest condition
 */
bool CQuestMgr::EvaluateQuestCondition(const QuestConditionNode* pCond) {
    try {
        // Check if a specific quest is completed or in progress
        const char* questCode = pCond->m_strParam;
        if (strlen(questCode) == 0) {
            Logger::Warning("EvaluateQuestCondition - Empty quest code");
            return false;
        }

        bool result = false;
        switch (pCond->m_nParam1) {
            case 0: // Quest must be completed
                result = IsCompleteNpcQuest(questCode, pCond->m_nParam2);
                break;

            case 1: // Quest must be in progress
                result = IsProcNpcQuest(questCode);
                break;

            default:
                Logger::Warning("EvaluateQuestCondition - Unknown quest condition type: %d",
                               pCond->m_nParam1);
                result = false;
                break;
        }

        Logger::Debug("EvaluateQuestCondition - Quest: %s, Type: %d, Result: %s",
                     questCode, pCond->m_nParam1, result ? "true" : "false");

        return result;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateQuestCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate skill condition
 */
bool CQuestMgr::EvaluateSkillCondition(const QuestConditionNode* pCond) {
    try {
        // Placeholder implementation
        Logger::Debug("EvaluateSkillCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateSkillCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate stat condition
 */
bool CQuestMgr::EvaluateStatCondition(const QuestConditionNode* pCond) {
    try {
        // Placeholder implementation
        Logger::Debug("EvaluateStatCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateStatCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate time condition
 */
bool CQuestMgr::EvaluateTimeCondition(const QuestConditionNode* pCond) {
    try {
        // Placeholder implementation
        Logger::Debug("EvaluateTimeCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateTimeCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Evaluate custom condition
 */
bool CQuestMgr::EvaluateCustomCondition(const QuestConditionNode* pCond) {
    try {
        // Placeholder implementation
        Logger::Debug("EvaluateCustomCondition - Placeholder implementation");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("EvaluateCustomCondition - Exception: %s", e.what());
        return false;
    }
}

/**
 * Initialize happen event containers
 */
void CQuestMgr::InitializeHappenEventContainers() {
    try {
        m_LastHappenEvent.Init();

        for (auto& tempEvent : m_pTempHappenEvent) {
            tempEvent.Init();
        }

        Logger::Debug("InitializeHappenEventContainers - Initialized all happen event containers");

    } catch (const std::exception& e) {
        Logger::Error("InitializeHappenEventContainers - Exception: %s", e.what());
    }
}

/**
 * Clean condition cache
 */
void CQuestMgr::CleanConditionCache() const {
    try {
        m_conditionCache.clear();
        Logger::Debug("CleanConditionCache - Condition cache cleared");

    } catch (const std::exception& e) {
        Logger::Error("CleanConditionCache - Exception: %s", e.what());
    }
}

/**
 * Generate cache key for condition
 */
std::string CQuestMgr::GenerateConditionCacheKey(const QuestConditionNode* pCond) const {
    if (!pCond) {
        return "NULL_CONDITION";
    }

    std::ostringstream oss;
    oss << static_cast<int>(pCond->m_nCondType) << "_"
        << pCond->m_nParam1 << "_"
        << pCond->m_nParam2 << "_"
        << pCond->m_nParam3 << "_"
        << pCond->m_strParam << "_"
        << (pCond->m_bNegate ? "1" : "0");

    return oss.str();
}

/**
 * Log quest operation
 */
void CQuestMgr::LogQuestOperation(const std::string& operation,
                                 const std::string& questCode,
                                 bool success) const {
    if (questCode.empty()) {
        Logger::Info("Quest Operation: %s - %s",
                    operation.c_str(), success ? "SUCCESS" : "FAILED");
    } else {
        Logger::Info("Quest Operation: %s [%s] - %s",
                    operation.c_str(), questCode.c_str(), success ? "SUCCESS" : "FAILED");
    }
}

/**
 * Cleanup expired temporary events
 */
void CQuestMgr::CleanupExpiredTempEvents() {
    try {
        auto now = std::chrono::system_clock::now();
        constexpr auto maxAge = std::chrono::minutes(30); // 30 minutes max age

        for (auto& tempEvent : m_pTempHappenEvent) {
            if (tempEvent.IsSet() && tempEvent.GetAge() > maxAge) {
                Logger::Debug("CleanupExpiredTempEvents - Cleaning expired temp event");
                tempEvent.Init();
            }
        }

    } catch (const std::exception& e) {
        Logger::Error("CleanupExpiredTempEvents - Exception: %s", e.what());
    }
}
