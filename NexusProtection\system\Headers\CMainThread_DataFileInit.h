/**
 * @file CMainThread_DataFileInit.h
 * @brief Data file initialization system for CMainThread
 * 
 * Refactored from DataFileInitCMainThreadAEAA_NXZ_1401E5BF0.c
 * This handles the loading and initialization of all game data files including
 * items, monsters, maps, skills, effects, and configuration data.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <filesystem>
#include <unordered_map>
#include <chrono>
#include <functional>

// Forward declarations for data structures
class CRecordData;
class CItemLootTable;
class COreCuttingTable;
class CItemUpgradeTable;
class CMonsterSPGroupTable;
class CPotionMgr;
class CQuestMgr;
class ItemCombineMgr;
class CPcBangFavor;
class CSUItemSystem;
class AggroCaculateData;
class MonsterSetInfoData;

// Legacy structures for compatibility
struct _skill_fld;
struct _mob_message;
struct _base_fld;

/**
 * @enum DataFileType
 * @brief Enumeration of all data file types for loading tracking
 */
enum class DataFileType : uint32_t {
    // Core Data Files
    ItemData = 0,
    SkillData,
    ForceData,
    ClassSkillData,
    BulletItemEffectData,
    
    // Character Data
    ClassData,
    GradeData,
    PlayerCharacterData,
    MonsterCharacterData,
    NPCharacterData,
    
    // Message Data
    MobMessageData,
    
    // Item Systems
    AnimusItemData,
    ExpData,
    ItemLootingData,
    OreCuttingData,
    ItemMakeData,
    ItemCombineData,
    ItemExchangeData,
    ItemUpgradeData,
    
    // Unit Data
    UnitHeadData,
    UnitUpperData,
    UnitLowerData,
    UnitArmsData,
    UnitShoulderData,
    UnitBackData,
    UnitBulletData,
    UnitFrameData,
    
    // System Data
    EditData,
    MonsterCharacterAIData,
    
    // Manager Systems
    PotionSystem,
    QuestSystem,
    ItemCombineSystem,
    PcBangSystem,
    SUItemSystem,
    
    // Configuration Files
    AggroCalculateConfig,
    MonsterSetConfig,
    
    MAX_DATA_FILES
};

/**
 * @enum DataFileLoadResult
 * @brief Result codes for data file loading operations
 */
enum class DataFileLoadResult : int32_t {
    Success = 1,
    Failure = 0,
    FileNotFound = -1,
    InvalidFormat = -2,
    MemoryError = -3,
    DependencyError = -4,
    ValidationError = -5,
    SecurityError = -6
};

/**
 * @struct DataFileInfo
 * @brief Information about a data file
 */
struct DataFileInfo {
    std::string filename;
    std::string description;
    uint32_t structSize;
    bool isRequired;
    std::vector<DataFileType> dependencies;
    
    DataFileInfo() = default;
    DataFileInfo(const std::string& file, const std::string& desc, uint32_t size, bool required = true)
        : filename(file), description(desc), structSize(size), isRequired(required) {}
};

/**
 * @struct DataFileLoadStats
 * @brief Statistics for data file loading process
 */
struct DataFileLoadStats {
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::array<bool, static_cast<size_t>(DataFileType::MAX_DATA_FILES)> fileStatus;
    std::array<std::chrono::milliseconds, static_cast<size_t>(DataFileType::MAX_DATA_FILES)> fileLoadTime;
    std::array<size_t, static_cast<size_t>(DataFileType::MAX_DATA_FILES)> fileSize;
    uint32_t successfulFiles;
    uint32_t failedFiles;
    std::string lastError;
    
    void Reset() {
        startTime = std::chrono::steady_clock::now();
        fileStatus.fill(false);
        fileLoadTime.fill(std::chrono::milliseconds::zero());
        fileSize.fill(0);
        successfulFiles = 0;
        failedFiles = 0;
        lastError.clear();
    }
    
    std::chrono::milliseconds GetTotalLoadTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        uint32_t total = successfulFiles + failedFiles;
        return total > 0 ? (static_cast<double>(successfulFiles) / total) * 100.0 : 0.0;
    }
    
    size_t GetTotalDataSize() const {
        size_t total = 0;
        for (const auto& size : fileSize) {
            total += size;
        }
        return total;
    }
};

/**
 * @class CMainThreadDataFileInit
 * @brief Data file initialization and management system
 * 
 * This class handles the loading and initialization of all game data files.
 * It provides modern C++20 interfaces while maintaining compatibility with
 * the original data structures and loading order.
 * 
 * Original function: CMainThread::DataFileInit (Address: 0x1401E5BF0)
 * Refactored to modern C++20 with comprehensive error handling and monitoring.
 */
class CMainThreadDataFileInit {
public:
    /**
     * @brief Constructor
     */
    CMainThreadDataFileInit();
    
    /**
     * @brief Destructor
     */
    ~CMainThreadDataFileInit();
    
    // Delete copy constructor and assignment operator
    CMainThreadDataFileInit(const CMainThreadDataFileInit&) = delete;
    CMainThreadDataFileInit& operator=(const CMainThreadDataFileInit&) = delete;
    
    // Allow move constructor and assignment operator
    CMainThreadDataFileInit(CMainThreadDataFileInit&&) = default;
    CMainThreadDataFileInit& operator=(CMainThreadDataFileInit&&) = default;
    
    /**
     * @brief Main data file initialization function
     * 
     * Loads all game data files in the correct order with comprehensive error handling.
     * This is the modern refactored version of the original DataFileInit function.
     * 
     * @param mainThread Pointer to CMainThread instance for accessing data tables
     * @return DataFileLoadResult indicating success or failure
     */
    DataFileLoadResult InitializeDataFiles(class CMainThread* mainThread);
    
    /**
     * @brief Legacy DataFileInit function for backward compatibility
     * 
     * Maintains the original function signature for existing code.
     * Original: char __fastcall CMainThread::DataFileInit(CMainThread *this)
     * 
     * @param mainThread Pointer to CMainThread instance
     * @return char (1 for success, 0 for failure)
     */
    static char DataFileInit_Legacy(class CMainThread* mainThread);
    
    /**
     * @brief Get data file loading statistics
     * @return const reference to loading statistics
     */
    const DataFileLoadStats& GetLoadingStats() const;
    
    /**
     * @brief Check if a specific data file is loaded
     * @param fileType The data file type to check
     * @return true if loaded, false otherwise
     */
    bool IsFileLoaded(DataFileType fileType) const;
    
    /**
     * @brief Get the last loading error message
     * @return string containing the last error message
     */
    std::string GetLastError() const;
    
    /**
     * @brief Get information about a specific data file type
     * @param fileType The data file type
     * @return DataFileInfo structure with file information
     */
    static DataFileInfo GetFileInfo(DataFileType fileType);
    
    /**
     * @brief Validate all loaded data files
     * @return true if all required files are loaded and valid
     */
    bool ValidateLoadedData() const;

private:
    // Loading statistics and monitoring
    DataFileLoadStats m_loadStats;
    mutable std::mutex m_statsMutex;
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    // File information registry
    static std::unordered_map<DataFileType, DataFileInfo> s_fileRegistry;
    
    // Security
    uint64_t m_securityCookie{0};

private:
    // Core data loading phases
    DataFileLoadResult LoadItemData(class CMainThread* mainThread);
    DataFileLoadResult LoadEffectData(class CMainThread* mainThread);
    DataFileLoadResult LoadCharacterData(class CMainThread* mainThread);
    DataFileLoadResult LoadMessageData(class CMainThread* mainThread);
    DataFileLoadResult LoadItemSystems(class CMainThread* mainThread);
    DataFileLoadResult LoadUnitData(class CMainThread* mainThread);
    DataFileLoadResult LoadSystemData(class CMainThread* mainThread);
    DataFileLoadResult LoadManagerSystems(class CMainThread* mainThread);
    DataFileLoadResult LoadConfigurationFiles(class CMainThread* mainThread);
    DataFileLoadResult FinalizeDataLoading(class CMainThread* mainThread);
    
    // Individual file loading functions
    DataFileLoadResult LoadSingleFile(CRecordData* recordData, const std::string& filename, 
                                     uint32_t structSize, DataFileType fileType);
    DataFileLoadResult LoadEffectFiles(class CMainThread* mainThread);
    DataFileLoadResult LoadUnitPartFiles(class CMainThread* mainThread);
    DataFileLoadResult LoadMobMessageData(class CMainThread* mainThread);
    
    // System initialization functions
    DataFileLoadResult InitializePotionManager();
    DataFileLoadResult InitializeQuestManager();
    DataFileLoadResult InitializeItemCombineManager();
    DataFileLoadResult InitializePcBangSystem();
    DataFileLoadResult InitializeSUItemSystem();
    DataFileLoadResult InitializeMonsterSPGroupTable(class CMainThread* mainThread);
    
    // Configuration loading functions
    DataFileLoadResult LoadAggroCalculateData();
    DataFileLoadResult LoadMonsterSetInfoData();
    
    // Validation and finalization functions
    DataFileLoadResult SetGlobalDataNames(class CMainThread* mainThread);
    DataFileLoadResult CheckLoadedData(class CMainThread* mainThread);
    DataFileLoadResult SetupMainThreadControl(class CMainThread* mainThread);
    
    // Utility functions
    void LogFileLoading(DataFileType fileType, bool success, const std::string& errorMsg = "");
    void SetLastError(const std::string& error);
    bool ValidateFileExists(const std::string& filename);
    size_t GetFileSize(const std::string& filename);
    
    // Static initialization
    static void InitializeFileRegistry();
    
    // Legacy compatibility functions
    static bool WriteTableData_Legacy(int tableId, void* tableData, int param, char* errorCode);
    static void SetupXmasEffects(class CMainThread* mainThread);
};

/**
 * @brief Convert DataFileType enum to string for logging
 * @param fileType The data file type
 * @return String representation of the file type
 */
std::string DataFileTypeToString(DataFileType fileType);

/**
 * @brief Convert DataFileLoadResult enum to string for logging
 * @param result The load result
 * @return String representation of the result
 */
std::string DataFileLoadResultToString(DataFileLoadResult result);
