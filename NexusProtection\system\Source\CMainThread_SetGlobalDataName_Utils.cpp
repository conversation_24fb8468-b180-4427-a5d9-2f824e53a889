/**
 * @file CMainThread_SetGlobalDataName_Utils.cpp
 * @brief Utility functions for global data name assignment system
 * 
 * Contains helper functions for name validation, assignment tracking,
 * and error handling.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_SetGlobalDataName.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <algorithm>
#include <regex>

/**
 * @brief Get global data name assignment statistics
 * @return const reference to assignment statistics
 */
const GlobalDataNameStats& CMainThreadSetGlobalDataName::GetAssignmentStats() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_assignmentStats;
}

/**
 * @brief Check if a specific data name is assigned
 * @param nameType The global data name type to check
 * @return true if assigned, false otherwise
 */
bool CMainThreadSetGlobalDataName::IsDataNameAssigned(GlobalDataNameType nameType) const {
    std::lock_guard<std::mutex> lock(m_namesMutex);
    return m_assignedNames.find(nameType) != m_assignedNames.end();
}

/**
 * @brief Get the assigned name for a specific data type
 * @param nameType The global data name type
 * @return string containing the assigned name
 */
std::string CMainThreadSetGlobalDataName::GetAssignedName(GlobalDataNameType nameType) const {
    std::lock_guard<std::mutex> lock(m_namesMutex);
    auto it = m_assignedNames.find(nameType);
    if (it != m_assignedNames.end()) {
        return it->second;
    }
    return "";
}

/**
 * @brief Get the last assignment error message
 * @return string containing the last error message
 */
std::string CMainThreadSetGlobalDataName::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Get information about a specific name type
 * @param nameType The name type
 * @return GlobalDataNameInfo structure with name information
 */
GlobalDataNameInfo CMainThreadSetGlobalDataName::GetNameInfo(GlobalDataNameType nameType) {
    auto it = s_nameRegistry.find(nameType);
    if (it != s_nameRegistry.end()) {
        return it->second;
    }
    return GlobalDataNameInfo("Unknown", "Unknown name type", "", false);
}

/**
 * @brief Validate all required names are assigned
 * @return true if all required names are assigned
 */
bool CMainThreadSetGlobalDataName::ValidateRequiredNames() const {
    std::lock_guard<std::mutex> lock(m_namesMutex);
    
    // Check all required name assignments
    for (const auto& [nameType, nameInfo] : s_nameRegistry) {
        if (nameInfo.isRequired) {
            if (m_assignedNames.find(nameType) == m_assignedNames.end()) {
                return false;
            }
        }
    }
    
    return true;
}

/**
 * @brief Assign a specific global data name
 * @param nameType The type of name to assign
 * @param name The name to assign
 * @return GlobalDataNameResult indicating success or failure
 */
GlobalDataNameResult CMainThreadSetGlobalDataName::AssignGlobalDataName(GlobalDataNameType nameType, const std::string& name) {
    try {
        auto startTime = std::chrono::steady_clock::now();
        
        // Validate name format
        if (!ValidateNameFormat(name)) {
            LogAssignment(nameType, false, name, "Invalid name format");
            return GlobalDataNameResult::InvalidName;
        }
        
        // Get name info for validation
        auto nameInfo = GetNameInfo(nameType);
        
        // Validate name length
        if (!ValidateNameLength(name, nameInfo.maxLength)) {
            LogAssignment(nameType, false, name, std::format("Name too long (max: {})", nameInfo.maxLength));
            return GlobalDataNameResult::NameTooLong;
        }
        
        // Validate name characters
        if (!ValidateNameCharacters(name)) {
            LogAssignment(nameType, false, name, "Invalid characters in name");
            return GlobalDataNameResult::InvalidCharacters;
        }
        
        // Validate name uniqueness
        if (!ValidateNameUniqueness(name, nameType)) {
            LogAssignment(nameType, false, name, "Duplicate name");
            return GlobalDataNameResult::DuplicateName;
        }
        
        // Assign the name
        {
            std::lock_guard<std::mutex> lock(m_namesMutex);
            m_assignedNames[nameType] = name;
        }
        
        // Update global names storage
        {
            std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
            g_GlobalDataNames[GlobalDataNameTypeToString(nameType)] = name;
        }
        
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        LogAssignment(nameType, true, name);
        
        // Update timing statistics
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            size_t index = static_cast<size_t>(nameType);
            if (index < m_assignmentStats.assignmentTime.size()) {
                m_assignmentStats.assignmentTime[index] = duration;
            }
        }
        
        return GlobalDataNameResult::Success;
        
    } catch (const std::exception& e) {
        LogAssignment(nameType, false, name, std::format("Exception: {}", e.what()));
        return GlobalDataNameResult::SystemError;
    }
}

/**
 * @brief Get all assigned global data names
 * @return unordered_map of name types to assigned names
 */
std::unordered_map<GlobalDataNameType, std::string> CMainThreadSetGlobalDataName::GetAllAssignedNames() const {
    std::lock_guard<std::mutex> lock(m_namesMutex);
    return m_assignedNames;
}

/**
 * @brief Validate name format
 * @param name The name to validate
 * @return true if format is valid
 */
bool CMainThreadSetGlobalDataName::ValidateNameFormat(const std::string& name) {
    if (name.empty()) {
        return false;
    }
    
    // Name must start with a letter or underscore
    if (!std::isalpha(name[0]) && name[0] != '_') {
        return false;
    }
    
    return true;
}

/**
 * @brief Validate name length
 * @param name The name to validate
 * @param maxLength Maximum allowed length
 * @return true if length is valid
 */
bool CMainThreadSetGlobalDataName::ValidateNameLength(const std::string& name, size_t maxLength) {
    return name.length() <= maxLength;
}

/**
 * @brief Validate name characters
 * @param name The name to validate
 * @return true if characters are valid
 */
bool CMainThreadSetGlobalDataName::ValidateNameCharacters(const std::string& name) {
    // Allow alphanumeric characters, underscores, and hyphens
    std::regex validPattern("^[a-zA-Z0-9_-]+$");
    return std::regex_match(name, validPattern);
}

/**
 * @brief Validate name uniqueness
 * @param name The name to validate
 * @param excludeType The name type to exclude from uniqueness check
 * @return true if name is unique
 */
bool CMainThreadSetGlobalDataName::ValidateNameUniqueness(const std::string& name, GlobalDataNameType excludeType) {
    std::lock_guard<std::mutex> lock(m_namesMutex);
    
    for (const auto& [nameType, assignedName] : m_assignedNames) {
        if (nameType != excludeType && assignedName == name) {
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Log assignment result
 * @param nameType Type of name assignment
 * @param success Whether assignment succeeded
 * @param name The name that was assigned
 * @param errorMsg Error message if assignment failed
 */
void CMainThreadSetGlobalDataName::LogAssignment(GlobalDataNameType nameType, bool success, 
                                                const std::string& name, const std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t index = static_cast<size_t>(nameType);
    if (index < m_assignmentStats.nameStatus.size()) {
        m_assignmentStats.nameStatus[index] = success;
        if (!name.empty()) {
            m_assignmentStats.assignedNames[index] = name;
        }
        
        if (success) {
            m_assignmentStats.successfulAssignments++;
        } else {
            m_assignmentStats.failedAssignments++;
            if (!errorMsg.empty()) {
                m_assignmentStats.lastError = errorMsg;
            }
        }
    }
    
    // Log to console
    auto nameInfo = GetNameInfo(nameType);
    if (success) {
        std::cout << std::format("[INFO] Assigned {}: '{}'", nameInfo.name, name) << std::endl;
    } else {
        std::cout << std::format("[ERROR] Failed to assign {}: {}", nameInfo.name, errorMsg) << std::endl;
    }
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void CMainThreadSetGlobalDataName::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
}

/**
 * @brief Generate a default name for a name type
 * @param nameType The name type
 * @return string containing the default name
 */
std::string CMainThreadSetGlobalDataName::GenerateDefaultName(GlobalDataNameType nameType) {
    auto nameInfo = GetNameInfo(nameType);
    if (!nameInfo.defaultValue.empty()) {
        return nameInfo.defaultValue;
    }
    
    // Generate a default name based on the type
    return std::format("Default{}", GlobalDataNameTypeToString(nameType));
}

/**
 * @brief Sanitize a name by removing invalid characters
 * @param name The name to sanitize
 * @return string containing the sanitized name
 */
std::string CMainThreadSetGlobalDataName::SanitizeName(const std::string& name) {
    std::string sanitized;
    sanitized.reserve(name.length());
    
    for (char c : name) {
        if (std::isalnum(c) || c == '_' || c == '-') {
            sanitized += c;
        }
    }
    
    // Ensure name starts with a letter or underscore
    if (!sanitized.empty() && !std::isalpha(sanitized[0]) && sanitized[0] != '_') {
        sanitized = "_" + sanitized;
    }
    
    return sanitized;
}

/**
 * @brief Legacy data table name assignment function
 * @param tableName Name of the table
 * @param nameType Type of the name
 * @return true if assignment succeeds
 */
bool CMainThreadSetGlobalDataName::AssignDataTableName_Legacy(const char* tableName, const char* nameType) {
    if (!tableName || !nameType) {
        return false;
    }
    
    try {
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        g_GlobalDataNames[nameType] = tableName;
        std::cout << "[INFO] Legacy assigned " << nameType << ": " << tableName << std::endl;
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Legacy manager name assignment function
 * @param managerName Name of the manager
 * @param nameType Type of the name
 * @return true if assignment succeeds
 */
bool CMainThreadSetGlobalDataName::AssignManagerName_Legacy(const char* managerName, const char* nameType) {
    if (!managerName || !nameType) {
        return false;
    }
    
    try {
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        g_GlobalDataNames[nameType] = managerName;
        std::cout << "[INFO] Legacy assigned " << nameType << ": " << managerName << std::endl;
        return true;
        
    } catch (...) {
        return false;
    }
}
