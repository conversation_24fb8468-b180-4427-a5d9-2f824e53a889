/**
 * @file CMainThread_CheckLoadedData_Registry.cpp
 * @brief Validation registry initialization and string conversion functions
 * 
 * Contains the initialization of the validation registry and utility
 * functions for converting enums to strings.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_CheckLoadedData.h"
#include <unordered_map>

/**
 * @brief Initialize the validation registry with all validation types
 */
void CMainThreadCheckLoadedData::InitializeValidationRegistry() {
    if (!s_validationRegistry.empty()) {
        return; // Already initialized
    }
    
    // Core Data Tables
    s_validationRegistry[DataValidationType::ItemData] = 
        DataValidationInfo("Item Data", "Core item database", true, 1, 50000);
    s_validationRegistry[DataValidationType::SkillData] = 
        DataValidationInfo("Skill Data", "Skill database", true, 1, 10000);
    s_validationRegistry[DataValidationType::ForceData] = 
        DataValidationInfo("Force Data", "Force power database", true, 1, 5000);
    s_validationRegistry[DataValidationType::ClassSkillData] = 
        DataValidationInfo("Class Skill Data", "Class-specific skill database", true, 1, 5000);
    s_validationRegistry[DataValidationType::BulletItemEffectData] = 
        DataValidationInfo("Bullet Item Effect Data", "Bullet and item effect database", true, 1, 10000);
    
    // Character Data
    s_validationRegistry[DataValidationType::ClassData] = 
        DataValidationInfo("Class Data", "Character class database", true, 1, 100);
    s_validationRegistry[DataValidationType::GradeData] = 
        DataValidationInfo("Grade Data", "Character grade database", true, 1, 1000);
    s_validationRegistry[DataValidationType::PlayerCharacterData] = 
        DataValidationInfo("Player Character Data", "Player character database", true, 1, 1000);
    s_validationRegistry[DataValidationType::MonsterCharacterData] = 
        DataValidationInfo("Monster Character Data", "Monster character database", true, 1, 10000);
    s_validationRegistry[DataValidationType::NPCharacterData] = 
        DataValidationInfo("NPC Character Data", "NPC character database", true, 1, 5000);
    
    // Item Systems
    s_validationRegistry[DataValidationType::AnimusItemData] = 
        DataValidationInfo("Animus Item Data", "Animus item database", true, 1, 5000);
    s_validationRegistry[DataValidationType::ExpData] = 
        DataValidationInfo("Experience Data", "Experience point database", true, 1, 1000);
    s_validationRegistry[DataValidationType::ItemLootingData] = 
        DataValidationInfo("Item Looting Data", "Item looting database", true, 1, 10000);
    s_validationRegistry[DataValidationType::OreCuttingData] = 
        DataValidationInfo("Ore Cutting Data", "Ore cutting database", true, 1, 1000);
    s_validationRegistry[DataValidationType::ItemMakeData] = 
        DataValidationInfo("Item Make Data", "Item crafting database", true, 1, 5000);
    s_validationRegistry[DataValidationType::ItemCombineData] = 
        DataValidationInfo("Item Combine Data", "Item combination database", true, 1, 5000);
    s_validationRegistry[DataValidationType::ItemExchangeData] = 
        DataValidationInfo("Item Exchange Data", "Item exchange database", true, 1, 5000);
    s_validationRegistry[DataValidationType::ItemUpgradeData] = 
        DataValidationInfo("Item Upgrade Data", "Item upgrade database", true, 1, 5000);
    
    // Unit Data
    s_validationRegistry[DataValidationType::UnitHeadData] = 
        DataValidationInfo("Unit Head Data", "Unit head part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitUpperData] = 
        DataValidationInfo("Unit Upper Data", "Unit upper body part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitLowerData] = 
        DataValidationInfo("Unit Lower Data", "Unit lower body part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitArmsData] = 
        DataValidationInfo("Unit Arms Data", "Unit arms part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitShoulderData] = 
        DataValidationInfo("Unit Shoulder Data", "Unit shoulder part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitBackData] = 
        DataValidationInfo("Unit Back Data", "Unit back part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitBulletData] = 
        DataValidationInfo("Unit Bullet Data", "Unit bullet part database", true, 1, 1000);
    s_validationRegistry[DataValidationType::UnitFrameData] = 
        DataValidationInfo("Unit Frame Data", "Unit frame part database", true, 1, 1000);
    
    // System Data
    s_validationRegistry[DataValidationType::EditData] = 
        DataValidationInfo("Edit Data", "Edit system database", true, 1, 5000);
    s_validationRegistry[DataValidationType::MonsterCharacterAIData] = 
        DataValidationInfo("Monster Character AI Data", "Monster AI database", true, 1, 5000);
    s_validationRegistry[DataValidationType::MobMessageData] = 
        DataValidationInfo("Mob Message Data", "Monster message database", true, 1, 5000);
    
    // Manager Systems
    s_validationRegistry[DataValidationType::PotionSystem] = 
        DataValidationInfo("Potion System", "Potion manager system", true, 1, 1);
    s_validationRegistry[DataValidationType::QuestSystem] = 
        DataValidationInfo("Quest System", "Quest manager system", true, 1, 1);
    s_validationRegistry[DataValidationType::ItemCombineSystem] = 
        DataValidationInfo("Item Combine System", "Item combine manager system", true, 1, 1);
    s_validationRegistry[DataValidationType::PcBangSystem] = 
        DataValidationInfo("PC Bang System", "PC Bang favor system", false, 0, 1);
    s_validationRegistry[DataValidationType::SUItemSystem] = 
        DataValidationInfo("SU Item System", "Special upgrade item system", false, 0, 1);
    s_validationRegistry[DataValidationType::MonsterSPGroupTable] = 
        DataValidationInfo("Monster SP Group Table", "Monster special group table", false, 0, 1);
    
    // Configuration Data
    s_validationRegistry[DataValidationType::AggroCalculateConfig] = 
        DataValidationInfo("Aggro Calculate Config", "Aggro calculation configuration", true, 1, 1);
    s_validationRegistry[DataValidationType::MonsterSetConfig] = 
        DataValidationInfo("Monster Set Config", "Monster set configuration", true, 1, 1);
}

/**
 * @brief Convert DataValidationType enum to string for logging
 * @param validationType The validation type
 * @return String representation of the validation type
 */
std::string DataValidationTypeToString(DataValidationType validationType) {
    static const std::unordered_map<DataValidationType, std::string> typeNames = {
        // Core Data Tables
        {DataValidationType::ItemData, "ItemData"},
        {DataValidationType::SkillData, "SkillData"},
        {DataValidationType::ForceData, "ForceData"},
        {DataValidationType::ClassSkillData, "ClassSkillData"},
        {DataValidationType::BulletItemEffectData, "BulletItemEffectData"},
        
        // Character Data
        {DataValidationType::ClassData, "ClassData"},
        {DataValidationType::GradeData, "GradeData"},
        {DataValidationType::PlayerCharacterData, "PlayerCharacterData"},
        {DataValidationType::MonsterCharacterData, "MonsterCharacterData"},
        {DataValidationType::NPCharacterData, "NPCharacterData"},
        
        // Item Systems
        {DataValidationType::AnimusItemData, "AnimusItemData"},
        {DataValidationType::ExpData, "ExpData"},
        {DataValidationType::ItemLootingData, "ItemLootingData"},
        {DataValidationType::OreCuttingData, "OreCuttingData"},
        {DataValidationType::ItemMakeData, "ItemMakeData"},
        {DataValidationType::ItemCombineData, "ItemCombineData"},
        {DataValidationType::ItemExchangeData, "ItemExchangeData"},
        {DataValidationType::ItemUpgradeData, "ItemUpgradeData"},
        
        // Unit Data
        {DataValidationType::UnitHeadData, "UnitHeadData"},
        {DataValidationType::UnitUpperData, "UnitUpperData"},
        {DataValidationType::UnitLowerData, "UnitLowerData"},
        {DataValidationType::UnitArmsData, "UnitArmsData"},
        {DataValidationType::UnitShoulderData, "UnitShoulderData"},
        {DataValidationType::UnitBackData, "UnitBackData"},
        {DataValidationType::UnitBulletData, "UnitBulletData"},
        {DataValidationType::UnitFrameData, "UnitFrameData"},
        
        // System Data
        {DataValidationType::EditData, "EditData"},
        {DataValidationType::MonsterCharacterAIData, "MonsterCharacterAIData"},
        {DataValidationType::MobMessageData, "MobMessageData"},
        
        // Manager Systems
        {DataValidationType::PotionSystem, "PotionSystem"},
        {DataValidationType::QuestSystem, "QuestSystem"},
        {DataValidationType::ItemCombineSystem, "ItemCombineSystem"},
        {DataValidationType::PcBangSystem, "PcBangSystem"},
        {DataValidationType::SUItemSystem, "SUItemSystem"},
        {DataValidationType::MonsterSPGroupTable, "MonsterSPGroupTable"},
        
        // Configuration Data
        {DataValidationType::AggroCalculateConfig, "AggroCalculateConfig"},
        {DataValidationType::MonsterSetConfig, "MonsterSetConfig"}
    };
    
    auto it = typeNames.find(validationType);
    if (it != typeNames.end()) {
        return it->second;
    }
    return "Unknown";
}

/**
 * @brief Convert DataValidationResult enum to string for logging
 * @param result The validation result
 * @return String representation of the result
 */
std::string DataValidationResultToString(DataValidationResult result) {
    switch (result) {
        case DataValidationResult::Success:
            return "Success";
        case DataValidationResult::Failure:
            return "Failure";
        case DataValidationResult::DataNotLoaded:
            return "DataNotLoaded";
        case DataValidationResult::InvalidData:
            return "InvalidData";
        case DataValidationResult::CorruptedData:
            return "CorruptedData";
        case DataValidationResult::MissingRecords:
            return "MissingRecords";
        case DataValidationResult::ValidationError:
            return "ValidationError";
        case DataValidationResult::SecurityError:
            return "SecurityError";
        default:
            return "Unknown";
    }
}

/**
 * @brief Legacy data table validation function
 * @param recordData Pointer to record data
 * @param tableName Name of the table for logging
 * @return true if validation succeeds
 */
bool CMainThreadCheckLoadedData::ValidateDataTable_Legacy(CRecordData* recordData, const char* tableName) {
    if (!recordData) {
        std::cout << "[ERROR] " << tableName << " is null" << std::endl;
        return false;
    }
    
    if (!CRecordData_IsValid(recordData)) {
        std::cout << "[ERROR] " << tableName << " is invalid" << std::endl;
        return false;
    }
    
    uint32_t recordCount = CRecordData_GetRecordNum(recordData);
    if (recordCount == 0) {
        std::cout << "[ERROR] " << tableName << " has no records" << std::endl;
        return false;
    }
    
    std::cout << "[INFO] " << tableName << " validated: " << recordCount << " records" << std::endl;
    return true;
}

/**
 * @brief Legacy manager system validation function
 * @param manager Pointer to manager instance
 * @param systemName Name of the system for logging
 * @return true if validation succeeds
 */
bool CMainThreadCheckLoadedData::CheckManagerSystem_Legacy(const void* manager, const char* systemName) {
    if (!manager) {
        std::cout << "[ERROR] " << systemName << " is null" << std::endl;
        return false;
    }
    
    std::cout << "[INFO] " << systemName << " validated" << std::endl;
    return true;
}
