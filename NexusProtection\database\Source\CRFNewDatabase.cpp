/*
 * CRFNewDatabase.cpp - Modern Database Connection and Query Management System
 * Refactored from 0CRFNewDatabaseQEAAXZ_140485F80.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CRFNewDatabase.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include <cstdarg>

// External dependencies
extern void ServerProgramExit(const char* reason, int exitCode);

/**
 * CRFNewDatabase constructor
 */
CRFNewDatabase::CRFNewDatabase() 
    : m_hEnv(SQL_NULL_HENV)
    , m_hDbc(SQL_NULL_HDBC)
    , m_hStmtSelect(SQL_NULL_HSTMT)
    , m_hStmtUpdate(SQL_NULL_HSTMT)
    , m_bConnectionActive(false)
    , m_connectionState(EDBConnectionState::DISCONNECTED)
    , m_bReconnectFailExit(true)
    , m_nQueryCount(0)
    , m_nSuccessfulQueries(0)
    , m_nFailedQueries(0)
    , m_nReconnectCount(0)
    , m_lastErrorCode(EDBErrorCode::SUCCESS) {
    
    try {
        // Initialize logging
        m_pProcessLogW = std::make_unique<CLogFile>();
        m_pErrorLogW = std::make_unique<CLogFile>();
        m_pProcessLogA = std::make_unique<CLogFile>();
        m_pErrorLogA = std::make_unique<CLogFile>();
        
        // Initialize ODBC environment
        InitializeODBC();
        
        Log("CRFNewDatabase initialized successfully");
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "Constructor failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::MEMORY_ERROR;
    }
}

/**
 * CRFNewDatabase destructor
 */
CRFNewDatabase::~CRFNewDatabase() {
    try {
        Log("~CRFNewDatabase() : EndDataBase()");
        EndDataBase();
        CleanupODBC();
        
    } catch (const std::exception& e) {
        // Log error but don't throw from destructor
        if (m_pErrorLogA) {
            ErrFmtLog("Destructor error: %s", e.what());
        }
    }
}

/**
 * Initialize database connection
 */
bool CRFNewDatabase::Initialize(const DBConnectionParams& params) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    try {
        if (!ValidateConnectionParams(params)) {
            m_lastErrorMessage = "Invalid connection parameters";
            m_lastErrorCode = EDBErrorCode::INVALID_PARAMETER;
            return false;
        }
        
        m_connectionParams = params;
        m_connectionState = EDBConnectionState::DISCONNECTED;
        
        Log("Database initialization completed with parameters");
        return true;
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "Initialize failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
        ErrFmtLog("Initialize failed: %s", e.what());
        return false;
    }
}

/**
 * Start database connection
 */
bool CRFNewDatabase::StartDataBase(const std::string& odbcName, const std::string& accountName, const std::string& password) {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    try {
        if (m_bConnectionActive) {
            Log("Database already connected");
            return true;
        }
        
        m_connectionState = EDBConnectionState::CONNECTING;
        
        // Store connection parameters
        m_szOdbcName = odbcName;
        m_szAccountName = accountName;
        m_szPassword = password;
        
        // Allocate connection handle
        SQLRETURN ret = SQLAllocHandle(SQL_HANDLE_DBC, m_hEnv, &m_hDbc);
        if (!SQL_SUCCEEDED(ret)) {
            ErrorAction(ret);
            return false;
        }
        
        // Set connection timeout
        SQLSetConnectAttr(m_hDbc, SQL_LOGIN_TIMEOUT, (SQLPOINTER)30, 0);
        
        // Connect to database
        ret = SQLConnect(m_hDbc, 
                        (SQLCHAR*)odbcName.c_str(), SQL_NTS,
                        (SQLCHAR*)accountName.c_str(), SQL_NTS,
                        (SQLCHAR*)password.c_str(), SQL_NTS);
        
        if (!SQL_SUCCEEDED(ret)) {
            ErrorAction(ret);
            SQLFreeHandle(SQL_HANDLE_DBC, m_hDbc);
            m_hDbc = SQL_NULL_HDBC;
            m_connectionState = EDBConnectionState::ERROR;
            return false;
        }
        
        // Allocate statement handles
        if (!AllocateStatements()) {
            SQLDisconnect(m_hDbc);
            SQLFreeHandle(SQL_HANDLE_DBC, m_hDbc);
            m_hDbc = SQL_NULL_HDBC;
            m_connectionState = EDBConnectionState::ERROR;
            return false;
        }
        
        m_bConnectionActive = true;
        m_connectionState = EDBConnectionState::CONNECTED;
        
        Log("Database connection established successfully");
        return true;
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "StartDataBase failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
        ErrFmtLog("StartDataBase failed: %s", e.what());
        m_connectionState = EDBConnectionState::ERROR;
        return false;
    }
}

/**
 * End database connection
 */
void CRFNewDatabase::EndDataBase() {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    try {
        if (!m_bConnectionActive) {
            return;
        }
        
        m_connectionState = EDBConnectionState::DISCONNECTED;
        m_bConnectionActive = false;
        
        // Free statement handles
        FreeStatements();
        
        // Disconnect and free connection handle
        if (m_hDbc != SQL_NULL_HDBC) {
            SQLDisconnect(m_hDbc);
            SQLFreeHandle(SQL_HANDLE_DBC, m_hDbc);
            m_hDbc = SQL_NULL_HDBC;
        }
        
        Log("Database connection closed");
        
    } catch (const std::exception& e) {
        ErrFmtLog("EndDataBase error: %s", e.what());
    }
}

/**
 * Configure user ODBC connection
 */
bool CRFNewDatabase::ConfigUserODBC(const std::string& odbcName, const std::string& serverName, 
                                   const std::string& databaseName, bool trusted) {
    try {
        // This would typically configure ODBC data source
        // For now, we'll store the configuration parameters
        m_connectionParams.odbcName = odbcName;
        m_connectionParams.serverName = serverName;
        m_connectionParams.databaseName = databaseName;
        
        Log("ODBC configuration completed for: " + odbcName);
        return true;
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "ConfigUserODBC failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
        ErrFmtLog("ConfigUserODBC failed: %s", e.what());
        return false;
    }
}

/**
 * Execute select query
 */
DBQueryResult CRFNewDatabase::ExecSelectQuery(const std::string& query) {
    return ExecuteQuery(query, EDBQueryType::SELECT);
}

/**
 * Execute update query
 */
bool CRFNewDatabase::ExecUpdateQuery(const std::string& query, bool logQuery) {
    try {
        if (logQuery) {
            Log("Executing update query: " + query);
        }
        
        DBQueryResult result = ExecuteQuery(query, EDBQueryType::UPDATE);
        UpdateStats(result.bSuccess);
        
        return result.bSuccess;
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "ExecUpdateQuery failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::QUERY_FAILED;
        ErrFmtLog("ExecUpdateQuery failed: %s", e.what());
        UpdateStats(false);
        return false;
    }
}

/**
 * Execute stored procedure
 */
DBQueryResult CRFNewDatabase::ExecStoredProcedure(const std::string& procedureName, 
                                                const std::vector<std::string>& parameters) {
    try {
        std::string query = "{ CALL " + procedureName;
        
        if (!parameters.empty()) {
            query += "(";
            for (size_t i = 0; i < parameters.size(); ++i) {
                if (i > 0) query += ", ";
                query += parameters[i];
            }
            query += ")";
        }
        query += " }";
        
        Log("Executing stored procedure: " + query);
        return ExecuteQuery(query, EDBQueryType::STORED_PROCEDURE);
        
    } catch (const std::exception& e) {
        DBQueryResult result;
        result.bSuccess = false;
        result.errorCode = EDBErrorCode::QUERY_FAILED;
        result.errorMessage = "ExecStoredProcedure failed: " + std::string(e.what());
        ErrFmtLog("ExecStoredProcedure failed: %s", e.what());
        UpdateStats(false);
        return result;
    }
}

/**
 * Check if table exists
 */
bool CRFNewDatabase::TableExist(const std::string& tableName) {
    try {
        std::string query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '" + tableName + "'";
        DBQueryResult result = ExecSelectQuery(query);
        
        if (result.bSuccess && !result.resultData.empty() && !result.resultData[0].empty()) {
            int count = std::stoi(result.resultData[0][0]);
            return count > 0;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        ErrFmtLog("TableExist failed: %s", e.what());
        return false;
    }
}

/**
 * Check if connection is active
 */
bool CRFNewDatabase::IsConnectionActive() const {
    return m_bConnectionActive.load();
}

/**
 * Get current connection state
 */
EDBConnectionState CRFNewDatabase::GetConnectionState() const {
    return m_connectionState.load();
}

/**
 * Set log file path
 */
void CRFNewDatabase::SetLogFile(const std::string& logPath, const std::string& dbName) {
    std::lock_guard<std::mutex> lock(m_logMutex);

    try {
        m_szLogUpperPath = logPath;

        // Initialize log files with proper paths
        if (m_pProcessLogW) {
            std::string processLogPath = logPath + dbName + "_Process.log";
            // m_pProcessLogW->Initialize(processLogPath);
        }

        if (m_pErrorLogW) {
            std::string errorLogPath = logPath + dbName + "_Error.log";
            // m_pErrorLogW->Initialize(errorLogPath);
        }

        Log("Log files configured for database: " + dbName);

    } catch (const std::exception& e) {
        m_lastErrorMessage = "SetLogFile failed: " + std::string(e.what());
        ErrFmtLog("SetLogFile failed: %s", e.what());
    }
}

/**
 * Set reconnect fail exit flag
 */
void CRFNewDatabase::SetReconnectFailExitFlag(bool exitOnFail) {
    m_bReconnectFailExit = exitOnFail;
    Log(exitOnFail ? "Reconnect fail exit enabled" : "Reconnect fail exit disabled");
}

/**
 * Log message to database log
 */
void CRFNewDatabase::Log(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_logMutex);

    try {
        std::string formattedMessage = FormatLogMessage(message);

        if (m_pProcessLogA) {
            // m_pProcessLogA->Write(formattedMessage);
        }

        // Also log to system logger if available
        Logger::LogInfo("DB: " + formattedMessage);

    } catch (const std::exception& e) {
        // Avoid infinite recursion in logging
    }
}

/**
 * Log error message with formatting
 */
void CRFNewDatabase::ErrFmtLog(const char* format, ...) {
    std::lock_guard<std::mutex> lock(m_logMutex);

    try {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);

        std::string errorMessage = FormatLogMessage(std::string(buffer));
        m_lastErrorMessage = errorMessage;

        if (m_pErrorLogA) {
            // m_pErrorLogA->Write(errorMessage);
        }

        // Also log to system logger if available
        Logger::LogError("DB: " + errorMessage);

    } catch (const std::exception& e) {
        // Avoid infinite recursion in logging
    }
}

/**
 * Get last error message
 */
std::string CRFNewDatabase::GetLastError() const {
    return m_lastErrorMessage;
}

/**
 * Get connection statistics
 */
std::string CRFNewDatabase::GetConnectionStats() const {
    std::ostringstream stats;
    stats << "Database Statistics:\n";
    stats << "  Total Queries: " << m_nQueryCount.load() << "\n";
    stats << "  Successful: " << m_nSuccessfulQueries.load() << "\n";
    stats << "  Failed: " << m_nFailedQueries.load() << "\n";
    stats << "  Reconnects: " << m_nReconnectCount.load() << "\n";
    stats << "  Connection Active: " << (m_bConnectionActive.load() ? "Yes" : "No") << "\n";
    stats << "  Connection State: " << static_cast<int>(m_connectionState.load());

    return stats.str();
}

/**
 * Initialize ODBC environment
 */
bool CRFNewDatabase::InitializeODBC() {
    try {
        // Allocate environment handle
        SQLRETURN ret = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &m_hEnv);
        if (!SQL_SUCCEEDED(ret)) {
            m_lastErrorMessage = "Failed to allocate ODBC environment handle";
            m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
            return false;
        }

        // Set ODBC version
        ret = SQLSetEnvAttr(m_hEnv, SQL_ATTR_ODBC_VERSION, (SQLPOINTER)SQL_OV_ODBC3, 0);
        if (!SQL_SUCCEEDED(ret)) {
            SQLFreeHandle(SQL_HANDLE_ENV, m_hEnv);
            m_hEnv = SQL_NULL_HENV;
            m_lastErrorMessage = "Failed to set ODBC version";
            m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        m_lastErrorMessage = "InitializeODBC failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
        return false;
    }
}

/**
 * Cleanup ODBC resources
 */
void CRFNewDatabase::CleanupODBC() {
    try {
        FreeStatements();

        if (m_hDbc != SQL_NULL_HDBC) {
            SQLDisconnect(m_hDbc);
            SQLFreeHandle(SQL_HANDLE_DBC, m_hDbc);
            m_hDbc = SQL_NULL_HDBC;
        }

        if (m_hEnv != SQL_NULL_HENV) {
            SQLFreeHandle(SQL_HANDLE_ENV, m_hEnv);
            m_hEnv = SQL_NULL_HENV;
        }

    } catch (const std::exception& e) {
        ErrFmtLog("CleanupODBC error: %s", e.what());
    }
}

/**
 * Allocate statement handles
 */
bool CRFNewDatabase::AllocateStatements() {
    try {
        // Allocate select statement handle
        SQLRETURN ret = SQLAllocHandle(SQL_HANDLE_STMT, m_hDbc, &m_hStmtSelect);
        if (!SQL_SUCCEEDED(ret)) {
            ErrorAction(ret);
            return false;
        }

        // Allocate update statement handle
        ret = SQLAllocHandle(SQL_HANDLE_STMT, m_hDbc, &m_hStmtUpdate);
        if (!SQL_SUCCEEDED(ret)) {
            SQLFreeHandle(SQL_HANDLE_STMT, m_hStmtSelect);
            m_hStmtSelect = SQL_NULL_HSTMT;
            ErrorAction(ret);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        ErrFmtLog("AllocateStatements failed: %s", e.what());
        return false;
    }
}

/**
 * Free statement handles
 */
void CRFNewDatabase::FreeStatements() {
    try {
        if (m_hStmtSelect != SQL_NULL_HSTMT) {
            SQLFreeHandle(SQL_HANDLE_STMT, m_hStmtSelect);
            m_hStmtSelect = SQL_NULL_HSTMT;
        }

        if (m_hStmtUpdate != SQL_NULL_HSTMT) {
            SQLFreeHandle(SQL_HANDLE_STMT, m_hStmtUpdate);
            m_hStmtUpdate = SQL_NULL_HSTMT;
        }

    } catch (const std::exception& e) {
        ErrFmtLog("FreeStatements error: %s", e.what());
    }
}

/**
 * Handle ODBC error
 */
void CRFNewDatabase::ErrorAction(SQLRETURN sqlRet, SQLHSTMT hStmt) {
    try {
        if (sqlRet == SQL_INVALID_HANDLE) {
            ErrFmtLog("SQL_INVALID_HANDLE -> ReConnectDataBase()");
            if (!ReConnectDataBase() && m_bReconnectFailExit) {
                ServerProgramExit("CRFNewDatabase::ErrorAction : DB Connection Refused! ReConnectDataBase Fail!", 1);
            }
        } else if (sqlRet == SQL_ERROR || sqlRet == SQL_SUCCESS_WITH_INFO) {
            std::string errorMsg;

            if (hStmt != nullptr) {
                errorMsg = GetODBCError(SQL_HANDLE_STMT, hStmt);
            } else if (m_hDbc != SQL_NULL_HDBC) {
                errorMsg = GetODBCError(SQL_HANDLE_DBC, m_hDbc);
            } else if (m_hEnv != SQL_NULL_HENV) {
                errorMsg = GetODBCError(SQL_HANDLE_ENV, m_hEnv);
            }

            m_lastErrorMessage = errorMsg;
            m_lastErrorCode = EDBErrorCode::QUERY_FAILED;
            ErrFmtLog("ODBC Error: %s", errorMsg.c_str());

            // Check if we need to reconnect
            if (ShouldReconnect(sqlRet)) {
                ReConnectDataBase();
            }
        }

    } catch (const std::exception& e) {
        ErrFmtLog("ErrorAction failed: %s", e.what());
    }
}

/**
 * Get ODBC error information
 */
std::string CRFNewDatabase::GetODBCError(SQLSMALLINT handleType, SQLHANDLE handle) {
    try {
        SQLCHAR sqlState[6];
        SQLCHAR messageText[1024];
        SQLINTEGER nativeError;
        SQLSMALLINT textLength;

        SQLRETURN ret = SQLGetDiagRec(handleType, handle, 1, sqlState, &nativeError,
                                     messageText, sizeof(messageText), &textLength);

        if (SQL_SUCCEEDED(ret)) {
            std::ostringstream error;
            error << "SQLState: " << sqlState
                  << ", Native Error: " << nativeError
                  << ", Message: " << messageText;
            return error.str();
        }

        return "Unknown ODBC error";

    } catch (const std::exception& e) {
        return "Error retrieving ODBC error information: " + std::string(e.what());
    }
}

/**
 * Execute query with prepared statement
 */
DBQueryResult CRFNewDatabase::ExecuteQuery(const std::string& query, EDBQueryType queryType) {
    std::lock_guard<std::mutex> lock(m_queryMutex);
    DBQueryResult result;

    try {
        if (!m_bConnectionActive) {
            result.errorCode = EDBErrorCode::CONNECTION_FAILED;
            result.errorMessage = "Database not connected";
            return result;
        }

        m_nQueryCount++;

        // Choose appropriate statement handle
        SQLHSTMT hStmt = (queryType == EDBQueryType::SELECT) ? m_hStmtSelect : m_hStmtUpdate;

        // Execute the query
        SQLRETURN ret = SQLExecDirect(hStmt, (SQLCHAR*)query.c_str(), SQL_NTS);

        if (!SQL_SUCCEEDED(ret)) {
            ErrorAction(ret, hStmt);
            result.errorCode = EDBErrorCode::QUERY_FAILED;
            result.errorMessage = m_lastErrorMessage;
            return result;
        }

        // For SELECT queries, fetch results
        if (queryType == EDBQueryType::SELECT) {
            SQLSMALLINT columnCount;
            SQLNumResultCols(hStmt, &columnCount);

            // Fetch rows
            while (SQLFetch(hStmt) == SQL_SUCCESS) {
                std::vector<std::string> row;

                for (SQLSMALLINT i = 1; i <= columnCount; ++i) {
                    SQLCHAR buffer[1024];
                    SQLLEN indicator;

                    ret = SQLGetData(hStmt, i, SQL_C_CHAR, buffer, sizeof(buffer), &indicator);
                    if (SQL_SUCCEEDED(ret)) {
                        if (indicator == SQL_NULL_DATA) {
                            row.push_back("");
                        } else {
                            row.push_back(std::string((char*)buffer));
                        }
                    } else {
                        row.push_back("");
                    }
                }

                result.resultData.push_back(row);
            }
        } else {
            // For non-SELECT queries, get affected row count
            SQLLEN rowCount;
            SQLRowCount(hStmt, &rowCount);
            result.nRowsAffected = static_cast<int>(rowCount);
        }

        // Close cursor
        SQLCloseCursor(hStmt);

        result.bSuccess = true;
        result.errorCode = EDBErrorCode::SUCCESS;

        return result;

    } catch (const std::exception& e) {
        result.bSuccess = false;
        result.errorCode = EDBErrorCode::QUERY_FAILED;
        result.errorMessage = "ExecuteQuery failed: " + std::string(e.what());
        ErrFmtLog("ExecuteQuery failed: %s", e.what());
        return result;
    }
}

/**
 * Validate connection parameters
 */
bool CRFNewDatabase::ValidateConnectionParams(const DBConnectionParams& params) const {
    if (params.odbcName.empty()) {
        return false;
    }

    if (params.accountName.empty()) {
        return false;
    }

    if (params.connectionTimeout <= 0 || params.connectionTimeout > 300) {
        return false;
    }

    if (params.queryTimeout <= 0 || params.queryTimeout > 3600) {
        return false;
    }

    return true;
}

/**
 * Update connection statistics
 */
void CRFNewDatabase::UpdateStats(bool success) {
    if (success) {
        m_nSuccessfulQueries++;
    } else {
        m_nFailedQueries++;
    }
}

/**
 * Format log message with timestamp
 */
std::string CRFNewDatabase::FormatLogMessage(const std::string& message) const {
    try {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::ostringstream oss;
        oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        oss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        oss << " [DB] " << message;

        return oss.str();

    } catch (const std::exception& e) {
        return "[DB] " + message;  // Fallback without timestamp
    }
}

/**
 * Check if reconnection is needed
 */
bool CRFNewDatabase::ShouldReconnect(SQLRETURN sqlRet) const {
    return (sqlRet == SQL_INVALID_HANDLE ||
            sqlRet == SQL_ERROR ||
            !m_bConnectionActive.load());
}

/**
 * Reconnect to database
 */
bool CRFNewDatabase::ReConnectDataBase() {
    std::lock_guard<std::mutex> lock(m_connectionMutex);
    
    try {
        Log("Attempting database reconnection...");
        m_connectionState = EDBConnectionState::RECONNECTING;
        m_nReconnectCount++;
        
        // Close existing connection
        EndDataBase();
        
        // Wait a moment before reconnecting
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // Attempt to reconnect
        bool success = StartDataBase(m_szOdbcName, m_szAccountName, m_szPassword);
        
        if (success) {
            Log("Database reconnection successful");
        } else {
            ErrFmtLog("Database reconnection failed");
            if (m_bReconnectFailExit) {
                ServerProgramExit("CRFNewDatabase::ReConnectDataBase : DB Connection Refused! ReConnectDataBase Fail!", 1);
            }
        }
        
        return success;
        
    } catch (const std::exception& e) {
        m_lastErrorMessage = "ReConnectDataBase failed: " + std::string(e.what());
        m_lastErrorCode = EDBErrorCode::CONNECTION_FAILED;
        ErrFmtLog("ReConnectDataBase failed: %s", e.what());
        m_connectionState = EDBConnectionState::ERROR;
        return false;
    }
}
