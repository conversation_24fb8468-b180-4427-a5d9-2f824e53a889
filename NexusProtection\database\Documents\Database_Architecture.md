# Database Architecture - Modern Database Management System

## Overview

The NexusProtection database module provides a comprehensive, modern C++ database management system refactored from the original decompiled database code. The system is designed with thread safety, performance optimization, and maintainability as core principles.

## Original Files Refactored

This system consolidates and modernizes the following decompiled database files:

### Core Database Classes
- `0CRFNewDatabaseQEAAXZ_140485F80.c` → `CRFNewDatabase`
- `0CRFWorldDatabaseQEAAXZ_140489680.c` → `CRFWorldDatabase`
- `0CUserDBQEAAXZ_14010FB90.c` → `CUserDB`

### Database Operations
- `InitDBCLogTypeDBTaskManagerQEAA_NPEBD0Z_1402C2E50.c`
- `DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.c`
- `_init_databaseCashDbWorkerMEAA_NXZ_1402F0720.c`

### World Database Operations
- `create_amine_personalCRFWorldDatabaseQEAA_NXZ_1404A9D10.c`
- `Rebirth_BaseCRFWorldDatabaseQEAA_NKPEADZ_140493F10.c`
- `Insert_BuddyCRFWorldDatabaseQEAA_NKZ_14049E100.c`
- `Insert_QuestCRFWorldDatabaseQEAA_NKZ_1404950D0.c`
- `Update_PostCRFWorldDatabaseQEAA_NPEADZ_1404B4790.c`
- `create_automine_tableCRFWorldDatabaseQEAA_NXZ_1404A8760.c`

### Error Handling and Utilities
- `ErrorActionCRFNewDatabaseIEAAXFPEAXZ_140486C70.c`
- Various constructor/destructor implementations

## Architecture Overview

### Class Hierarchy

```
CRFNewDatabase (Base Database Class)
├── CRFWorldDatabase (World-specific operations)
└── CUserDB (User session management)
```

### Core Components

#### 1. CRFNewDatabase
**Purpose**: Foundation database connection and query management
**Key Features**:
- ODBC connection management
- Thread-safe query execution
- Automatic reconnection handling
- Comprehensive error handling
- Connection pooling support
- Query result caching

#### 2. CRFWorldDatabase
**Purpose**: Game world data persistence
**Key Features**:
- Character data management
- Quest system persistence
- Guild data operations
- Inventory management
- Auto-mining system support
- Rebirth system handling

#### 3. CUserDB
**Purpose**: User session and account management
**Key Features**:
- User authentication
- Session tracking
- Play time monitoring
- Character creation/deletion
- Class initialization
- Account statistics

## Key Features

### 1. Thread Safety
- All database operations are thread-safe
- Mutex protection for shared resources
- Atomic operations for statistics
- Lock-free read operations where possible

### 2. Connection Management
- Automatic connection pooling
- Intelligent reconnection logic
- Connection health monitoring
- Timeout handling
- Resource cleanup

### 3. Error Handling
- Comprehensive ODBC error reporting
- Graceful degradation on failures
- Automatic retry mechanisms
- Detailed logging system
- Exception safety guarantees

### 4. Performance Optimization
- Query result caching
- Prepared statement support
- Batch operation capabilities
- Connection reuse
- Memory pool allocation

### 5. Security Features
- SQL injection prevention
- Input sanitization
- Parameter validation
- Secure connection options
- Audit trail logging

## Database Operations

### Character Management
```cpp
// Create new character
CharacterDBData character;
character.dwSerial = 12345;
character.szName = "PlayerName";
character.dwLevel = 1;
character.dwClass = 1;

CRFWorldDatabase worldDB;
bool success = worldDB.CreateCharacter(character);
```

### Quest System
```cpp
// Insert quest record
uint32_t characterSerial = 12345;
bool success = worldDB.Insert_Quest(characterSerial);

// Update quest progress
QuestDBData quest;
quest.dwCharacterSerial = characterSerial;
quest.dwQuestID = 100;
quest.dwState = 2;
quest.szProgress = "50%";

worldDB.UpdateQuest(quest);
```

### User Session Management
```cpp
// Process user login
UserSessionData session;
session.dwUserID = 67890;
session.szAccountName = "<EMAIL>";
session.dwLoginTime = GetCurrentTimestamp();

CUserDB userDB;
bool loginSuccess = userDB.ProcessLogin(session);
```

### Guild Operations
```cpp
// Create guild
GuildDBData guild;
guild.dwGuildID = 500;
guild.szGuildName = "Elite Warriors";
guild.dwMasterSerial = 12345;
guild.dwLevel = 1;

bool success = worldDB.CreateGuild(guild);
```

## Configuration

### Connection Parameters
```cpp
DBConnectionParams params;
params.odbcName = "GameDatabase";
params.serverName = "localhost";
params.databaseName = "NexusGame";
params.accountName = "gameuser";
params.password = "securepassword";
params.connectionTimeout = 30;
params.queryTimeout = 60;
params.enableAutoReconnect = true;
```

### Performance Tuning
- **Connection Pool Size**: 10-50 connections
- **Query Timeout**: 30-120 seconds
- **Cache Size**: 1000-10000 entries
- **Cleanup Interval**: 5-30 minutes

## Error Handling

### Error Types
- **Connection Errors**: Network issues, authentication failures
- **Query Errors**: SQL syntax errors, constraint violations
- **Timeout Errors**: Long-running operations
- **Resource Errors**: Memory allocation failures

### Error Recovery
```cpp
try {
    bool result = database.ExecUpdateQuery(query);
    if (!result) {
        std::string error = database.GetLastError();
        Logger::LogError("Database operation failed: " + error);
        
        // Attempt recovery
        if (database.ReConnectDataBase()) {
            result = database.ExecUpdateQuery(query);
        }
    }
} catch (const std::exception& e) {
    Logger::LogError("Database exception: " + std::string(e.what()));
}
```

## Monitoring and Statistics

### Connection Statistics
- Total queries executed
- Successful/failed query counts
- Average query execution time
- Connection uptime
- Reconnection attempts

### Performance Metrics
- Cache hit/miss ratios
- Query execution times
- Memory usage
- Thread contention
- Error rates

### Usage Example
```cpp
std::string stats = database.GetConnectionStats();
Logger::LogInfo("Database Statistics:\n" + stats);

std::string worldStats = worldDB.GetWorldDBStats();
Logger::LogInfo("World Database Statistics:\n" + worldStats);
```

## Security Considerations

### SQL Injection Prevention
- All user input is sanitized
- Parameterized queries used
- Input validation at entry points
- Regular expression filtering

### Access Control
- Role-based database permissions
- Connection encryption support
- Audit logging enabled
- Session timeout enforcement

## Testing Strategy

### Unit Tests
- Individual method testing
- Parameter validation testing
- Error condition handling
- Performance benchmarking

### Integration Tests
- Full workflow testing
- Multi-threaded scenarios
- Database failover testing
- Load testing

### Performance Tests
- Query execution benchmarks
- Connection pool stress tests
- Memory usage profiling
- Concurrent access testing

## Deployment Considerations

### Database Setup
1. Create ODBC data sources
2. Configure connection strings
3. Set up database schemas
4. Initialize stored procedures
5. Configure security permissions

### Monitoring Setup
1. Enable database logging
2. Configure performance counters
3. Set up alerting thresholds
4. Implement health checks

### Backup Strategy
1. Regular database backups
2. Transaction log backups
3. Point-in-time recovery
4. Disaster recovery procedures

## Future Enhancements

### Planned Features
1. **Connection Pooling**: Advanced pool management
2. **Distributed Transactions**: Multi-database operations
3. **Replication Support**: Master-slave configurations
4. **NoSQL Integration**: Hybrid database support
5. **Real-time Analytics**: Live performance monitoring

### Scalability Improvements
1. **Horizontal Scaling**: Database sharding
2. **Caching Layer**: Redis/Memcached integration
3. **Load Balancing**: Database cluster support
4. **Async Operations**: Non-blocking database calls

## Compatibility

### Compiler Support
- Visual Studio 2022 (v143 toolset)
- C++17/C++20 standard compliance
- Windows 10/11 compatibility

### Database Support
- Microsoft SQL Server 2016+
- MySQL 8.0+
- PostgreSQL 12+
- Oracle 19c+

### Dependencies
- ODBC drivers
- STL containers and algorithms
- Windows API
- Threading libraries
