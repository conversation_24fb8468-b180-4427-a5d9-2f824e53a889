#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>

// Forward declarations
class CMonster;
class CRFMonsterAIMgr;
class UsStateTBL;
class Us_HFSM;

/**
 * @brief Monster AI Creation and Management System
 * 
 * This class handles the creation and initialization of AI systems for monsters.
 * It manages the integration between monsters and the AI state machine system,
 * providing a clean interface for AI creation and configuration.
 * 
 * Refactored from:
 * - CreateAICMonsterQEAAHHZ_1401423D0.c (Address: 0x1401423D0)
 * 
 * Key responsibilities:
 * - AI type validation and creation
 * - State table retrieval and configuration
 * - HFSM (Hierarchical Finite State Machine) setup
 * - Fallback handling for AI creation failures
 * - Integration with CRFMonsterAIMgr singleton
 */
class MonsterAICreator {
public:
    /**
     * @brief AI creation result codes
     */
    enum class AICreationResult : int32_t {
        Success = 1,           // AI created successfully
        Failed = 0,            // AI creation failed
        InvalidType = -1,      // Invalid AI type specified
        InvalidMonster = -2,   // Invalid monster pointer
        ManagerNotAvailable = -3,  // AI manager not available
        StateTableNotFound = -4,   // State table not found for AI type
        HFSMSetupFailed = -5      // HFSM setup failed
    };

    /**
     * @brief AI type definitions
     */
    enum class AIType : int32_t {
        None = 0,              // No AI
        Basic = 1,             // Basic monster AI
        Aggressive = 2,        // Aggressive behavior
        Defensive = 3,         // Defensive behavior
        Patrol = 4,            // Patrol behavior
        Boss = 5,              // Boss monster AI
        Elite = 6,             // Elite monster AI
        Support = 7,           // Support/healing AI
        Custom = 8             // Custom AI type
    };

    /**
     * @brief AI creation configuration
     */
    struct AICreationConfig {
        AIType aiType = AIType::Basic;
        bool enableFallback = true;        // Enable fallback to default states
        bool resetEmotionState = true;     // Reset emotion state on creation
        bool resetCombatState = true;      // Reset combat state on creation
        uint32_t timeoutMs = 5000;         // Timeout for AI creation
        
        AICreationConfig() = default;
        AICreationConfig(AIType type, bool fallback = true) 
            : aiType(type), enableFallback(fallback) {}
    };

    /**
     * @brief AI creation statistics
     */
    struct AICreationStats {
        uint64_t totalCreationAttempts = 0;
        uint64_t successfulCreations = 0;
        uint64_t failedCreations = 0;
        uint64_t fallbackActivations = 0;
        std::unordered_map<AIType, uint64_t> typeUsageCount;
        
        double GetSuccessRate() const {
            return totalCreationAttempts > 0 ? 
                   static_cast<double>(successfulCreations) / totalCreationAttempts : 0.0;
        }
    };

public:
    MonsterAICreator() = default;
    ~MonsterAICreator() = default;

    // Non-copyable but movable
    MonsterAICreator(const MonsterAICreator&) = delete;
    MonsterAICreator& operator=(const MonsterAICreator&) = delete;
    MonsterAICreator(MonsterAICreator&&) = default;
    MonsterAICreator& operator=(MonsterAICreator&&) = default;

    /**
     * @brief Create AI for a monster
     * @param monster Pointer to the monster
     * @param aiType Type of AI to create
     * @return AI creation result code
     */
    AICreationResult CreateAI(CMonster* monster, AIType aiType);

    /**
     * @brief Create AI for a monster with configuration
     * @param monster Pointer to the monster
     * @param config AI creation configuration
     * @return AI creation result code
     */
    AICreationResult CreateAI(CMonster* monster, const AICreationConfig& config);

    /**
     * @brief Validate AI type
     * @param aiType AI type to validate
     * @return true if AI type is valid, false otherwise
     */
    static bool IsValidAIType(AIType aiType);

    /**
     * @brief Convert AI type to integer
     * @param aiType AI type
     * @return Integer representation of AI type
     */
    static int32_t AITypeToInt(AIType aiType);

    /**
     * @brief Convert integer to AI type
     * @param value Integer value
     * @return AI type
     */
    static AIType IntToAIType(int32_t value);

    /**
     * @brief Get AI creation statistics
     * @return Current statistics
     */
    const AICreationStats& GetStats() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStats();

    /**
     * @brief Set default fallback configuration
     * @param config Default configuration for fallback
     */
    void SetDefaultFallbackConfig(const AICreationConfig& config);

    /**
     * @brief Get default fallback configuration
     * @return Default fallback configuration
     */
    const AICreationConfig& GetDefaultFallbackConfig() const { return m_defaultFallbackConfig; }

private:
    /**
     * @brief Validate monster for AI creation
     * @param monster Pointer to the monster
     * @return true if monster is valid, false otherwise
     */
    bool ValidateMonster(CMonster* monster) const;

    /**
     * @brief Get AI manager instance
     * @return Pointer to AI manager, nullptr if not available
     */
    CRFMonsterAIMgr* GetAIManager() const;

    /**
     * @brief Retrieve state table for AI type
     * @param aiManager Pointer to AI manager
     * @param aiType AI type
     * @return Pointer to state table, nullptr if not found
     */
    UsStateTBL* GetStateTable(CRFMonsterAIMgr* aiManager, AIType aiType) const;

    /**
     * @brief Setup HFSM for monster
     * @param monster Pointer to the monster
     * @param stateTable Pointer to state table
     * @return true if setup successful, false otherwise
     */
    bool SetupHFSM(CMonster* monster, UsStateTBL* stateTable) const;

    /**
     * @brief Apply fallback configuration
     * @param monster Pointer to the monster
     * @param config AI creation configuration
     * @return AI creation result
     */
    AICreationResult ApplyFallback(CMonster* monster, const AICreationConfig& config);

    /**
     * @brief Update statistics
     * @param result AI creation result
     * @param aiType AI type that was attempted
     */
    void UpdateStats(AICreationResult result, AIType aiType);

    /**
     * @brief Log AI creation attempt
     * @param monster Pointer to the monster
     * @param aiType AI type
     * @param result Creation result
     */
    void LogCreationAttempt(CMonster* monster, AIType aiType, AICreationResult result) const;

private:
    AICreationStats m_stats;                           // Creation statistics
    AICreationConfig m_defaultFallbackConfig;         // Default fallback configuration
};

/**
 * @brief Utility functions for Monster AI Creation
 */
namespace MonsterAICreatorUtils {
    /**
     * @brief Convert AI creation result to string
     * @param result AI creation result
     * @return String representation
     */
    std::string AICreationResultToString(MonsterAICreator::AICreationResult result);

    /**
     * @brief Convert AI type to string
     * @param aiType AI type
     * @return String representation
     */
    std::string AITypeToString(MonsterAICreator::AIType aiType);

    /**
     * @brief Get recommended AI type for monster
     * @param monster Pointer to the monster
     * @return Recommended AI type
     */
    MonsterAICreator::AIType GetRecommendedAIType(CMonster* monster);

    /**
     * @brief Validate AI creation configuration
     * @param config Configuration to validate
     * @return true if configuration is valid, false otherwise
     */
    bool ValidateAICreationConfig(const MonsterAICreator::AICreationConfig& config);

    /**
     * @brief Format AI creation statistics
     * @param stats Statistics to format
     * @return Formatted string
     */
    std::string FormatAICreationStats(const MonsterAICreator::AICreationStats& stats);

    /**
     * @brief Create default AI configuration for monster type
     * @param monsterType Type of monster
     * @return Default AI configuration
     */
    MonsterAICreator::AICreationConfig CreateDefaultConfigForMonsterType(int32_t monsterType);
}
