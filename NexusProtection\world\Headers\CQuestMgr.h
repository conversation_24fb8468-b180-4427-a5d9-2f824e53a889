/*
 * CQuestMgr.h - Quest Manager System
 * Refactored for Visual Studio 2022 compatibility
 * Original: Various CQuestMgr functions from decompiled source
 */

#pragma once

#include "QuestStructures.h"
#include "MapStructures.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>

// Forward declarations
class CPlayer;
class CRecordData;
class CPlayerDB;
struct _base_fld;

/**
 * Quest Manager class - handles all quest-related operations
 */
class CQuestMgr {
public:
    // Static quest data tables
    static CRecordData* s_tblQuest;
    static CRecordData* s_tblQuestHappenEvent;
    
    /**
     * Constructor
     */
    CQuestMgr();
    
    /**
     * Destructor
     */
    ~CQuestMgr();
    
    /**
     * Initialize quest manager
     * @param pMaster Pointer to player who owns this quest manager
     * @param pQuestData Pointer to quest database
     */
    void InitMgr(CPlayer* pMaster, QuestDBBase* pQuestData);
    
    /**
     * Check NPC quest list for available quests
     * Refactored from CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.c
     * @param pszEventCode Event code to check
     * @param byRaceCode Race code for race-specific quests
     * @param pQuestIndexData Output structure for quest indices
     */
    void CheckNPCQuestList(const char* pszEventCode, uint8_t byRaceCode, 
                          NPCQuestIndexTempData* pQuestIndexData);
    
    /**
     * Check if NPC quest is completed
     * @param pszCode Quest code to check
     * @param bQuestRepeat Whether quest is repeatable
     * @return true if quest is completed
     */
    bool IsCompleteNpcQuest(const char* pszCode, int bQuestRepeat);
    
    /**
     * Check if NPC quest is in progress
     * @param pszCode Quest code to check
     * @return true if quest is in progress
     */
    bool IsProcNpcQuest(const char* pszCode);
    
    /**
     * Check if linked NPC quest is in progress
     * @param pszCode Quest code to check
     * @param dwLinkIndex Link index
     * @return true if linked quest is in progress
     */
    bool IsProcLinkNpcQuest(const char* pszCode, uint32_t dwLinkIndex);
    
    /**
     * Check if repeatable NPC quest is possible
     * @param pszCode Quest code to check
     * @param dwRepeatIndex Repeat index
     * @return true if repeat is possible
     */
    bool IsPossibleRepeatNpcQuest(const char* pszCode, uint32_t dwRepeatIndex);
    
    /**
     * Check NPC quest startable conditions
     * @param pszEventCode Event code
     * @param byRaceCode Race code
     * @param dwQuestIndex Quest index
     * @param dwHappenIndex Happen index
     * @return Pointer to happen event container if startable
     */
    QuestHappenEventContainer* CheckNPCQuestStartable(const char* pszEventCode, 
                                                     uint8_t byRaceCode,
                                                     uint32_t dwQuestIndex, 
                                                     uint32_t dwHappenIndex);
    
    /**
     * Check quest happen event
     * @param happenType Type of happen event
     * @param pszEventCode Event code
     * @param byRaceCode Race code
     * @return Pointer to happen event container
     */
    QuestHappenEventContainer* CheckQuestHappenEvent(QuestHappenType happenType,
                                                    const char* pszEventCode,
                                                    uint8_t byRaceCode);
    
    /**
     * Get last happen event
     * @return Pointer to last happen event container
     */
    QuestHappenEventContainer* GetLastHappenEvent();
    
    /**
     * Return quest item
     * @param pszItemCode Item code
     * @param nEndReturnItemCnt Item count
     * @param byQuestDBSlot Quest DB slot
     * @param bCheckOnly Whether to only check without actually returning
     * @return true if successful
     */
    bool ReturnItem(const char* pszItemCode, int nEndReturnItemCnt, 
                   uint8_t byQuestDBSlot, bool bCheckOnly);
    
    /**
     * Delete quest item
     * @param pszItemCode Item code
     * @param wCount Item count
     * @return true if successful
     */
    bool DeleteQuestItem(const char* pszItemCode, uint16_t wCount);
    
    // Getters
    CPlayer* GetMaster() const { return m_pMaster; }
    QuestDBBase* GetQuestData() const { return m_pQuestData; }
    uint32_t GetOldTimeoutCheckTime() const { return m_dwOldTimeoutChecktime; }
    
    // Setters
    void SetOldTimeoutCheckTime(uint32_t time) { m_dwOldTimeoutChecktime = time; }
    
private:
    // Member variables
    CPlayer* m_pMaster;                                         // Player who owns this quest manager
    QuestDBBase* m_pQuestData;                                  // Quest database
    QuestHappenEventContainer m_LastHappenEvent;                // Last happen event
    std::array<QuestHappenEventContainer, 3> m_pTempHappenEvent; // Temporary happen events
    uint32_t m_dwOldTimeoutChecktime;                           // Old timeout check time
    
    // Quest condition checking cache
    mutable std::unordered_map<std::string, bool> m_conditionCache;
    mutable std::chrono::system_clock::time_point m_lastCacheCleanup;
    
    /**
     * Check quest condition
     * @param pCond Condition node to check
     * @return true if condition is met
     */
    bool CheckCondition(const QuestConditionNode* pCond);
    
    /**
     * Static condition checker (for compatibility)
     * @param pQuestMgr Quest manager instance
     * @param pCond Condition node to check
     * @return true if condition is met
     */
    static bool _CheckCondition(CQuestMgr* pQuestMgr, const QuestConditionNode* pCond);
    
    /**
     * Validate quest prerequisites
     * @param pQuestField Quest field data
     * @return true if prerequisites are met
     */
    bool ValidateQuestPrerequisites(const _base_fld* pQuestField);
    
    /**
     * Process single quest event
     * @param pEventField Event field data
     * @param byRaceCode Race code
     * @param pQuestIndexData Output quest index data
     * @param questCount Current quest count
     * @return Updated quest count
     */
    int ProcessSingleQuestEvent(const _base_fld* pEventField, uint8_t byRaceCode,
                               NPCQuestIndexTempData* pQuestIndexData, int questCount);
    
    /**
     * Validate event conditions
     * @param pEventNode Event node data
     * @return true if all conditions are met
     */
    bool ValidateEventConditions(const QuestHappenEventNode* pEventNode);
    
    /**
     * Check level requirements
     * @param pQuestField Quest field data
     * @return true if level requirements are met
     */
    bool CheckLevelRequirements(const _base_fld* pQuestField);
    
    /**
     * Check quest completion status
     * @param pQuestField Quest field data
     * @return true if quest can be started
     */
    bool CheckQuestCompletionStatus(const _base_fld* pQuestField);
    
    /**
     * Clean condition cache
     */
    void CleanConditionCache() const;
    
    /**
     * Generate cache key for condition
     * @param pCond Condition node
     * @return Cache key string
     */
    std::string GenerateConditionCacheKey(const QuestConditionNode* pCond) const;
    
    /**
     * Log quest operation
     * @param operation Operation description
     * @param questCode Quest code (optional)
     * @param success Whether operation was successful
     */
    void LogQuestOperation(const std::string& operation, 
                          const std::string& questCode = "", 
                          bool success = true) const;
    
    /**
     * Validate input parameters
     * @param pszEventCode Event code
     * @param pQuestIndexData Quest index data
     * @return true if parameters are valid
     */
    bool ValidateInputParameters(const char* pszEventCode, 
                                const NPCQuestIndexTempData* pQuestIndexData) const;
    
    /**
     * Initialize happen event containers
     */
    void InitializeHappenEventContainers();
    
    /**
     * Cleanup expired temporary events
     */
    void CleanupExpiredTempEvents();
    
    // Disable copy constructor and assignment operator
    CQuestMgr(const CQuestMgr&) = delete;
    CQuestMgr& operator=(const CQuestMgr&) = delete;
    
    // Enable move constructor and assignment operator
    CQuestMgr(CQuestMgr&&) = default;
    CQuestMgr& operator=(CQuestMgr&&) = default;
};

/**
 * Quest utility functions
 */
namespace QuestUtils {
    /**
     * Validate quest code format
     * @param questCode Quest code to validate
     * @return true if format is valid
     */
    bool IsValidQuestCode(const char* questCode);
    
    /**
     * Validate event code format
     * @param eventCode Event code to validate
     * @return true if format is valid
     */
    bool IsValidEventCode(const char* eventCode);
    
    /**
     * Get race-specific data offset
     * @param raceCode Race code
     * @return Data offset for race
     */
    size_t GetRaceDataOffset(uint8_t raceCode);
    
    /**
     * Format quest condition for logging
     * @param pCond Condition node
     * @return Formatted string
     */
    std::string FormatConditionForLog(const QuestConditionNode* pCond);
    
    /**
     * Calculate condition node address
     * @param baseAddr Base address
     * @param index Condition index
     * @return Calculated address
     */
    const QuestConditionNode* CalculateConditionNodeAddress(const char* baseAddr, int index);
}

#endif // CQUESTMGR_H
