/**
 * @file CMainThread_NetworkInit.cpp
 * @brief Network initialization implementation for CMainThread
 * 
 * Refactored from NetworkInitCMainThreadAEAA_NXZ_1401EB330.c
 * This implements the network initialization system.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/CMainThread_NetworkInit.h"
#include "../Headers/CMainThread.h"
#include <iostream>
#include <format>
#include <cstring>
#include <Windows.h>

// External function declarations (to be properly linked)
extern void MyMessageBox(const char* title, const char* format, ...);
extern uint64_t _security_cookie;

// External global objects
extern CNetWorking g_Network;

// External class methods (to be properly linked)
extern bool CNetWorking_SetNetSystem(CNetWorking* networking, uint32_t lineCount, 
                                     _NET_TYPE_PARAM* params, const char* serverName, 
                                     const char* logPath);
extern void CMainThread_AddPassablePacket(class CMainThread* mainThread);
extern bool CMainThread_IsReleaseServiceMode(class CMainThread* mainThread);

// Static line registry initialization
std::unordered_map<NetworkLineType, NetworkLineConfig> CMainThreadNetworkInit::s_lineRegistry;

/**
 * @brief Constructor for _NET_TYPE_PARAM
 */
_NET_TYPE_PARAM::_NET_TYPE_PARAM() {
    memset(m_szModuleName, 0, sizeof(m_szModuleName));
    m_wSocketMaxNum = 1;
    m_byRecvThreadNum = 1;
    m_byRecvSleepTime = 1;
    m_dwSendBufferSize = 1000000;
    m_dwRecvBufferSize = 1000000;
    m_bServer = true;
    m_bRealSockCheck = false;
    m_bSystemLogFile = true;
    m_bSvrToS = true;
    m_bOddMsgWriteLog = true;
    m_bAnSyncConnect = true;
}

/**
 * @brief Copy constructor for _NET_TYPE_PARAM
 */
_NET_TYPE_PARAM::_NET_TYPE_PARAM(const _NET_TYPE_PARAM& other) {
    *this = other;
}

/**
 * @brief Assignment operator for _NET_TYPE_PARAM
 */
_NET_TYPE_PARAM& _NET_TYPE_PARAM::operator=(const _NET_TYPE_PARAM& other) {
    if (this != &other) {
        memcpy(m_szModuleName, other.m_szModuleName, sizeof(m_szModuleName));
        m_wSocketMaxNum = other.m_wSocketMaxNum;
        m_byRecvThreadNum = other.m_byRecvThreadNum;
        m_byRecvSleepTime = other.m_byRecvSleepTime;
        m_dwSendBufferSize = other.m_dwSendBufferSize;
        m_dwRecvBufferSize = other.m_dwRecvBufferSize;
        m_bServer = other.m_bServer;
        m_bRealSockCheck = other.m_bRealSockCheck;
        m_bSystemLogFile = other.m_bSystemLogFile;
        m_bSvrToS = other.m_bSvrToS;
        m_bOddMsgWriteLog = other.m_bOddMsgWriteLog;
        m_bAnSyncConnect = other.m_bAnSyncConnect;
    }
    return *this;
}

/**
 * @brief Constructor
 */
CMainThreadNetworkInit::CMainThreadNetworkInit() {
    m_initStats.Reset();
    InitializeLineRegistry();
}

/**
 * @brief Destructor
 */
CMainThreadNetworkInit::~CMainThreadNetworkInit() {
    // Cleanup if needed
}

/**
 * @brief Initialize the line registry with all network line configurations
 */
void CMainThreadNetworkInit::InitializeLineRegistry() {
    if (!s_lineRegistry.empty()) {
        return; // Already initialized
    }
    
    // Client Line Configuration
    s_lineRegistry[NetworkLineType::ClientLine] = NetworkLineConfig("ClientLine", "Client connection line", 27780, 2532);
    
    // Account Line Configuration  
    s_lineRegistry[NetworkLineType::AccountLine] = NetworkLineConfig("AccountLine", "Account management line", 27555, 2);
    
    // Web Line Configuration
    s_lineRegistry[NetworkLineType::WebLine] = NetworkLineConfig("WebLine", "Web service line", 27556, 1);
    
    // Billing Line Configuration
    s_lineRegistry[NetworkLineType::BillingLine] = NetworkLineConfig("BillingLine", "Billing service line", 0, 1);
}

/**
 * @brief Main network initialization function
 * 
 * Modern C++20 implementation of the original NetworkInit function.
 * Initializes all network systems in the correct order with comprehensive error handling.
 * 
 * @param mainThread Pointer to CMainThread instance for accessing network systems
 * @return NetworkInitResult indicating success or failure
 */
NetworkInitResult CMainThreadNetworkInit::InitializeNetworkSystems(CMainThread* mainThread) {
    try {
        m_initStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting network system initialization..." << std::endl;
        
        // Setup network system with all lines
        if (auto result = SetupNetworkSystem(mainThread); result != NetworkInitResult::Success) {
            return result;
        }
        
        // Add passable packets
        if (auto result = AddPassablePackets(mainThread); result != NetworkInitResult::Success) {
            return result;
        }
        
        // Finalize initialization statistics
        m_initStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return NetworkInitResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Network system initialization completed successfully in {}ms", 
                                m_initStats.GetTotalInitTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} lines)", 
                                m_initStats.GetSuccessRate(), 
                                m_initStats.successfulLines) << std::endl;
        
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during network initialization: {}", e.what()));
        m_initStats.endTime = std::chrono::steady_clock::now();
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Legacy NetworkInit function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: char __fastcall CMainThread::NetworkInit(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return char (1 for success, 0 for failure)
 */
char CMainThreadNetworkInit::NetworkInit_Legacy(CMainThread* mainThread) {
    try {
        CMainThreadNetworkInit networkInit;
        NetworkInitResult result = networkInit.InitializeNetworkSystems(mainThread);
        return (result == NetworkInitResult::Success) ? 1 : 0;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NetworkInit_Legacy: " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Setup network system with all configured lines
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::SetupNetworkSystem(CMainThread* mainThread) {
    try {
        std::cout << "[INFO] Setting up network system..." << std::endl;
        
        // Create network parameters for all lines (equivalent to original vector constructor iterator)
        std::array<_NET_TYPE_PARAM, 4> networkParams;
        
        // Initialize all parameters
        for (auto& param : networkParams) {
            param = _NET_TYPE_PARAM(); // Default initialization
        }
        
        // Configure Client Line (index 0)
        auto clientConfig = GetLineConfig(NetworkLineType::ClientLine);
        networkParams[0] = CreateNetTypeParam(clientConfig);
        ConfigureServiceMode(mainThread, networkParams[0]);
        
        // Configure Account Line (index 1)
        auto accountConfig = GetLineConfig(NetworkLineType::AccountLine);
        networkParams[1] = CreateNetTypeParam(accountConfig);
        
        // Configure Web Line (index 2)
        auto webConfig = GetLineConfig(NetworkLineType::WebLine);
        networkParams[2] = CreateNetTypeParam(webConfig);
        
        // Configure Billing Line (index 3)
        auto billingConfig = GetLineConfig(NetworkLineType::BillingLine);
        networkParams[3] = CreateNetTypeParam(billingConfig);
        
        // Call CNetWorking::SetNetSystem (equivalent to original call)
        if (!SetNetSystem_Legacy(&g_Network, 4, networkParams.data(), "GameServer", "..\\ZoneServerLog\\NetLog")) {
            SetLastError("CNetWorking::SetNetSystem failed");
            return NetworkInitResult::NetworkSystemFailed;
        }
        
        // Mark all lines as successfully initialized
        LogLineInitialization(NetworkLineType::ClientLine, true);
        LogLineInitialization(NetworkLineType::AccountLine, true);
        LogLineInitialization(NetworkLineType::WebLine, true);
        LogLineInitialization(NetworkLineType::BillingLine, true);
        
        std::cout << "[INFO] Network system setup completed successfully" << std::endl;
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during network system setup: {}", e.what()));
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Add passable packets to the network system
 * @param mainThread Pointer to CMainThread instance
 * @return NetworkInitResult
 */
NetworkInitResult CMainThreadNetworkInit::AddPassablePackets(CMainThread* mainThread) {
    try {
        std::cout << "[INFO] Adding passable packets..." << std::endl;
        
        // Call legacy AddPassablePacket function
        AddPassablePacket_Legacy(mainThread);
        
        std::cout << "[INFO] Passable packets added successfully" << std::endl;
        return NetworkInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during passable packet setup: {}", e.what()));
        return NetworkInitResult::Failure;
    }
}

/**
 * @brief Create network type parameter from configuration
 * @param config Network line configuration
 * @return _NET_TYPE_PARAM structure
 */
_NET_TYPE_PARAM CMainThreadNetworkInit::CreateNetTypeParam(const NetworkLineConfig& config) {
    _NET_TYPE_PARAM param;
    
    // Set module name
    strncpy_s(param.m_szModuleName, sizeof(param.m_szModuleName), config.name.c_str(), _TRUNCATE);
    
    // Set configuration parameters
    param.m_wSocketMaxNum = config.maxConnections;
    param.m_byRecvThreadNum = config.recvThreadNum;
    param.m_byRecvSleepTime = config.recvSleepTime;
    param.m_dwSendBufferSize = config.sendBufferSize;
    param.m_dwRecvBufferSize = config.recvBufferSize;
    param.m_bServer = config.isServer;
    param.m_bRealSockCheck = config.realSockCheck;
    param.m_bSystemLogFile = config.systemLogFile;
    param.m_bSvrToS = config.svrToS;
    param.m_bOddMsgWriteLog = config.oddMsgWriteLog;
    param.m_bAnSyncConnect = config.anSyncConnect;
    
    return param;
}

/**
 * @brief Configure service mode specific parameters
 * @param mainThread Pointer to CMainThread instance
 * @param param Network parameter to configure
 */
void CMainThreadNetworkInit::ConfigureServiceMode(CMainThread* mainThread, _NET_TYPE_PARAM& param) {
    // Check if in release service mode (equivalent to original IsReleaseServiceMode check)
    if (IsReleaseServiceMode_Legacy(mainThread)) {
        // Adjust parameters for release mode
        param.m_byRecvSleepTime = 8; // Original v10 = 8
        // Additional release mode configurations would go here
    }
}
