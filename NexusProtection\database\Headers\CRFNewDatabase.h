#pragma once

#include <string>
#include <memory>
#include <vector>
#include <functional>
#include <mutex>
#include <atomic>
#include <windows.h>
#include <sql.h>
#include <sqlext.h>

// Forward declarations
class CLogFile;

/**
 * Database connection states
 */
enum class EDBConnectionState : int {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    ERROR = 3,
    RECONNECTING = 4
};

/**
 * Database query types
 */
enum class EDBQueryType : int {
    SELECT = 0,
    UPDATE = 1,
    INSERT = 2,
    DELETE = 3,
    STORED_PROCEDURE = 4
};

/**
 * Database error codes
 */
enum class EDBErrorCode : int {
    SUCCESS = 0,
    CONNECTION_FAILED = 1,
    QUERY_FAILED = 2,
    INVALID_HANDLE = 3,
    TIMEOUT = 4,
    MEMORY_ERROR = 5,
    INVALID_PARAMETER = 6
};

/**
 * Database query result structure
 */
struct DBQueryResult {
    bool bSuccess;
    EDBErrorCode errorCode;
    std::string errorMessage;
    int nRowsAffected;
    std::vector<std::vector<std::string>> resultData;
    
    DBQueryResult() 
        : bSuccess(false), errorCode(EDBErrorCode::SUCCESS), 
          nRowsAffected(0) {}
};

/**
 * Database connection parameters
 */
struct DBConnectionParams {
    std::string odbcName;
    std::string serverName;
    std::string databaseName;
    std::string accountName;
    std::string password;
    int connectionTimeout;
    int queryTimeout;
    bool enableAutoReconnect;
    
    DBConnectionParams() 
        : connectionTimeout(30), queryTimeout(60), 
          enableAutoReconnect(true) {}
};

/**
 * CRFNewDatabase - Modern database connection and query management system
 * Refactored from 0CRFNewDatabaseQEAAXZ_140485F80.c
 */
class CRFNewDatabase {
public:
    /**
     * Constructor
     */
    CRFNewDatabase();
    
    /**
     * Virtual destructor
     */
    virtual ~CRFNewDatabase();
    
    /**
     * Initialize database connection
     * @param params Connection parameters
     * @return true if initialization successful
     */
    bool Initialize(const DBConnectionParams& params);
    
    /**
     * Start database connection
     * @param odbcName ODBC data source name
     * @param accountName Database account name
     * @param password Database password
     * @return true if connection successful
     */
    bool StartDataBase(const std::string& odbcName, const std::string& accountName, const std::string& password);
    
    /**
     * End database connection
     */
    void EndDataBase();
    
    /**
     * Configure user ODBC connection
     * @param odbcName ODBC data source name
     * @param serverName Database server name
     * @param databaseName Database name
     * @param trusted Use trusted connection
     * @return true if configuration successful
     */
    bool ConfigUserODBC(const std::string& odbcName, const std::string& serverName, 
                       const std::string& databaseName, bool trusted = false);
    
    /**
     * Execute select query
     * @param query SQL query string
     * @return Query result
     */
    DBQueryResult ExecSelectQuery(const std::string& query);
    
    /**
     * Execute update query
     * @param query SQL query string
     * @param logQuery Whether to log the query
     * @return true if query successful
     */
    bool ExecUpdateQuery(const std::string& query, bool logQuery = true);
    
    /**
     * Execute stored procedure
     * @param procedureName Stored procedure name
     * @param parameters Procedure parameters
     * @return Query result
     */
    DBQueryResult ExecStoredProcedure(const std::string& procedureName, 
                                    const std::vector<std::string>& parameters = {});
    
    /**
     * Check if table exists
     * @param tableName Table name to check
     * @return true if table exists
     */
    bool TableExist(const std::string& tableName);
    
    /**
     * Reconnect to database
     * @return true if reconnection successful
     */
    bool ReConnectDataBase();
    
    /**
     * Check if connection is active
     * @return true if connection is active
     */
    bool IsConnectionActive() const;
    
    /**
     * Get current connection state
     * @return Current connection state
     */
    EDBConnectionState GetConnectionState() const;
    
    /**
     * Set log file path
     * @param logPath Log file directory path
     * @param dbName Database name for log file naming
     */
    void SetLogFile(const std::string& logPath, const std::string& dbName);
    
    /**
     * Set reconnect fail exit flag
     * @param exitOnFail Whether to exit on reconnect failure
     */
    void SetReconnectFailExitFlag(bool exitOnFail);
    
    /**
     * Log message to database log
     * @param message Message to log
     */
    void Log(const std::string& message);
    
    /**
     * Log error message with formatting
     * @param format Error message format
     * @param ... Format arguments
     */
    void ErrFmtLog(const char* format, ...);
    
    /**
     * Get last error message
     * @return Last error message
     */
    std::string GetLastError() const;
    
    /**
     * Get connection statistics
     * @return Connection statistics as string
     */
    std::string GetConnectionStats() const;

protected:
    // ODBC handles
    SQLHENV m_hEnv;                     // Environment handle
    SQLHDBC m_hDbc;                     // Connection handle
    SQLHSTMT m_hStmtSelect;             // Select statement handle
    SQLHSTMT m_hStmtUpdate;             // Update statement handle
    
    // Connection state
    std::atomic<bool> m_bConnectionActive;
    std::atomic<EDBConnectionState> m_connectionState;
    std::atomic<bool> m_bReconnectFailExit;
    
    // Connection parameters
    DBConnectionParams m_connectionParams;
    std::string m_szOdbcName;
    std::string m_szAccountName;
    std::string m_szPassword;
    std::string m_szLogUpperPath;
    
    // Logging
    std::unique_ptr<CLogFile> m_pProcessLogW;
    std::unique_ptr<CLogFile> m_pErrorLogW;
    std::unique_ptr<CLogFile> m_pProcessLogA;
    std::unique_ptr<CLogFile> m_pErrorLogA;
    
    // Thread safety
    mutable std::mutex m_connectionMutex;
    mutable std::mutex m_queryMutex;
    mutable std::mutex m_logMutex;
    
    // Statistics
    std::atomic<uint64_t> m_nQueryCount;
    std::atomic<uint64_t> m_nSuccessfulQueries;
    std::atomic<uint64_t> m_nFailedQueries;
    std::atomic<uint64_t> m_nReconnectCount;
    
    // Error handling
    std::string m_lastErrorMessage;
    EDBErrorCode m_lastErrorCode;
    
    /**
     * Initialize ODBC environment
     * @return true if initialization successful
     */
    bool InitializeODBC();
    
    /**
     * Cleanup ODBC resources
     */
    void CleanupODBC();
    
    /**
     * Allocate statement handles
     * @return true if allocation successful
     */
    bool AllocateStatements();
    
    /**
     * Free statement handles
     */
    void FreeStatements();
    
    /**
     * Handle ODBC error
     * @param sqlRet SQL return code
     * @param hStmt Statement handle
     */
    void ErrorAction(SQLRETURN sqlRet, SQLHSTMT hStmt = nullptr);
    
    /**
     * Get ODBC error information
     * @param handleType Handle type
     * @param handle Handle
     * @return Error message
     */
    std::string GetODBCError(SQLSMALLINT handleType, SQLHANDLE handle);
    
    /**
     * Execute query with prepared statement
     * @param query SQL query
     * @param queryType Query type
     * @return Query result
     */
    DBQueryResult ExecuteQuery(const std::string& query, EDBQueryType queryType);
    
    /**
     * Validate connection parameters
     * @param params Parameters to validate
     * @return true if parameters are valid
     */
    bool ValidateConnectionParams(const DBConnectionParams& params) const;
    
    /**
     * Update connection statistics
     * @param success Whether the operation was successful
     */
    void UpdateStats(bool success);
    
    /**
     * Format log message with timestamp
     * @param message Message to format
     * @return Formatted message
     */
    std::string FormatLogMessage(const std::string& message) const;
    
    /**
     * Check if reconnection is needed
     * @param sqlRet SQL return code
     * @return true if reconnection is needed
     */
    bool ShouldReconnect(SQLRETURN sqlRet) const;
};
