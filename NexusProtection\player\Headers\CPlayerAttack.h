/*
 * CPlayerAttack.h - Player Attack System
 * Refactored for Visual Studio 2022 compatibility
 * Original: 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.c, AttackSkillCPlayerAttackQEAAXPEAU_attack_param_NZ_14016E140.c
 */

#pragma once

#include "CMonsterAttack.h"
#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>

// Forward declarations
class CPlayer;
class CCharacter;
class CMonster;
class CMonsterSkill;
class CAttack;
class CGameObject;
struct _UnitPart_fld;
struct _MASTERY_PARAM;

/**
 * Player attack type enumeration
 */
enum class PlayerAttackType : int32_t {
    General = 0,                    // General attack
    Skill = 1,                      // Skill attack
    Unit = 2,                       // Unit attack
    Force = 3,                      // Force attack
    Siege = 4,                      // Siege attack
    SelfDestruction = 5,            // Self destruction
    Test = 6                        // Test attack
};

/**
 * Player attack result enumeration
 */
enum class PlayerAttackResult : int32_t {
    Success = 1,                    // Attack successful
    Failed = 0,                     // Attack failed
    InvalidTarget = -1,             // Invalid target
    InvalidSkill = -2,              // Invalid skill
    NotAttackable = -3,             // Target not attackable
    OutOfRange = -4,                // Target out of range
    InsufficientResources = -5,     // Insufficient resources
    Stunned = -6,                   // Player is stunned
    InSafeZone = -7,                // Target in safe zone
    InvalidWeapon = -8              // Invalid weapon
};

/**
 * Player attack statistics
 */
struct PlayerAttackStatistics {
    uint32_t totalAttacks;          // Total attacks performed
    uint32_t successfulAttacks;     // Successful attacks
    uint32_t failedAttacks;         // Failed attacks
    uint32_t criticalHits;          // Critical hits
    uint32_t skillAttacks;          // Skill-based attacks
    uint32_t generalAttacks;        // General attacks
    uint32_t unitAttacks;           // Unit attacks
    uint32_t siegeAttacks;          // Siege attacks
    uint32_t totalDamageDealt;      // Total damage dealt
    uint32_t pvpAttacks;            // PvP attacks
    uint32_t pveAttacks;            // PvE attacks
    
    PlayerAttackStatistics();
    void Reset();
    float GetSuccessRate() const;
    float GetCriticalRate() const;
    float GetPvPRatio() const;
};

/**
 * Unit part field structure for unit attacks
 */
struct _UnitPart_fld {
    int m_nWPType;                  // Weapon type
    int m_nGAMinAF;                 // General attack minimum AF
    int m_nGAMaxAF;                 // General attack maximum AF
    int m_nGAMinSelProb;            // General attack minimum selection probability
    int m_nGAMaxSelProb;            // General attack maximum selection probability
    float m_fAttackRange;           // Attack range
    uint32_t m_dwIndex;             // Unit index
    
    _UnitPart_fld();
    void Reset();
    bool IsValid() const;
};

/**
 * CPlayerAttack class for player-specific attack functionality
 */
class CPlayerAttack : public CAttack {
public:
    /**
     * Constructor
     * @param pPlayer Attacking player
     */
    CPlayerAttack(CCharacter* pPlayer);
    
    /**
     * Destructor
     */
    ~CPlayerAttack() override;
    
    /**
     * Perform player skill attack
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @return true if attack successful
     */
    bool AttackSkill(_attack_param* pParam, bool bUseEffBullet = false);
    
    /**
     * Perform player unit attack
     * @param pParam Attack parameters
     * @return true if attack successful
     */
    bool AttackUnit(_attack_param* pParam);
    
    /**
     * Perform player force attack
     * @param pParam Attack parameters
     * @param pTarget Target character
     * @param fArea Target area coordinates
     * @param nPart Attack part
     * @return true if attack successful
     */
    bool AttackForce(_attack_param* pParam, CCharacter* pTarget, float* fArea, int nPart);
    
    /**
     * Perform player siege attack
     * @param pParam Attack parameters
     * @param pTarget Target character
     * @param fArea Target area coordinates
     * @param nPart Attack part
     * @return true if attack successful
     */
    bool AttackSiege(_attack_param* pParam, CCharacter* pTarget, float* fArea, int nPart);
    
    /**
     * Perform player general attack
     * @param pParam Attack parameters
     * @param pTarget Target character
     * @param nPart Attack part
     * @param nGrade Attack grade
     * @param bMustMiss Force miss flag
     * @return true if attack successful
     */
    bool AttackGeneral(_attack_param* pParam, CCharacter* pTarget, int nPart, int nGrade, bool bMustMiss = false);
    
    /**
     * Perform self destruction attack
     * @return true if attack successful
     */
    bool AttackSelfDestruction();
    
    /**
     * Perform test attack
     * @param nPart Attack part
     * @param nGrade Attack grade
     * @param nAttPower Attack power
     * @param fArea Target area coordinates
     * @return true if attack successful
     */
    bool AttackTest(int nPart, int nGrade, int nAttPower, float* fArea);
    
    /**
     * Get attacking player
     * @return Pointer to attacking player
     */
    CPlayer* GetAttackingPlayer() const { return m_pAttPlayer; }
    
    /**
     * Set attacking player
     * @param pPlayer Attacking player
     */
    void SetAttackingPlayer(CPlayer* pPlayer) { m_pAttPlayer = pPlayer; }
    
    /**
     * Get player attack statistics
     * @return Reference to attack statistics
     */
    const PlayerAttackStatistics& GetPlayerStatistics() const { return m_playerStats; }
    
    /**
     * Reset player attack statistics
     */
    void ResetPlayerStatistics() { m_playerStats.Reset(); }

public:
    // Member variables
    CPlayer* m_pAttPlayer;                      // Attacking player

protected:
    PlayerAttackStatistics m_playerStats;       // Player attack statistics
    
    /**
     * Calculate player-specific damage
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return Calculated damage
     */
    int CalculateDamage(CCharacter* pTarget, _attack_param* pParam) override;
    
    /**
     * Calculate skill attack points
     * @param bUseEffBullet Use effect bullet flag
     * @return Calculated attack points
     */
    int _CalcSkillAttPnt(bool bUseEffBullet);
    
    /**
     * Apply player-specific effects
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return true if effects applied successfully
     */
    bool ApplyPlayerEffects(CCharacter* pTarget, _attack_param* pParam);
    
    /**
     * Check player attack conditions
     * @param pParam Attack parameters
     * @return true if conditions are met
     */
    bool CheckPlayerAttackConditions(_attack_param* pParam) const;
    
    /**
     * Process skill damage based on skill type
     * @param pParam Attack parameters
     * @param nAttPower Attack power
     * @param nAttPnt Attack points
     * @param bUseEffBullet Use effect bullet flag
     * @return true if processing successful
     */
    bool ProcessSkillDamage(_attack_param* pParam, int nAttPower, int nAttPnt, bool bUseEffBullet);
    
    /**
     * Process unit damage calculations
     * @param pParam Attack parameters
     * @param nAttPower Attack power
     * @return true if processing successful
     */
    bool ProcessUnitDamage(_attack_param* pParam, int nAttPower);
    
    /**
     * Calculate unit attack modifiers
     * @return Total attack modifier
     */
    float CalculateUnitAttackModifiers();
    
    /**
     * Check dodge and avoidance
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return true if attack hits
     */
    bool CheckDodgeAndAvoidance(CCharacter* pTarget, _attack_param* pParam);
    
    /**
     * Apply PvP modifiers
     * @param nAttPower Attack power reference
     * @param nAttPnt Attack points reference
     */
    void ApplyPvPModifiers(int& nAttPower, int& nAttPnt);
    
    /**
     * Apply boss type modifiers
     * @param nAttPower Attack power reference
     * @param nAttPnt Attack points reference
     */
    void ApplyBossTypeModifiers(int& nAttPower, int& nAttPnt);
    
    /**
     * Update player attack statistics
     * @param attackType Type of attack performed
     * @param success Whether attack was successful
     * @param damage Damage dealt
     * @param critical Whether attack was critical
     */
    void UpdatePlayerStatistics(PlayerAttackType attackType, bool success, int damage, bool critical);
};

/**
 * Player attack parameter generation namespace
 */
namespace PlayerAttackParams {
    /**
     * Generate general attack parameters for player
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param nPart Attack part
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeGeneralAttackParam(CPlayer* pPlayer, CCharacter* pTarget, int nPart, _attack_param* pAttackParam);
    
    /**
     * Generate skill attack parameters for player
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pSkillFld Skill field data
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeSkillAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pSkillFld, _attack_param* pAttackParam);
    
    /**
     * Generate unit attack parameters for player
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pWeaponFld Weapon field data
     * @param fAddBulletFc Additional bullet factor
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeUnitAttackParam(CPlayer* pPlayer, CCharacter* pTarget, _UnitPart_fld* pWeaponFld, 
                            float fAddBulletFc, _attack_param* pAttackParam);
    
    /**
     * Generate force attack parameters for player
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pForceFld Force field data
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeForceAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pForceFld, _attack_param* pAttackParam);
    
    /**
     * Generate siege attack parameters for player
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pSiegeFld Siege field data
     * @param nPart Attack part
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeSiegeAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pSiegeFld, 
                             int nPart, _attack_param* pAttackParam);
    
    /**
     * Generate weapon active force attack parameters
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pWeaponFld Weapon field data
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeWPActiveForceAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pWeaponFld, _attack_param* pAttackParam);
    
    /**
     * Generate weapon active skill attack parameters
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @param pWeaponFld Weapon field data
     * @param pAttackParam Output attack parameters
     * @return true if generation successful
     */
    bool MakeWPActiveSkillAttackParam(CPlayer* pPlayer, CCharacter* pTarget, void* pWeaponFld, _attack_param* pAttackParam);
}

/**
 * Player attack utility functions
 */
namespace PlayerAttackUtils {
    /**
     * Convert PlayerAttackResult to string
     * @param result Attack result
     * @return String representation
     */
    std::string AttackResultToString(PlayerAttackResult result);
    
    /**
     * Convert PlayerAttackType to string
     * @param type Attack type
     * @return String representation
     */
    std::string AttackTypeToString(PlayerAttackType type);
    
    /**
     * Check if target is valid for player attack
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @return true if target is valid
     */
    bool IsValidPlayerTarget(CPlayer* pPlayer, CCharacter* pTarget);
    
    /**
     * Calculate player attack range
     * @param pPlayer Attacking player
     * @param attackType Type of attack
     * @return Attack range
     */
    float CalculatePlayerAttackRange(CPlayer* pPlayer, PlayerAttackType attackType);
    
    /**
     * Check if player can perform attack type
     * @param pPlayer Attacking player
     * @param attackType Type of attack
     * @return true if attack type is available
     */
    bool CanPerformAttackType(CPlayer* pPlayer, PlayerAttackType attackType);
    
    /**
     * Get player weapon class
     * @param pPlayer Player to check
     * @return Weapon class
     */
    int GetPlayerWeaponClass(CPlayer* pPlayer);
    
    /**
     * Calculate player critical hit probability
     * @param pPlayer Attacking player
     * @param pTarget Target character
     * @return Critical hit probability (0.0 to 1.0)
     */
    float CalculatePlayerCriticalProbability(CPlayer* pPlayer, CCharacter* pTarget);
}

// External function declarations (to be properly linked)
extern float ffloor(float value);
extern int rand();
extern float GetSqrt(float* pos1, float* pos2);
extern int s_nLimitDist[];
extern int s_nLimitAngle[][8];
extern int s_nLimitRadius[];

#endif // CPLAYERATTACK_H
