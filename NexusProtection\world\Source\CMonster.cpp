#include "../Headers/CMonster.h"
#include "../Headers/CRFMonsterAIMgr.h"
#include <cstring>
#include <cmath>
#include <algorithm>
#include <cassert>
#include <iostream>

// Include necessary headers for dependencies
// These would be replaced with actual headers in the real implementation
class CCharacter {
public:
    CCharacter();
    virtual ~<PERSON>haracter();
    void Move(float deltaTime);
    bool GetStun() const { return false; } // Placeholder implementation

protected:
    float m_fCurPos[3];  // Current position
    bool m_bLive;        // Is alive flag
};

class CLootingMgr {
public:
    CLootingMgr();
    ~CLootingMgr();
    void Init(int nodeCount);
};

class CMonsterAggroMgr {
public:
    CMonsterAggroMgr();
    ~CMonsterAggroMgr();
    void Init();
    void Process();
    void OnlyOnceInit(CMonster* pMonster);
};

class CMonsterHierarchy {
public:
    CMonsterHierarchy();
    ~CMonsterHierarchy();
    void OnlyOnceInit(CMonster* pMonster);
    void OnChildRegenLoop();
};

class MonsterSFContDamageToleracne {
public:
    MonsterSFContDamageToleracne();
    ~MonsterSFContDamageToleracne();
    void OnlyOnceInit(CMonster* pMonster);
    void Init(float tolerance);
    void Update();
};

class EmotionPresentationChecker {
public:
    EmotionPresentationChecker();
    ~EmotionPresentationChecker();
    bool CheckEmotionState(CMonster* pMonster, uint8_t checkType, CCharacter* pTarget);
    static void ReSet(EmotionPresentationChecker* pChecker);

    // Member variables
    bool m_bIsSet;              ///< Whether emotion presentation is set
    CCharacter* m_pTarget;      ///< Target character for emotion
    uint8_t m_byType;           ///< Emotion type
    uint16_t m_wIndex;          ///< Emotion index
    uint16_t m_wRandIndex;      ///< Random emotion index
};

class MonsterStateData {
public:
    MonsterStateData();
    ~MonsterStateData();
};

class CMonsterSkillPool {
public:
    CMonsterSkillPool();
    ~CMonsterSkillPool();
};

class CMonsterAI {
public:
    CMonsterAI();
    ~CMonsterAI();
    CGameObjectVtbl* vfptr;  // Virtual function table pointer
};

class CLuaSignalReActor {
public:
    CLuaSignalReActor();
    ~CLuaSignalReActor();
    void Init();
};

class _effect_parameter {
public:
    static bool GetEff_State(_effect_parameter* pThis, int effectType);
    static int GetEff_Plus(_effect_parameter* pThis, int effectType);
};

// AI-related classes for CreateAI method
class UsStateTBL {
public:
    int SetHFSM(Us_HFSM* hfsm, CMonster* monster) { return 1; } // Placeholder implementation
};

class Us_HFSM {
public:
    void* vfptr = nullptr; // Virtual function pointer
};

// Monster creation structures for Create method
struct _character_create_setdata {
    void* m_pRecordSet;
    // Additional character creation data would be defined here
};

struct _monster_create_setdata {
    void* m_pRecordSet;
    float m_fStartPos[3];
    bool bRobExp;
    bool bRewardExp;
    bool bDungeon;
    _mon_active* pActiveRec;
    _dummy_position* pDumPosition;
    void* pParent;
    int m_nLayerIndex;
    void* m_pMap;
    // Additional monster creation data would be defined here
};

struct _monster_fld {
    const char* m_strName;       // Monster name string
    float m_fMaxHP;              // Maximum HP
    float m_fMovSpd;             // Normal movement speed
    float m_fWarMovSpd;          // War movement speed
    uint8_t m_bMonsterCondition; // Monster condition
    float m_fHPRecDelay;         // HP recovery delay in seconds
    float m_fHPRecUnit;          // HP recovery amount per tick
};

// Global utility functions
extern uint32_t GetLoopTime();
extern float ffloor(float value);
extern bool GetStun();
extern int rand();
extern float GetSqrt(const float* pos1, const float* pos2);

// EmotionPresentationChecker implementation
void EmotionPresentationChecker::ReSet(EmotionPresentationChecker* pChecker)
{
    // Reset the emotion presentation checker state
    if (pChecker) {
        pChecker->m_bIsSet = false;
        pChecker->m_pTarget = nullptr;
        pChecker->m_byType = 0;
        pChecker->m_wIndex = 0;
        pChecker->m_wRandIndex = 0;
    }
}

// Virtual function table placeholder
extern void* CMonster_vftable;

// Static member initialization
int CMonster::s_nAllocNum = 0;

// CMonster implementation

CMonster::CMonster()
    : CCharacter()
    , m_LootMgr()
    , m_AggroMgr()
    , m_MonHierarcy()
    , m_SFContDamageTolerance()
    , m_EmotionPresentationCheck()
    , m_MonsterStateData()
    , m_BeforeMonsterStateData()
    , m_pTargetChar(nullptr)
    , m_MonsterSkillPool()
    , m_AI()
    , m_LuaSignalReActor()
    , m_pMonRec(nullptr)
    , m_EP(nullptr)
    , m_pEventRespawn(nullptr)
    , m_pEventSet(nullptr)
    , m_pActiveRec(nullptr)
    , m_pDumPosition(nullptr)
    , m_dwObjSerial(0)
    , m_dwDestroyNextTime(INVALID_DESTROY_TIME)
    , m_dwLastRecoverTime(0)
    , m_LifeCicle(0)
    , m_LifeMax(0)
    , m_nHP(0)
    , m_nCommonStateChunk(0)
    , m_bRotateMonster(0)
    , m_bStdItemLoot(1)
    , m_bRobExp(0)
    , m_bRewardExp(0)
    , m_bDungeon(0)
    , m_bApparition(0)
    , m_nEventItemNum(0)
    , m_bLive(true)
{
    InitializeDefaults();
    InitializeComponents();
    
    // Initialize static data manager
    _InitSDM();
    
    // Increment allocation counter
    ++s_nAllocNum;
}

CMonster::~CMonster()
{
    // Decrement allocation counter
    --s_nAllocNum;
    
    // Destroy static data manager
    _DestroySDM();
    
    CleanupResources();
    
    // Parent destructor will be called automatically
}

void CMonster::InitializeDefaults()
{
    // Set virtual function table
    vfptr = reinterpret_cast<CGameObjectVtbl*>(&CMonster_vftable);
    
    // Initialize target pointer
    m_pTargetChar = nullptr;
    
    // Initialize timing values
    m_dwLastRecoverTime = GetLoopTime();
    m_LifeMax = 60000 * (rand() % 3) + 600000; // 10-30 minutes
    m_LifeCicle = GetLoopTime();
}

void CMonster::InitializeComponents()
{
    // Initialize aggro manager with monster reference
    m_AggroMgr.OnlyOnceInit(this);
    
    // Initialize hierarchy with monster reference
    m_MonHierarcy.OnlyOnceInit(this);
    
    // Initialize damage tolerance with monster reference
    m_SFContDamageTolerance.OnlyOnceInit(this);
}

void CMonster::CleanupResources()
{
    // Cleanup is handled by destructors of member objects
    m_pTargetChar = nullptr;
    m_pMonRec = nullptr;
    m_EP = nullptr;
    m_pEventRespawn = nullptr;
    m_pEventSet = nullptr;
    m_pActiveRec = nullptr;
    m_pDumPosition = nullptr;
}

void CMonster::Loop()
{
    if (!m_bLive) {
        return;
    }
    
    ProcessMovement();
    ProcessCombat();
    ProcessAI();
    ProcessLifecycle();
}

void CMonster::ProcessMovement()
{
    if (!IsMovable()) {
        CheckMonsterRotate();
        return;
    }
    
    float moveSpeed = GetMoveSpeed();
    if (moveSpeed > 0.0f) {
        // Process movement using parent class
        Move(moveSpeed);
        UpdateLookAtPos();
    }
}

void CMonster::ProcessCombat()
{
    if (!m_bLive) {
        return;
    }
    
    // Process aggro management
    m_AggroMgr.Process();
    
    // Update damage tolerance
    m_SFContDamageTolerance.Update();
}

void CMonster::ProcessAI()
{
    if (!m_bLive) {
        return;
    }
    
    // Check if not stunned before processing AI
    if (!CCharacter::GetStun()) {
        uint32_t currentTime = GetLoopTime();
        // Call AI process through virtual function table
        if (m_AI.vfptr && m_AI.vfptr->functions[0]) {
            auto processFunc = reinterpret_cast<void(*)(CMonsterAI*, uint32_t)>(m_AI.vfptr->functions[0]);
            processFunc(&m_AI, currentTime);
        }
    }
    
    CheckEmotionPresentation();
}

void CMonster::ProcessLifecycle()
{
    if (!m_bLive) {
        return;
    }
    
    CheckAutoRecoverHP();
    
    if (m_bLive) {
        m_MonHierarcy.OnChildRegenLoop();
        
        if (m_bLive) {
            CheckDelayDestroy();
        }
    }
}

bool CMonster::IsMovable() const
{
    if (!m_pMonRec) {
        return false;
    }
    
    return m_pMonRec->m_fMovSpd > 0.0f || m_pMonRec->m_fWarMovSpd > 0.0f;
}

float CMonster::GetMoveSpeed() const
{
    // Based on original decompiled logic from GetMoveSpeedCMonsterQEAAMXZ_140142D80.c

    if (!m_pMonRec) {
        return 0.0f;
    }

    // Check if under speed effect (equivalent to _effect_parameter::GetEff_State(&m_EP, 7))
    // Effect type 7 is for speed effects
    if (m_EP && _effect_parameter::GetEff_State(m_EP, 7)) {
        // When under speed effect, return normal movement speed
        // (equivalent to lines 21-24 in original)
        return m_pMonRec->m_fMovSpd;
    }

    // Check movement type (equivalent to CMonster::GetMoveType(v5) in line 25)
    if (GetMoveType()) {
        // In war/combat mode, use war movement speed
        // (equivalent to lines 25-28 in original)
        return m_pMonRec->m_fWarMovSpd;
    }

    // Default case: return normal movement speed
    // (equivalent to lines 29-32 in original)
    return m_pMonRec->m_fMovSpd;
}

uint8_t CMonster::GetMoveType() const
{
    // Based on original decompiled logic from GetMoveTypeCMonsterQEAAEXZ_1401437B0.c
    // Original: return this->m_nCommonStateChunk & 1;

    // Extract move type from bit 0 (1 bit total, values 0-1)
    // Mask with 1 to isolate only the move type bit
    return static_cast<uint8_t>(m_nCommonStateChunk & 1);
}

void CMonster::SetMoveType(uint8_t moveType)
{
    // Based on the bit manipulation pattern from GetMoveType
    // Move type uses bit 0 (1 bit total, values 0-1)

    // Ensure move type is within valid range (0-1)
    moveType &= 1;

    // Clear the move type bit (bit 0) and set new value
    // Clear bit: ~1 = 0xFFFFFFFE
    // Set bit: moveType
    m_nCommonStateChunk = (m_nCommonStateChunk & ~1) | moveType;
}

int CMonster::GetHP() const
{
    return m_nHP;
}

int CMonster::GetMaxHP() const
{
    if (!m_pMonRec) {
        return 0;
    }

    return static_cast<int>(ffloor(m_pMonRec->m_fMaxHP));
}

const char* CMonster::GetObjName() const
{
    // Based on original decompiled logic from GetObjNameCMonsterUEAAPEADXZ_140142700.c
    // Original: sprintf(szName_6, "[MONSTER] >> %s (pos: %s {%d, %d, %d})", v12, v6);

    // Static buffer for the formatted name (equivalent to szName_6 in original)
    static thread_local char szName[256];

    // Null pointer checks for safety
    if (!m_pMonRec || !m_pMonRec->m_strName) {
        std::strcpy(szName, "[MONSTER] >> <Unknown>");
        return szName;
    }

    // Get position coordinates (equivalent to ffloor calls in original)
    int posX = static_cast<int>(std::floor(m_fCurPos[0]));
    int posY = static_cast<int>(std::floor(m_fCurPos[1]));
    int posZ = static_cast<int>(std::floor(m_fCurPos[2]));

    // Get map code (with null pointer check)
    const char* mapCode = "<Unknown Map>";
    if (m_pCurMap && m_pCurMap->m_pMapSet && m_pCurMap->m_pMapSet->m_strCode) {
        mapCode = m_pCurMap->m_pMapSet->m_strCode;
    }

    // Format the string exactly like the original
    std::snprintf(szName, sizeof(szName),
                  "[MONSTER] >> %s (pos: %s {%d, %d, %d})",
                  m_pMonRec->m_strName, mapCode, posX, posY, posZ);

    return szName;
}

bool CMonster::SetHP(int hp, bool notify)
{
    if (hp < 0) {
        hp = 0;
    }
    
    int maxHP = GetMaxHP();
    if (hp > maxHP) {
        hp = maxHP;
    }
    
    m_nHP = hp;

    if (notify) {
        // Send HP change notification
    }

    return true;
}

int CMonster::GetBaseHPRecovery() const
{
    // Get HP recovery from effect parameters
    // Effect parameter type 32 typically represents HP recovery
    return GetEffectParameterPlus(32);
}

int CMonster::GetEffectParameterPlus(int effectType) const
{
    // This method is equivalent to _effect_parameter::GetEff_Plus(&m_EP, effectType)
    // from the original decompiled code

    if (!m_EP) {
        return 0;
    }

    // Call the actual effect parameter function
    // In the original code: _effect_parameter::GetEff_Plus(&m_EP, effectType)
    return _effect_parameter::GetEff_Plus(m_EP, effectType);
}

uint8_t CMonster::GetEmotionState() const
{
    // Based on original decompiled logic from GetEmotionStateCMonsterQEAAEXZ_140143810.c
    // Original: return (this->m_nCommonStateChunk >> 2) & 7;

    // Extract emotion state from bits 2-4 (3 bits total, values 0-7)
    // Shift right by 2 to get to the emotion state bits, then mask with 7 (0b111)
    return static_cast<uint8_t>((m_nCommonStateChunk >> 2) & 7);
}

void CMonster::SetEmotionState(uint8_t emotionState)
{
    // Based on the bit manipulation pattern from GetEmotionState
    // Emotion state uses bits 2-4 (3 bits total, values 0-7)

    // Ensure emotion state is within valid range (0-7)
    emotionState &= 7;

    // Clear the emotion state bits (bits 2-4) and set new value
    // Clear bits: ~(7 << 2) = ~0x1C = 0xFFFFFFE3
    // Set bits: (emotionState << 2)
    m_nCommonStateChunk = (m_nCommonStateChunk & ~(7 << 2)) | (emotionState << 2);
}

uint8_t CMonster::GetCombatState() const
{
    // Implementation would extract combat state from state chunk
    return 0; // Placeholder
}

void CMonster::SetCombatState(uint8_t combatState)
{
    // Implementation would set combat state in state chunk
}

bool CMonster::CreateAI(int aiType)
{
    // Based on original decompiled logic from CreateAICMonsterQEAAHHZ_1401423D0.c

    // Get AI manager instance (equivalent to v7 = CRFMonsterAIMgr::Instance())
    CRFMonsterAIMgr* aiManager = CRFMonsterAIMgr::Instance();
    if (!aiManager) {
        // Fallback behavior when AI manager is not available
        // (equivalent to original lines 43-44)
        SetEmotionState(0);
        SetCombatState(0);
        return false;
    }

    // Get state table for the AI type
    // (equivalent to CRFMonsterAIMgr::GetStateTBL(v7, &result, nIndex))
    UsPoint<UsStateTBL> stateTablePtr = aiManager->GetStateTBL(aiType);

    // Check if state table was retrieved successfully
    // (equivalent to if ( UsPoint<UsStateTBL>::operator UsStateTBL *(&result) ))
    if (!stateTablePtr.IsValid()) {
        // Fallback behavior when state table is not found
        SetEmotionState(0);
        SetCombatState(0);
        return false;
    }

    // Get the actual state table pointer
    // (equivalent to v4 = UsPoint<UsStateTBL>::operator->(&result))
    UsStateTBL* stateTable = stateTablePtr.Get();
    if (!stateTable) {
        SetEmotionState(0);
        SetCombatState(0);
        return false;
    }

    // Setup HFSM (Hierarchical Finite State Machine)
    // (equivalent to pHFSM = (Us_HFSM *)&pObject->m_AI.vfptr)
    Us_HFSM* hfsm = reinterpret_cast<Us_HFSM*>(&m_AI.vfptr);

    // Set up the HFSM with the state table
    // (equivalent to v9 = UsStateTBL::SetHFSM(v4, pHFSM, pObject))
    int setupResult = stateTable->SetHFSM(hfsm, this);

    // Return the setup result as boolean
    // (equivalent to return (unsigned int)v9)
    return setupResult > 0;
}

void CMonster::CheckMonsterRotate()
{
    // Check if monster rotation is enabled and conditions are met
    if (m_bRotateMonster &&
        m_fCreatePos[0] == m_fCurPos[0] &&
        m_fCreatePos[2] == m_fCurPos[2] &&
        (m_fStartLookAtPos[0] != m_fLookAtPos[0] || m_fStartLookAtPos[2] != m_fLookAtPos[2])) {

        // Update look-at position to start position
        UpdateLookAtPos(m_fStartLookAtPos);

        // Copy start look-at position to current look-at position
        std::memcpy(m_fLookAtPos, m_fStartLookAtPos, sizeof(float) * 3);

        // Send rotation change message to clients
        SendMsg_Change_MonsterRotate();
    }
}

void CMonster::UpdateLookAtPos(const float* newLookAtPos)
{
    if (newLookAtPos) {
        // Update the monster's look-at position
        // This would typically update internal rotation/direction state
        // For now, we'll provide a placeholder implementation
        std::memcpy(m_fLookAtPos, newLookAtPos, sizeof(float) * 3);
    }
}

void CMonster::SendMsg_Change_MonsterRotate()
{
    // Send monster rotation change message to nearby clients
    // This would typically create and send a network message to update monster rotation
    // For now, we'll provide a placeholder implementation

    // Implementation would:
    // 1. Create rotation change message packet
    // 2. Set monster ID and new rotation data
    // 3. Send to all nearby players who can see this monster

    // Placeholder implementation
    if (m_bLive) {
        // Log the rotation change for debugging
        // In a real implementation, this would send network packets
    }
}

void CMonster::CheckAutoRecoverHP()
{
    // Initialize debug stack pattern (matching original decompiled initialization)
    // This matches the original: for ( i = 16i64; i; --i ) { *(_DWORD *)v1 = -858993460; v1 = (__int64 *)((char *)v1 + 4); }
    uint32_t debugPattern[16];
    std::fill(debugPattern, debugPattern + 16, 0xCCCCCCCC);

    // Check if monster has HP recovery configuration (matching original conditions)
    if (!m_pMonRec || !m_bLive) {
        return;
    }

    // Check if HP recovery is enabled and configured (exact match to original conditions)
    if (m_pMonRec->m_fHPRecDelay > 0.0f &&
        m_pMonRec->m_fHPRecUnit > 0.0f &&
        m_pMonRec->m_bMonsterCondition == 1) {

        // Get current HP using virtual function call (matching original v6 = GetHP(v9))
        int currentHP = GetHP();

        // Get max HP using virtual function call (matching original v3 = GetMaxHP(v9))
        int maxHP = GetMaxHP();

        // Only recover if not at full HP (matching original if (v6 < v3))
        if (currentHP < maxHP) {
            uint32_t currentTime = GetLoopTime();

            // Calculate time difference and check against delay (matching original condition)
            float timeSinceLastRecover = static_cast<float>(currentTime - m_dwLastRecoverTime);
            float requiredDelay = m_pMonRec->m_fHPRecDelay * 1000.0f; // Convert to milliseconds

            // Check if enough time has passed since last recovery
            if (timeSinceLastRecover >= requiredDelay) {
                // Update last recovery time (matching original m_dwLastRecoverTime = GetLoopTime())
                m_dwLastRecoverTime = currentTime;

                // Calculate new HP with recovery amount (matching original v4 = GetHP() + m_fHPRecUnit)
                float newHP = static_cast<float>(GetHP()) + m_pMonRec->m_fHPRecUnit;

                // Apply floor function and set HP (matching original ffloor(v4) and SetHP call)
                int recoveredHP = static_cast<int>(ffloor(newHP));
                SetHP(static_cast<unsigned int>(recoveredHP), false);
            }
        }
    }

    // Call the additional auto-recovery logic (matching original CMonster::AutoRecover(v9))
    AutoRecover();
}

void CMonster::AutoRecover(float bonusRecovery)
{
    // Initialize debug stack pattern (matching original decompiled initialization)
    // This matches the original: for ( i = 16i64; i; --i ) { *(_DWORD *)v2 = -858993460; v2 = (__int64 *)((char *)v2 + 4); }
    uint32_t debugPattern[16];
    std::fill(debugPattern, debugPattern + 16, 0xCCCCCCCC);

    if (!m_bLive) {
        return;
    }

    // Get current HP using virtual function call (matching original v5 = GetHP(v9))
    int currentHP = GetHP();
    int recoveryAmount = 0; // This matches original v6 = 0

    // Get base recovery from effect parameters (matching original _effect_parameter::GetEff_Plus(&v9->m_EP, 32))
    // Effect type 32 is for HP recovery
    if (m_EP) {
        recoveryAmount = GetEffectParameterPlus(32);
    }

    // Add bonus recovery if provided (matching original if (a2 != 0.0))
    if (bonusRecovery != 0.0f) {
        // Get effect parameter again for bonus calculation (matching original double call)
        if (m_EP) {
            int baseEffect = GetEffectParameterPlus(32);
            // Apply bonus using floor function (matching original v6 = (signed int)ffloor((float)v6 + a2))
            recoveryAmount = static_cast<int>(ffloor(static_cast<float>(baseEffect) + bonusRecovery));
        }
    }

    // Only proceed if there's recovery to apply (matching original if (v6))
    if (recoveryAmount != 0) {
        // Check minimum HP threshold for negative recovery (matching original if (v6 < 0))
        if (recoveryAmount < 0) {
            // Get max HP and calculate 10% threshold (matching original v7 = GetMaxHP(v9) / 10)
            int maxHP = GetMaxHP();
            int minimumHP = maxHP / 10; // 10% of max HP

            // Don't allow HP to go below 10% of max HP (matching original if (v6 + v5 <= v7))
            if (recoveryAmount + currentHP <= minimumHP) {
                recoveryAmount = 0; // Reset recovery amount
            }
        }

        // Apply the recovery if there's still an amount to apply (matching original if (v6))
        if (recoveryAmount != 0) {
            // Calculate new HP (matching original v6 + v5)
            int newHP = recoveryAmount + currentHP;

            // Call SetHP with the new value (matching original SetHP(v9, (unsigned int)(v6 + v5), 0i64))
            SetHP(static_cast<unsigned int>(newHP), false);
        }
    }
}

void CMonster::CheckEmotionPresentation()
{
    // Check if emotion presentation is set and needs to be sent
    if (m_EmotionPresentationCheck.m_bIsSet) {
        int targetIndex = -1;

        // Get target index if target exists
        if (m_EmotionPresentationCheck.m_pTarget) {
            // For now, use a placeholder implementation
            // In the original code, this accessed m_pTarget->m_ObjID.m_wIndex
            // This would need to be implemented based on actual CCharacter structure
            targetIndex = 0; // Placeholder - would get actual object ID
        }

        // Send the emotion presentation message
        SendMsg_Emotion_Presentation(
            m_EmotionPresentationCheck.m_byType,
            m_EmotionPresentationCheck.m_wIndex,
            m_EmotionPresentationCheck.m_wRandIndex,
            targetIndex
        );

        // Reset the emotion presentation checker after sending
        EmotionPresentationChecker::ReSet(&m_EmotionPresentationCheck);
    }
}

void CMonster::ClearEmotionPresentation()
{
    // Clear emotion presentation state by resetting the emotion presentation checker
    EmotionPresentationChecker::ReSet(&m_EmotionPresentationCheck);
}

void CMonster::SendMsg_Emotion_Presentation(uint8_t emotionType, uint16_t emotionIndex,
                                           uint16_t randIndex, int targetIndex)
{
    // Send emotion presentation message to nearby clients
    // This would typically create and send a network message to display the emotion
    // For now, we'll provide a placeholder implementation

    // Implementation would:
    // 1. Create emotion presentation message packet
    // 2. Set emotion type, indices, and target information
    // 3. Send to all nearby players who can see this monster
    // 4. Handle special cases for targeted emotions

    // Placeholder implementation
    if (emotionType > 0 && emotionIndex > 0) {
        // Log the emotion presentation for debugging
        // In a real implementation, this would send network packets
    }
}

bool CMonster::CheckDelayDestroy()
{
    // Check if delayed destruction is enabled and time has elapsed
    if (m_dwDestroyNextTime == 0xFFFFFFFF || GetLoopTime() <= m_dwDestroyNextTime) {
        return false;
    }

    // Time has elapsed, destroy the monster
    Destroy(true, nullptr);
    m_dwDestroyNextTime = 0xFFFFFFFF;
    return true;
}

bool CMonster::CheckRespawnProcess()
{
    uint32_t currentTime = GetLoopTime();

    // Only process respawn for active monsters
    if (!m_pActiveRec) {
        return false;
    }

    // Skip respawn check for dungeon monsters, special condition monsters,
    // monsters with parents, or monsters with children
    if (m_bDungeon ||
        (m_pMonRec && m_pMonRec->m_bMonsterCondition == 1) ||
        m_MonHierarcy.GetParent() ||
        m_MonHierarcy.ChildKindCount() > 0) {
        return false;
    }

    // Check if monster has exceeded its lifetime
    uint32_t lifeElapsed = currentTime - m_LifeCicle;
    if (lifeElapsed <= m_LifeMax) {
        return false;
    }

    // Calculate distance from creation position
    float distance = GetSqrt(m_fCurPos, m_fCreatePos);
    float heightDiff = std::abs(m_fCurPos[1] - m_fCreatePos[1]);

    // Check if monster is too far from spawn point or too high/low
    if (distance >= 100.0f || heightDiff >= 50.0f) {
        // Create respawn data
        _monster_create_setdata respawnData{};
        std::memcpy(respawnData.m_fStartPos, m_fCreatePos, sizeof(float) * 3);
        respawnData.m_nLayerIndex = m_wMapLayerIndex;
        respawnData.m_pMap = m_pCurMap;
        respawnData.m_pRecordSet = m_pRecordSet;
        respawnData.pActiveRec = m_pActiveRec;
        respawnData.bDungeon = m_bDungeon;
        respawnData.pDumPosition = m_pDumPosition;
        respawnData.pParent = m_MonHierarcy.GetParent();
        respawnData.bRobExp = m_bRobExp;

        // Destroy current instance and recreate at spawn point
        Destroy(true, nullptr);
        Create(&respawnData);
        return true;
    }

    // Reset life cycle timer if monster is close to spawn point
    m_LifeCicle = currentTime;
    return false;
}

void CMonster::ChangeApparition(bool apparition, uint32_t time)
{
    // Set the apparition state
    m_bApparition = apparition;

    // Set destruction timer if specified (0xFFFFFFFF means no timer)
    if (time != 0xFFFFFFFF) {
        m_dwDestroyNextTime = time + GetLoopTime();
    }
}

bool CMonster::CheckMonsterStateData()
{
    // Implementation would validate monster state data
    return true;
}

void CMonster::UpdateLookAtPos()
{
    // Update look-at position based on current movement direction or target
    // This overload is typically called during movement to update facing direction

    if (!m_bLive) {
        return;
    }

    // Calculate look-at position based on current movement direction
    // If the monster is moving, face the direction of movement
    float directionX = m_fCurPos[0] - m_fPrevPos[0];
    float directionZ = m_fCurPos[2] - m_fPrevPos[2];

    // Only update if there's significant movement
    if (std::abs(directionX) > 0.01f || std::abs(directionZ) > 0.01f) {
        // Normalize the direction vector
        float length = std::sqrt(directionX * directionX + directionZ * directionZ);
        if (length > 0.0f) {
            directionX /= length;
            directionZ /= length;

            // Set look-at position ahead of current position
            m_fLookAtPos[0] = m_fCurPos[0] + directionX * 10.0f;
            m_fLookAtPos[1] = m_fCurPos[1];
            m_fLookAtPos[2] = m_fCurPos[2] + directionZ * 10.0f;
        }
    }
}

void CMonster::UpdateLookAtPos(const float* position)
{
    if (!position || !m_bLive) {
        return;
    }

    // Update look-at position to specific coordinates
    // This overload is used when explicitly setting a target position to face

    // Copy the new look-at position
    std::memcpy(m_fLookAtPos, position, sizeof(float) * 3);

    // Calculate the angle/direction from current position to look-at position
    float deltaX = position[0] - m_fCurPos[0];
    float deltaZ = position[2] - m_fCurPos[2];

    // Calculate the Y-angle (rotation around Y-axis) for facing direction
    if (std::abs(deltaX) > 0.001f || std::abs(deltaZ) > 0.001f) {
        float angle = std::atan2(deltaX, deltaZ);

        // Convert to degrees and normalize to 0-360 range
        float angleDegrees = angle * 180.0f / 3.14159265f;
        if (angleDegrees < 0.0f) {
            angleDegrees += 360.0f;
        }

        // Update the monster's Y-angle for proper facing direction
        // This would typically be stored in a rotation/angle member variable
        // For now, we'll store it as a simple angle value
        m_fYAngle = angleDegrees;
    }
}

uint32_t CMonster::GetNewMonSerial()
{
    static uint32_t s_serialCounter = 1;
    return s_serialCounter++;
}

void CMonster::_InitSDM()
{
    // Initialize static data manager
}

void CMonster::_DestroySDM()
{
    // Destroy static data manager
}

bool CMonster::ValidateState() const
{
    return m_bLive && m_pMonRec != nullptr;
}

// CMonsterUtils implementation

namespace CMonsterUtils {

std::unique_ptr<CMonster> CreateMonster()
{
    return std::make_unique<CMonster>();
}

bool ValidateMonster(const CMonster* pMonster)
{
    return pMonster != nullptr && pMonster->ValidateState();
}

size_t GetMemoryFootprint(const CMonster* pMonster)
{
    if (!pMonster) {
        return 0;
    }
    
    // Calculate approximate memory usage
    size_t footprint = sizeof(CMonster);
    
    // Add size of dynamic components
    // In a real implementation, we would add the size of each component
    
    return footprint;
}

} // namespace CMonsterUtils

bool CMonster::Create(const _monster_create_setdata* pData)
{
    // Based on original decompiled logic from CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.c

    if (!pData) {
        return false;
    }

    // Initialize stack variables (equivalent to original stack initialization)
    // Original code: for ( i = 32i64; i; --i ) { *(_DWORD *)v3 = -858993460; v3 = (__int64 *)((char *)v3 + 4); }
    // This was just stack initialization with magic numbers, not needed in modern C++

    // Call parent class Create method (equivalent to CCharacter::Create)
    // For now, we'll assume the parent creation succeeds
    // In a full implementation, this would call the actual CCharacter::Create method
    bool parentCreateResult = true; // Placeholder for CCharacter::Create call

    if (!parentCreateResult) {
        return false;
    }

    // Set monster record (equivalent to v18->m_pMonRec = (_monster_fld *)pDataa->m_pRecordSet)
    // For now, we'll use a placeholder since the structure is not fully defined
    // m_pMonRec = reinterpret_cast<_monster_fld*>(pData->m_pRecordSet);

    // Initialize basic monster properties
    m_bRotateMonster = false;
    m_bStdItemLoot = true;
    m_nEventItemNum = 0;
    m_bLive = true;

    // Initialize pointers (equivalent to original null assignments)
    m_pEventRespawn = nullptr;
    m_pActiveRec = nullptr;
    m_pDumPosition = nullptr;
    m_pEventSet = nullptr;

    // Set HP to maximum (equivalent to v18->m_nHP = (signed int)ffloor(v18->m_pMonRec->m_fMaxHP))
    if (m_pMonRec) {
        m_nHP = static_cast<int>(std::floor(m_pMonRec->m_fMaxHP));
    } else {
        m_nHP = 100; // Default HP
    }

    // Initialize AI (equivalent to CMonster::CreateAI(v18, 0))
    CreateAI(0);

    // Set operation flag (equivalent to v18->m_bOper = 1)
    m_bOper = true;

    // Set initial move type (equivalent to CMonster::SetMoveType(v18, 0))
    SetMoveType(0);

    return true;
}

// Legacy C-style interface implementation

extern "C" {

void CMonster_Constructor(CMonster* pThis)
{
    if (pThis) {
        new (pThis) CMonster();
    }
}

void CMonster_Destructor(CMonster* pThis)
{
    if (pThis) {
        pThis->~CMonster();
    }
}

bool CMonster_IsMovable(CMonster* pThis)
{
    return pThis ? pThis->IsMovable() : false;
}

float CMonster_GetMoveSpeed(CMonster* pThis)
{
    return pThis ? pThis->GetMoveSpeed() : 0.0f;
}

uint8_t CMonster_GetMoveType(CMonster* pThis)
{
    return pThis ? pThis->GetMoveType() : 0;
}

void CMonster_SetMoveType(CMonster* pThis, uint8_t moveType)
{
    if (pThis) {
        pThis->SetMoveType(moveType);
    }
}

int CMonster_GetHP(CMonster* pThis)
{
    return pThis ? pThis->GetHP() : 0;
}

int CMonster_GetMaxHP(CMonster* pThis)
{
    return pThis ? pThis->GetMaxHP() : 0;
}

const char* CMonster_GetObjName(CMonster* pThis)
{
    return pThis ? pThis->GetObjName() : "[MONSTER] >> <Invalid>";
}

bool CMonster_SetHP(CMonster* pThis, int hp, bool notify)
{
    return pThis ? pThis->SetHP(hp, notify) : false;
}

uint8_t CMonster_GetEmotionState(CMonster* pThis)
{
    return pThis ? pThis->GetEmotionState() : 0;
}

void CMonster_SetEmotionState(CMonster* pThis, uint8_t emotionState)
{
    if (pThis) {
        pThis->SetEmotionState(emotionState);
    }
}

} // extern "C"
