/*
 * CMonsterAttack_Params.cpp - Monster Attack Parameter Generation
 * Refactored from make_gen_attack_paramCMonsterQEAAXPEAVCCharacterPE_14014DE80.c
 * and make_skill_attack_paramCMonsterQEAA_NPEAVCCharacte_14014E260.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CMonsterAttack.h"
#include "../../world/Headers/CCharacter.h"
#include "../../world/Headers/CMonster.h"
#include "../../world/Headers/CMonsterSkill.h"
#include "../../world/Headers/CMonsterSkillPool.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <cmath>

// External dependencies
extern uint32_t GetLoopTime();
extern void memcpy_0(void* dest, const void* src, size_t size);
extern float ffloor(float value);

/**
 * Monster attack parameter generation namespace
 */
namespace MonsterAttackParams {

/**
 * Generate general attack parameters for monster
 * Refactored from original CMonster::make_gen_attack_param
 */
bool MakeGeneralAttackParam(CMonster* pMonster, CCharacter* pTarget, _attack_param* pAttackParam) {
    try {
        if (!pMonster || !pAttackParam) {
            Logger::Error("MakeGeneralAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters (equivalent to original memory initialization)
        pAttackParam->Reset();
        
        // Get monster skill for attack (equivalent to original CMonsterSkillPool::GetMonSkillKind call)
        CMonsterSkill* pSkill = nullptr;
        if (pMonster->GetMonsterSkillPool()) {
            pSkill = pMonster->GetMonsterSkillPool()->GetMonSkillKind(0);
        }
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part (equivalent to original GetAttackRandomPart/GetAttackPart logic)
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pMonster->GetAttackPart();
        }
        
        // Set attack class from monster record
        if (pMonster->GetMonsterRecord()) {
            pAttackParam->nClass = pMonster->GetMonsterRecord()->m_bAttRangeType;
        } else {
            pAttackParam->nClass = 0; // Default attack class
        }
        
        // Configure attack parameters based on skill availability
        if (pSkill) {
            // Use skill-based parameters
            pAttackParam->nTol = pSkill->GetElement();
            pAttackParam->nMinAF = pSkill->GetMinDmg();
            pAttackParam->nMaxAF = pSkill->GetMaxDmg();
            pAttackParam->nMinSel = pSkill->GetMinProb();
            pAttackParam->nMaxSel = pSkill->GetMaxProb();
            
            Logger::Debug("MakeGeneralAttackParam - Using skill parameters: MinDmg=%d, MaxDmg=%d", 
                         pAttackParam->nMinAF, pAttackParam->nMaxAF);
        } else {
            // Use default parameters (equivalent to original else branch)
            pAttackParam->nTol = -1;
            pAttackParam->nMinAF = 0;
            pAttackParam->nMaxAF = 500;
            pAttackParam->nMinSel = 0;
            pAttackParam->nMaxSel = 100;
            
            Logger::Debug("MakeGeneralAttackParam - Using default parameters");
        }
        
        // Set pass count based on monster condition (equivalent to original m_bMonsterCondition check)
        if (pMonster->GetMonsterRecord()) {
            pAttackParam->bPassCount = (pMonster->GetMonsterRecord()->m_bMonsterCondition == 1);
        } else {
            pAttackParam->bPassCount = false;
        }
        
        // Configure special attack types (equivalent to original m_nAttType check)
        if (pMonster->GetMonsterRecord() && pMonster->GetMonsterRecord()->m_nAttType > 2) {
            pAttackParam->nAttactType = 6;
            pAttackParam->nExtentRange = 90;
        } else {
            pAttackParam->nAttactType = 0;
            pAttackParam->nExtentRange = CAttack::DEFAULT_EXTENT_RANGE;
        }
        
        // Set attack area (equivalent to original memcpy_0 call)
        if (pTarget) {
            const auto& targetPos = pTarget->GetPosition();
            pAttackParam->fArea[0] = targetPos.x;
            pAttackParam->fArea[1] = targetPos.y;
            pAttackParam->fArea[2] = targetPos.z;
        } else {
            pAttackParam->fArea.fill(0.0f);
        }
        
        Logger::Debug("MakeGeneralAttackParam - Parameters generated successfully for monster %p", pMonster);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeGeneralAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate skill attack parameters for monster
 * Refactored from original CMonster::make_skill_attack_param
 */
bool MakeSkillAttackParam(CMonster* pMonster, CCharacter* pTarget, CMonsterSkill* pSkill, 
                         int nEffectType, float fEffectPlus, _attack_param* pAttackParam) {
    try {
        if (!pMonster || !pAttackParam) {
            Logger::Error("MakeSkillAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters (equivalent to original memory initialization)
        pAttackParam->Reset();
        
        // Validate skill and skill type (equivalent to original skill type check)
        if (!pSkill) {
            Logger::Warning("MakeSkillAttackParam - No skill provided");
            return false;
        }
        
        int skillType = pSkill->GetType();
        if (skillType != 1 && skillType != 2) {
            Logger::Warning("MakeSkillAttackParam - Invalid skill type: %d", skillType);
            return false;
        }
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Determine attack part (equivalent to original GetAttackRandomPart logic)
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            // Use monster as character for attack part calculation (equivalent to original cast)
            pAttackParam->nPart = pMonster->GetAttackRandomPart();
        }
        
        // Set attack class from monster record
        if (pMonster->GetMonsterRecord()) {
            pAttackParam->nClass = pMonster->GetMonsterRecord()->m_bAttRangeType;
        } else {
            pAttackParam->nClass = 0;
        }
        
        // Configure skill-based parameters
        pAttackParam->nTol = pSkill->GetElement();
        pAttackParam->nMinAF = pSkill->GetMinDmg();
        pAttackParam->nMaxAF = pSkill->GetMaxDmg();
        pAttackParam->nMinSel = pSkill->GetMinProb();
        pAttackParam->nMaxSel = pSkill->GetMaxProb();
        pAttackParam->nExtentRange = 20;
        pAttackParam->nShotNum = 1;
        pAttackParam->nAddAttPnt = 0;
        pAttackParam->pFld = pSkill->GetFld();
        
        // Configure effect parameters (equivalent to original nEffectType check)
        if (nEffectType) {
            pAttackParam->byEffectCode = 2;
            pAttackParam->nLevel = 1;
            pAttackParam->nMastery = 99;
        } else {
            pAttackParam->byEffectCode = 0;
            pAttackParam->nLevel = pSkill->GetSFLv();
            
            // Apply effect plus modifier (equivalent to original _effect_parameter::GetEff_Plus call)
            if (pMonster->GetEffectParameter()) {
                float effectBonus = pMonster->GetEffectParameter()->GetEff_Plus(19);
                pAttackParam->nLevel = static_cast<int>(ffloor(static_cast<float>(pAttackParam->nLevel) + effectBonus + fEffectPlus));
            } else {
                pAttackParam->nLevel = static_cast<int>(ffloor(static_cast<float>(pAttackParam->nLevel) + fEffectPlus));
            }
            
            // Clamp level to maximum (equivalent to original level > 7 check)
            if (pAttackParam->nLevel > 7) {
                pAttackParam->nLevel = 7;
            }
            
            pAttackParam->nMastery = 99;
        }
        
        // Set attack area (equivalent to original memcpy_0 calls)
        if (pTarget) {
            const auto& targetPos = pTarget->GetPosition();
            pAttackParam->fArea[0] = targetPos.x;
            pAttackParam->fArea[1] = targetPos.y;
            pAttackParam->fArea[2] = targetPos.z;
        } else {
            const auto& monsterPos = pMonster->GetPosition();
            pAttackParam->fArea[0] = monsterPos.x;
            pAttackParam->fArea[1] = monsterPos.y;
            pAttackParam->fArea[2] = monsterPos.z;
        }
        
        // Set maximum attack points
        pAttackParam->nMaxAttackPnt = 0;
        
        Logger::Debug("MakeSkillAttackParam - Skill parameters generated successfully for monster %p, skill type %d", 
                     pMonster, skillType);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeSkillAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate force attack parameters for monster
 */
bool MakeForceAttackParam(CMonster* pMonster, CCharacter* pTarget, int nForceDamage, _attack_param* pAttackParam) {
    try {
        if (!pMonster || !pAttackParam) {
            Logger::Error("MakeForceAttackParam - Invalid parameters");
            return false;
        }
        
        // Reset attack parameters
        pAttackParam->Reset();
        
        // Set target
        pAttackParam->pDst = pTarget;
        
        // Force attacks use specific parameters
        if (pTarget) {
            pAttackParam->nPart = pTarget->GetAttackRandomPart();
        } else {
            pAttackParam->nPart = pMonster->GetAttackPart();
        }
        
        // Set attack class from monster record
        if (pMonster->GetMonsterRecord()) {
            pAttackParam->nClass = pMonster->GetMonsterRecord()->m_bAttRangeType;
        } else {
            pAttackParam->nClass = 0;
        }
        
        // Force attack parameters
        pAttackParam->nTol = -1; // No elemental type
        pAttackParam->nMinAF = nForceDamage;
        pAttackParam->nMaxAF = nForceDamage;
        pAttackParam->nMinSel = 100; // Always hits
        pAttackParam->nMaxSel = 100;
        pAttackParam->nExtentRange = 50; // Larger range for force attacks
        pAttackParam->nShotNum = 1;
        pAttackParam->nAddAttPnt = 0;
        pAttackParam->pFld = nullptr;
        pAttackParam->byEffectCode = 1; // Force effect
        pAttackParam->nLevel = 7; // Maximum level
        pAttackParam->nMastery = 99;
        pAttackParam->nMaxAttackPnt = 0;
        pAttackParam->bPassCount = true; // Force attacks bypass defenses
        pAttackParam->nAttactType = 6; // Special attack type
        
        // Set attack area
        if (pTarget) {
            const auto& targetPos = pTarget->GetPosition();
            pAttackParam->fArea[0] = targetPos.x;
            pAttackParam->fArea[1] = targetPos.y;
            pAttackParam->fArea[2] = targetPos.z;
        } else {
            pAttackParam->fArea.fill(0.0f);
        }
        
        Logger::Debug("MakeForceAttackParam - Force parameters generated successfully for monster %p, damage %d", 
                     pMonster, nForceDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("MakeForceAttackParam - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate attack parameters
 */
bool ValidateAttackParameters(const _attack_param* pAttackParam) {
    if (!pAttackParam) {
        return false;
    }
    
    // Check basic parameter validity
    if (pAttackParam->nMinAF < 0 || pAttackParam->nMaxAF < pAttackParam->nMinAF) {
        Logger::Warning("ValidateAttackParameters - Invalid damage range: Min=%d, Max=%d", 
                       pAttackParam->nMinAF, pAttackParam->nMaxAF);
        return false;
    }
    
    if (pAttackParam->nMinSel < 0 || pAttackParam->nMaxSel < pAttackParam->nMinSel || 
        pAttackParam->nMaxSel > 100) {
        Logger::Warning("ValidateAttackParameters - Invalid selection range: Min=%d, Max=%d", 
                       pAttackParam->nMinSel, pAttackParam->nMaxSel);
        return false;
    }
    
    if (pAttackParam->nExtentRange < 0) {
        Logger::Warning("ValidateAttackParameters - Invalid extent range: %d", pAttackParam->nExtentRange);
        return false;
    }
    
    if (pAttackParam->nShotNum <= 0) {
        Logger::Warning("ValidateAttackParameters - Invalid shot number: %d", pAttackParam->nShotNum);
        return false;
    }
    
    if (pAttackParam->nLevel < 0 || pAttackParam->nLevel > 7) {
        Logger::Warning("ValidateAttackParameters - Invalid level: %d", pAttackParam->nLevel);
        return false;
    }
    
    if (pAttackParam->nMastery < 0 || pAttackParam->nMastery > 99) {
        Logger::Warning("ValidateAttackParameters - Invalid mastery: %d", pAttackParam->nMastery);
        return false;
    }
    
    return true;
}

/**
 * Copy attack parameters
 */
bool CopyAttackParameters(const _attack_param* pSource, _attack_param* pDestination) {
    if (!pSource || !pDestination) {
        return false;
    }
    
    try {
        *pDestination = *pSource;
        return true;
    } catch (const std::exception& e) {
        Logger::Error("CopyAttackParameters - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get attack parameter summary for debugging
 */
std::string GetAttackParameterSummary(const _attack_param* pAttackParam) {
    if (!pAttackParam) {
        return "Invalid parameters";
    }
    
    std::ostringstream oss;
    oss << "AttackParam: ";
    oss << "Target=" << pAttackParam->pDst;
    oss << " Part=" << pAttackParam->nPart;
    oss << " Class=" << pAttackParam->nClass;
    oss << " Dmg=" << pAttackParam->nMinAF << "-" << pAttackParam->nMaxAF;
    oss << " Sel=" << pAttackParam->nMinSel << "-" << pAttackParam->nMaxSel;
    oss << " Range=" << pAttackParam->nExtentRange;
    oss << " Level=" << pAttackParam->nLevel;
    oss << " Type=" << pAttackParam->nAttactType;
    
    return oss.str();
}

} // namespace MonsterAttackParams

// Extension methods for CMonster class (to be added to CMonster implementation)
namespace CMonsterExtensions {

/**
 * Make general attack parameters (wrapper for compatibility)
 */
bool MakeGeneralAttackParam(CMonster* pMonster, CCharacter* pTarget, _attack_param* pAttackParam) {
    return MonsterAttackParams::MakeGeneralAttackParam(pMonster, pTarget, pAttackParam);
}

/**
 * Make skill attack parameters (wrapper for compatibility)
 */
bool MakeSkillAttackParam(CMonster* pMonster, CCharacter* pTarget, CMonsterSkill* pSkill, 
                         int nEffectType, _attack_param* pAttackParam) {
    return MonsterAttackParams::MakeSkillAttackParam(pMonster, pTarget, pSkill, nEffectType, 0.0f, pAttackParam);
}

/**
 * Make skill attack parameters with effect plus
 */
bool MakeSkillAttackParamWithEffectPlus(CMonster* pMonster, CCharacter* pTarget, CMonsterSkill* pSkill, 
                                       int nEffectType, float fEffectPlus, _attack_param* pAttackParam) {
    return MonsterAttackParams::MakeSkillAttackParam(pMonster, pTarget, pSkill, nEffectType, fEffectPlus, pAttackParam);
}

} // namespace CMonsterExtensions
