/**
 * @file CMainThread_PlayerManagement_Utils.cpp
 * @brief Utility functions for player management system
 * 
 * Contains helper functions for player lookup, character loading,
 * and player state management.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_PlayerManagement.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <algorithm>

// External global player objects
extern CUserDB g_UserDB[2532];
extern CPartyPlayer g_PartyPlayer[2532];
extern CPlayer g_Player[2532];

// External database functions
extern char CMainThread_db_Load_Avator(CMainThread* mainThread, uint32_t dwSerial, uint32_t dwAccountSerial, 
                                      _AVATOR_DATA* pData, bool* pbAddItem, uint32_t* pdwAddDalant, 
                                      uint32_t* pdwAddGold, bool* pbTrunkAddItem, char* pbyTrunkOldSlot, 
                                      long double* pdTrunkOldDalant, long double* pdTrunkOldGold, 
                                      bool* pbCreateTrunkFree, bool* pbExtTrunkAddItem, 
                                      char* pbyExtTrunkOldSlot, bool bAll, uint32_t* pdwCheckSum);

/**
 * @brief Load character data
 * 
 * Loads character data from the database.
 * 
 * @param accountSerial Account serial number
 * @param characterSerial Character serial number
 * @param avatorData Pointer to avatar data structure
 * @param firstLogin Whether this is the first login
 * @return CharacterLoadResult indicating success or failure
 */
CharacterLoadResult CMainThreadPlayerManagement::LoadCharacterData(uint32_t accountSerial, uint32_t characterSerial, 
                                                                  _AVATOR_DATA* avatorData, bool firstLogin) {
    try {
        if (!m_mainThread) {
            SetLastError("Main thread not initialized");
            return CharacterLoadResult::SystemError;
        }
        
        if (!avatorData) {
            SetLastError("Invalid avatar data pointer");
            return CharacterLoadResult::InvalidData;
        }
        
        std::cout << std::format("[INFO] Loading character data: Account={}, Character={}", 
                                accountSerial, characterSerial) << std::endl;
        
        // Initialize output parameters
        bool addItem = false;
        uint32_t addDalant = 0;
        uint32_t addGold = 0;
        bool trunkAddItem = false;
        char trunkOldSlot = 0;
        long double trunkOldDalant = 0.0;
        long double trunkOldGold = 0.0;
        bool createTrunkFree = false;
        bool extTrunkAddItem = false;
        char extTrunkOldSlot = 0;
        uint32_t checkSum = 0;
        
        // Call the database loading function
        char result = CMainThread_db_Load_Avator(m_mainThread, characterSerial, accountSerial, avatorData,
                                               &addItem, &addDalant, &addGold, &trunkAddItem, &trunkOldSlot,
                                               &trunkOldDalant, &trunkOldGold, &createTrunkFree, &extTrunkAddItem,
                                               &extTrunkOldSlot, true, &checkSum);
        
        if (result) {
            std::cout << std::format("[INFO] Character data loaded successfully: Account={}, Character={}", 
                                    accountSerial, characterSerial) << std::endl;
            return CharacterLoadResult::Success;
        } else {
            SetLastError(std::format("Database load failed for Account={}, Character={}", 
                                   accountSerial, characterSerial));
            return CharacterLoadResult::DatabaseError;
        }
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception loading character data: {}", e.what()));
        return CharacterLoadResult::SystemError;
    }
}

/**
 * @brief Find available player slot
 * 
 * Finds an available player slot for a new connection.
 * 
 * @return Optional containing the available slot index, or empty if none available
 */
std::optional<uint32_t> CMainThreadPlayerManagement::FindAvailablePlayerSlot() const {
    try {
        std::lock_guard<std::mutex> lock(m_statesMutex);
        
        for (uint32_t index = 0; index < 2532; ++index) {
            if (m_playerStates[index] == PlayerState::Inactive) {
                return index;
            }
        }
        
        return std::nullopt;  // No available slots
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception finding available player slot: " << e.what() << std::endl;
        return std::nullopt;
    }
}

/**
 * @brief Get player by index
 * 
 * Gets a player object by its index.
 * 
 * @param index Player index
 * @return Pointer to CPlayer object, or nullptr if invalid
 */
CPlayer* CMainThreadPlayerManagement::GetPlayerByIndex(uint32_t index) const {
    if (index >= 2532) {
        return nullptr;
    }
    
    return &g_Player[index];
}

/**
 * @brief Get player by account serial
 * 
 * Gets a player object by its account serial number.
 * 
 * @param accountSerial Account serial number
 * @return Pointer to CPlayer object, or nullptr if not found
 */
CPlayer* CMainThreadPlayerManagement::GetPlayerByAccountSerial(uint32_t accountSerial) const {
    try {
        for (uint32_t index = 0; index < 2532; ++index) {
            CUserDB* userDB = &g_UserDB[index];
            if (userDB && userDB->m_dwAccountSerial == accountSerial && userDB->m_bActive) {
                return &g_Player[index];
            }
        }
        
        return nullptr;  // Not found
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception getting player by account serial: " << e.what() << std::endl;
        return nullptr;
    }
}

/**
 * @brief Get player by character serial
 * 
 * Gets a player object by its character serial number.
 * 
 * @param characterSerial Character serial number
 * @return Pointer to CPlayer object, or nullptr if not found
 */
CPlayer* CMainThreadPlayerManagement::GetPlayerByCharacterSerial(uint32_t characterSerial) const {
    try {
        for (uint32_t index = 0; index < 2532; ++index) {
            CUserDB* userDB = &g_UserDB[index];
            if (userDB && userDB->m_AvatorData.m_dwSerial == characterSerial && userDB->m_bActive) {
                return &g_Player[index];
            }
        }
        
        return nullptr;  // Not found
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception getting player by character serial: " << e.what() << std::endl;
        return nullptr;
    }
}

/**
 * @brief Get user database by index
 * 
 * Gets a user database object by its index.
 * 
 * @param index User database index
 * @return Pointer to CUserDB object, or nullptr if invalid
 */
CUserDB* CMainThreadPlayerManagement::GetUserDBByIndex(uint32_t index) const {
    if (index >= 2532) {
        return nullptr;
    }
    
    return &g_UserDB[index];
}

/**
 * @brief Get party player by index
 * 
 * Gets a party player object by its index.
 * 
 * @param index Party player index
 * @return Pointer to CPartyPlayer object, or nullptr if invalid
 */
CPartyPlayer* CMainThreadPlayerManagement::GetPartyPlayerByIndex(uint32_t index) const {
    if (index >= 2532) {
        return nullptr;
    }
    
    return &g_PartyPlayer[index];
}

/**
 * @brief Get active player count
 * @return Number of active players
 */
uint32_t CMainThreadPlayerManagement::GetActivePlayerCount() const {
    try {
        std::lock_guard<std::mutex> lock(m_statesMutex);
        
        uint32_t activeCount = 0;
        for (const auto& state : m_playerStates) {
            if (state != PlayerState::Inactive && state != PlayerState::Disconnected) {
                activeCount++;
            }
        }
        
        return activeCount;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception getting active player count: " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Register player state change callback
 * @param callback Function to call when player state changes
 */
void CMainThreadPlayerManagement::RegisterPlayerStateChangeCallback(std::function<void(uint32_t, PlayerState, PlayerState)> callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_stateCallbacks.push_back(std::move(callback));
}

/**
 * @brief Update player state
 * @param playerIndex Player index
 * @param newState New player state
 */
void CMainThreadPlayerManagement::UpdatePlayerState(uint32_t playerIndex, PlayerState newState) {
    if (playerIndex >= 2532) {
        return;
    }
    
    PlayerState oldState;
    
    // Update state
    {
        std::lock_guard<std::mutex> lock(m_statesMutex);
        oldState = m_playerStates[playerIndex];
        m_playerStates[playerIndex] = newState;
    }
    
    // Call registered callbacks
    if (oldState != newState) {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        for (const auto& callback : m_stateCallbacks) {
            try {
                callback(playerIndex, oldState, newState);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in player state callback: " << e.what() << std::endl;
            }
        }
        
        std::cout << std::format("[INFO] Player {} state changed: {} -> {}", 
                                playerIndex, PlayerStateToString(oldState), PlayerStateToString(newState)) << std::endl;
    }
}

/**
 * @brief Convert PlayerObjectType enum to string for logging
 * @param objectType The object type
 * @return String representation of the object type
 */
std::string PlayerObjectTypeToString(PlayerObjectType objectType) {
    switch (objectType) {
        case PlayerObjectType::UserDatabase: return "UserDatabase";
        case PlayerObjectType::PartyPlayer: return "PartyPlayer";
        case PlayerObjectType::Player: return "Player";
        default: return "Unknown";
    }
}

/**
 * @brief Convert PlayerInitResult enum to string for logging
 * @param result The initialization result
 * @return String representation of the result
 */
std::string PlayerInitResultToString(PlayerInitResult result) {
    switch (result) {
        case PlayerInitResult::Success: return "Success";
        case PlayerInitResult::Failure: return "Failure";
        case PlayerInitResult::InvalidParameter: return "InvalidParameter";
        case PlayerInitResult::AllocationError: return "AllocationError";
        case PlayerInitResult::DatabaseError: return "DatabaseError";
        case PlayerInitResult::InitializationError: return "InitializationError";
        case PlayerInitResult::SystemError: return "SystemError";
        default: return "Unknown";
    }
}

/**
 * @brief Convert PlayerState enum to string for logging
 * @param state The player state
 * @return String representation of the state
 */
std::string PlayerStateToString(PlayerState state) {
    switch (state) {
        case PlayerState::Inactive: return "Inactive";
        case PlayerState::LoggingIn: return "LoggingIn";
        case PlayerState::CharacterSelect: return "CharacterSelect";
        case PlayerState::Loading: return "Loading";
        case PlayerState::Active: return "Active";
        case PlayerState::Disconnecting: return "Disconnecting";
        case PlayerState::Disconnected: return "Disconnected";
        default: return "Unknown";
    }
}

/**
 * @brief Convert CharacterLoadResult enum to string for logging
 * @param result The character load result
 * @return String representation of the result
 */
std::string CharacterLoadResultToString(CharacterLoadResult result) {
    switch (result) {
        case CharacterLoadResult::Success: return "Success";
        case CharacterLoadResult::Failure: return "Failure";
        case CharacterLoadResult::NotFound: return "NotFound";
        case CharacterLoadResult::DatabaseError: return "DatabaseError";
        case CharacterLoadResult::InvalidData: return "InvalidData";
        case CharacterLoadResult::SystemError: return "SystemError";
        default: return "Unknown";
    }
}
