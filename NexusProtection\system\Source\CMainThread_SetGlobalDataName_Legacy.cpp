/**
 * @file CMainThread_SetGlobalDataName_Legacy.cpp
 * @brief Legacy compatibility wrapper for SetGlobalDataName function
 * 
 * Provides backward compatibility for existing code that calls the original
 * SetGlobalDataName function. Maintains exact original behavior and signatures.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_SetGlobalDataName.h"
#include "CMainThread.h"
#include <iostream>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External message box function
extern void MyMessageBox(const char* title, const char* message);

/**
 * @brief Original SetGlobalDataName function behavior
 * 
 * This function replicates the exact original behavior of the SetGlobalDataName
 * function for maximum compatibility with existing code.
 * 
 * @return bool (true for success, false for failure)
 */
bool SetGlobalDataName_OriginalBehavior() {
    try {
        // Original function was very simple - just returned true
        // The actual name assignment was done elsewhere in the codebase
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Exact original SetGlobalDataName function implementation
 * 
 * This is the most faithful reproduction of the original function,
 * maintaining the exact same logic and behavior.
 * 
 * @return bool (true for success, false for failure)
 */
bool SetGlobalDataName_ExactOriginal() {
    // The original function was extremely simple and just returned true
    // All the actual name assignment was handled by other systems
    return true;
}

/**
 * @brief C-style function wrapper for exact original compatibility
 * 
 * Provides the exact original C function signature for maximum compatibility.
 * Original: bool __fastcall CMainThread::SetGlobalDataName(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance (this parameter)
 * @param unused Unused parameter (EDX register in __fastcall)
 * @return bool (true for success, false for failure)
 */
extern "C" bool __fastcall CMainThread_SetGlobalDataName_Original(CMainThread* mainThread, void* unused) {
    // Security cookie setup (equivalent to original stack protection)
    uintptr_t localCookie = reinterpret_cast<uintptr_t>(mainThread) ^ _security_cookie;
    
    try {
        // Original function logic was very simple
        if (!mainThread) {
            return false;
        }
        
        // The original function just returned true after basic validation
        // All complex name assignment was handled by other systems during data loading
        bool result = true;
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uintptr_t>(mainThread) ^ _security_cookie) != localCookie) {
            return false;
        }
        
        return result;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Enhanced legacy wrapper with modern error handling
 * 
 * Provides the original behavior but with enhanced error handling and logging.
 * This version can be used as a drop-in replacement for the original function.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_SetGlobalDataName_Enhanced(CMainThread* mainThread) {
    try {
        if (!mainThread) {
            std::cerr << "[ERROR] CMainThread pointer is null in SetGlobalDataName" << std::endl;
            return false;
        }
        
        // Use the modern name assignment system but return simple bool result
        CMainThreadSetGlobalDataName nameAssigner;
        GlobalDataNameResult result = nameAssigner.AssignAllGlobalDataNames(mainThread);
        
        bool success = (result == GlobalDataNameResult::Success);
        
        if (!success) {
            std::cerr << "[ERROR] Global data name assignment failed: " << nameAssigner.GetLastError() << std::endl;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in SetGlobalDataName: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in SetGlobalDataName" << std::endl;
        return false;
    }
}

/**
 * @brief Simple name assignment wrapper for basic name setting
 * 
 * Provides a simplified name assignment that sets only the most critical names.
 * This can be used when full name assignment is not needed.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_SetGlobalDataName_Simple(CMainThread* mainThread) {
    if (!mainThread) {
        return false;
    }
    
    try {
        // Set only the most critical global data names
        if (!CMainThreadSetGlobalDataName::AssignDataTableName_Legacy("ItemData", "ItemDataName")) {
            return false;
        }
        
        if (!CMainThreadSetGlobalDataName::AssignDataTableName_Legacy("SkillData", "SkillDataName")) {
            return false;
        }
        
        if (!CMainThreadSetGlobalDataName::AssignDataTableName_Legacy("ClassData", "ClassDataName")) {
            return false;
        }
        
        if (!CMainThreadSetGlobalDataName::AssignManagerName_Legacy("PotionManager", "PotionManagerName")) {
            return false;
        }
        
        if (!CMainThreadSetGlobalDataName::AssignManagerName_Legacy("QuestManager", "QuestManagerName")) {
            return false;
        }
        
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Comprehensive name assignment wrapper with detailed logging
 * 
 * Provides comprehensive name assignment with detailed logging for debugging purposes.
 * This version logs all assignment steps and provides detailed error information.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_SetGlobalDataName_Comprehensive(CMainThread* mainThread) {
    if (!mainThread) {
        std::cout << "[ERROR] CMainThread pointer is null" << std::endl;
        return false;
    }
    
    std::cout << "[INFO] Starting comprehensive global data name assignment..." << std::endl;
    
    try {
        CMainThreadSetGlobalDataName nameAssigner;
        GlobalDataNameResult result = nameAssigner.AssignAllGlobalDataNames(mainThread);
        
        // Print detailed statistics
        const auto& stats = nameAssigner.GetAssignmentStats();
        std::cout << "[INFO] Name assignment completed:" << std::endl;
        std::cout << "  - Total time: " << stats.GetTotalAssignmentTime().count() << "ms" << std::endl;
        std::cout << "  - Success rate: " << stats.GetSuccessRate() << "%" << std::endl;
        std::cout << "  - Successful assignments: " << stats.successfulAssignments << std::endl;
        std::cout << "  - Failed assignments: " << stats.failedAssignments << std::endl;
        std::cout << "  - Total assignments: " << stats.GetTotalAssignments() << std::endl;
        
        if (result != GlobalDataNameResult::Success) {
            std::cout << "[ERROR] Name assignment failed: " << nameAssigner.GetLastError() << std::endl;
            return false;
        }
        
        // Print all assigned names
        auto assignedNames = nameAssigner.GetAllAssignedNames();
        std::cout << "[INFO] Assigned names:" << std::endl;
        for (const auto& [nameType, name] : assignedNames) {
            std::cout << "  - " << GlobalDataNameTypeToString(nameType) << ": " << name << std::endl;
        }
        
        std::cout << "[INFO] All global data names assigned successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[ERROR] Exception during name assignment: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[ERROR] Unknown exception during name assignment" << std::endl;
        return false;
    }
}

/**
 * @brief Global data name validation helper
 * 
 * Validates that all required global data names have been assigned.
 * This function checks the state of all critical name assignments.
 * 
 * @return bool (true if all required names are assigned, false otherwise)
 */
bool ValidateGlobalDataNames_Legacy() {
    try {
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        
        // Check for essential global data names
        const std::vector<std::string> requiredNames = {
            "ItemDataName",
            "SkillDataName",
            "ClassDataName",
            "PlayerCharacterDataName",
            "MonsterCharacterDataName",
            "PotionManagerName",
            "QuestManagerName"
        };
        
        for (const auto& requiredName : requiredNames) {
            if (g_GlobalDataNames.find(requiredName) == g_GlobalDataNames.end()) {
                std::cout << "[ERROR] Required global data name not assigned: " << requiredName << std::endl;
                return false;
            }
        }
        
        std::cout << "[INFO] All required global data names are assigned" << std::endl;
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Quick name assignment for essential names only
 * 
 * Performs a quick assignment of only the most essential names required
 * for basic server operation. This is useful for fast startup scenarios.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThread_SetGlobalDataName_Quick(CMainThread* mainThread) {
    if (!mainThread) {
        return false;
    }
    
    try {
        // Set only the absolutely essential global data names
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        
        g_GlobalDataNames["ServerInstanceName"] = "NexusProtectionServer";
        g_GlobalDataNames["ItemDataName"] = "ItemData";
        g_GlobalDataNames["SkillDataName"] = "SkillData";
        g_GlobalDataNames["ClassDataName"] = "ClassData";
        g_GlobalDataNames["PotionManagerName"] = "PotionManager";
        
        return true;
        
    } catch (...) {
        return false;
    }
}

/**
 * @brief Get global data name by type
 * 
 * Retrieves a global data name by its type identifier.
 * This is a utility function for accessing assigned names.
 * 
 * @param nameType The name type identifier
 * @return string containing the assigned name, or empty string if not found
 */
std::string GetGlobalDataName_Legacy(const std::string& nameType) {
    try {
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        
        auto it = g_GlobalDataNames.find(nameType);
        if (it != g_GlobalDataNames.end()) {
            return it->second;
        }
        
        return "";
        
    } catch (...) {
        return "";
    }
}

/**
 * @brief Set a specific global data name
 * 
 * Sets a specific global data name with the given value.
 * This is a utility function for manual name assignment.
 * 
 * @param nameType The name type identifier
 * @param name The name to assign
 * @return bool (true for success, false for failure)
 */
bool SetGlobalDataName_Legacy(const std::string& nameType, const std::string& name) {
    try {
        std::lock_guard<std::mutex> lock(g_GlobalDataNamesMutex);
        g_GlobalDataNames[nameType] = name;
        return true;
        
    } catch (...) {
        return false;
    }
}
