/*
 * QuestUtils.cpp - Quest Utility Functions Implementation
 * Utility functions for quest system operations
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CQuestMgr.h"
#include "../Headers/QuestStructures.h"
#include "../../common/Headers/Logger.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <regex>

namespace QuestUtils {

/**
 * Validate quest code format
 */
bool IsValidQuestCode(const char* questCode) {
    if (!questCode) {
        return false;
    }
    
    size_t len = strlen(questCode);
    if (len == 0 || len > QuestConstants::MAX_QUEST_CODE_LENGTH - 1) {
        return false;
    }
    
    // Quest codes should contain only alphanumeric characters and underscores
    for (size_t i = 0; i < len; ++i) {
        char c = questCode[i];
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }
    
    return true;
}

/**
 * Validate event code format
 */
bool IsValidEventCode(const char* eventCode) {
    if (!eventCode) {
        return false;
    }
    
    size_t len = strlen(eventCode);
    if (len == 0 || len > QuestConstants::MAX_EVENT_CODE_LENGTH - 1) {
        return false;
    }
    
    // Event codes should contain only alphanumeric characters and underscores
    for (size_t i = 0; i < len; ++i) {
        char c = eventCode[i];
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }
    
    return true;
}

/**
 * Get race-specific data offset
 */
size_t GetRaceDataOffset(uint8_t raceCode) {
    return QuestConstants::RACE_DATA_SIZE * static_cast<size_t>(raceCode);
}

/**
 * Format quest condition for logging
 */
std::string FormatConditionForLog(const QuestConditionNode* pCond) {
    if (!pCond) {
        return "NULL_CONDITION";
    }
    
    std::ostringstream oss;
    oss << "Type:" << static_cast<int>(pCond->m_nCondType)
        << " P1:" << pCond->m_nParam1
        << " P2:" << pCond->m_nParam2
        << " P3:" << pCond->m_nParam3
        << " Str:'" << pCond->m_strParam << "'"
        << " Negate:" << (pCond->m_bNegate ? "true" : "false");
    
    return oss.str();
}

/**
 * Calculate condition node address
 */
const QuestConditionNode* CalculateConditionNodeAddress(const char* baseAddr, int index) {
    if (!baseAddr || index < 0 || index >= QuestConstants::MAX_CONDITIONS_PER_EVENT) {
        Logger::Error("CalculateConditionNodeAddress - Invalid parameters: baseAddr=%p, index=%d", 
                     baseAddr, index);
        return nullptr;
    }
    
    try {
        // Calculate address: baseAddr + (72 * index + 24)
        size_t offset = QuestConstants::CONDITION_NODE_SIZE * static_cast<size_t>(index) + 
                       QuestConstants::CONDITION_NODE_OFFSET;
        
        const char* conditionAddr = baseAddr + offset;
        
        Logger::Debug("CalculateConditionNodeAddress - Index: %d, Offset: %zu, Addr: %p", 
                     index, offset, conditionAddr);
        
        return reinterpret_cast<const QuestConditionNode*>(conditionAddr);
        
    } catch (const std::exception& e) {
        Logger::Error("CalculateConditionNodeAddress - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Convert quest status to string
 */
std::string QuestStatusToString(QuestStatus status) {
    switch (status) {
        case QuestStatus::NotStarted: return "NotStarted";
        case QuestStatus::InProgress: return "InProgress";
        case QuestStatus::Completed: return "Completed";
        case QuestStatus::Failed: return "Failed";
        case QuestStatus::Abandoned: return "Abandoned";
        case QuestStatus::Repeatable: return "Repeatable";
        default: return "Unknown";
    }
}

/**
 * Convert quest happen type to string
 */
std::string QuestHappenTypeToString(QuestHappenType type) {
    switch (type) {
        case QuestHappenType::None: return "None";
        case QuestHappenType::NPC: return "NPC";
        case QuestHappenType::Item: return "Item";
        case QuestHappenType::Monster: return "Monster";
        case QuestHappenType::Area: return "Area";
        case QuestHappenType::Time: return "Time";
        case QuestHappenType::Level: return "Level";
        case QuestHappenType::Custom: return "Custom";
        default: return "Unknown";
    }
}

/**
 * Convert quest condition type to string
 */
std::string QuestConditionTypeToString(QuestConditionType type) {
    switch (type) {
        case QuestConditionType::None: return "None";
        case QuestConditionType::Level: return "Level";
        case QuestConditionType::Class: return "Class";
        case QuestConditionType::Item: return "Item";
        case QuestConditionType::Quest: return "Quest";
        case QuestConditionType::Skill: return "Skill";
        case QuestConditionType::Stat: return "Stat";
        case QuestConditionType::Time: return "Time";
        case QuestConditionType::Custom: return "Custom";
        default: return "Unknown";
    }
}

/**
 * Validate quest index data
 */
bool ValidateQuestIndexData(const NPCQuestIndexTempData* pData) {
    if (!pData) {
        return false;
    }
    
    if (!pData->IsValid()) {
        return false;
    }
    
    // Check each quest entry
    for (int i = 0; i < pData->GetQuestCount(); ++i) {
        const NPCQuestIndexData* pQuestData = pData->GetQuestData(i);
        if (!pQuestData || !pQuestData->IsValid()) {
            return false;
        }
    }
    
    return true;
}

/**
 * Generate unique quest identifier
 */
std::string GenerateQuestIdentifier(const char* questCode, uint32_t questIndex) {
    if (!questCode) {
        return "";
    }
    
    std::ostringstream oss;
    oss << questCode << "_" << questIndex;
    return oss.str();
}

/**
 * Parse quest identifier
 */
bool ParseQuestIdentifier(const std::string& identifier, std::string& questCode, uint32_t& questIndex) {
    size_t underscorePos = identifier.find_last_of('_');
    if (underscorePos == std::string::npos) {
        return false;
    }
    
    questCode = identifier.substr(0, underscorePos);
    
    try {
        questIndex = std::stoul(identifier.substr(underscorePos + 1));
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

/**
 * Calculate quest priority score
 */
int CalculateQuestPriorityScore(const NPCQuestIndexData* pQuestData) {
    if (!pQuestData || !pQuestData->IsValid()) {
        return 0;
    }
    
    // Simple priority calculation based on indices
    // Higher quest index = higher priority
    int score = static_cast<int>(pQuestData->dwQuestIndex);
    
    // Bonus for happen index
    score += static_cast<int>(pQuestData->dwQuestHappenIndex) / 10;
    
    return score;
}

/**
 * Sort quest index data by priority
 */
void SortQuestIndexDataByPriority(NPCQuestIndexTempData* pData) {
    if (!pData || pData->GetQuestCount() <= 1) {
        return;
    }
    
    try {
        // Create vector of indices and their priorities
        std::vector<std::pair<int, int>> priorities;
        for (int i = 0; i < pData->GetQuestCount(); ++i) {
            const NPCQuestIndexData* pQuestData = pData->GetQuestData(i);
            if (pQuestData) {
                int priority = CalculateQuestPriorityScore(pQuestData);
                priorities.emplace_back(priority, i);
            }
        }
        
        // Sort by priority (descending)
        std::sort(priorities.begin(), priorities.end(), 
                 [](const std::pair<int, int>& a, const std::pair<int, int>& b) {
                     return a.first > b.first;
                 });
        
        // Rearrange quest data based on sorted priorities
        std::array<NPCQuestIndexData, NPCQuestIndexTempData::MAX_QUEST_COUNT> sortedData;
        for (size_t i = 0; i < priorities.size(); ++i) {
            int originalIndex = priorities[i].second;
            const NPCQuestIndexData* pOriginalData = pData->GetQuestData(originalIndex);
            if (pOriginalData) {
                sortedData[i] = *pOriginalData;
            }
        }
        
        // Copy sorted data back
        for (size_t i = 0; i < priorities.size(); ++i) {
            pData->IndexData[i] = sortedData[i];
        }
        
        Logger::Debug("SortQuestIndexDataByPriority - Sorted %zu quests by priority", priorities.size());
        
    } catch (const std::exception& e) {
        Logger::Error("SortQuestIndexDataByPriority - Exception: %s", e.what());
    }
}

/**
 * Get quest statistics
 */
struct QuestStatistics {
    int totalQuests;
    int validQuests;
    int invalidQuests;
    uint32_t minQuestIndex;
    uint32_t maxQuestIndex;
    uint32_t minHappenIndex;
    uint32_t maxHappenIndex;
};

QuestStatistics GetQuestStatistics(const NPCQuestIndexTempData* pData) {
    QuestStatistics stats = {};
    
    if (!pData) {
        return stats;
    }
    
    stats.totalQuests = pData->GetQuestCount();
    stats.minQuestIndex = UINT32_MAX;
    stats.maxQuestIndex = 0;
    stats.minHappenIndex = UINT32_MAX;
    stats.maxHappenIndex = 0;
    
    for (int i = 0; i < pData->GetQuestCount(); ++i) {
        const NPCQuestIndexData* pQuestData = pData->GetQuestData(i);
        if (pQuestData && pQuestData->IsValid()) {
            stats.validQuests++;
            
            stats.minQuestIndex = std::min(stats.minQuestIndex, pQuestData->dwQuestIndex);
            stats.maxQuestIndex = std::max(stats.maxQuestIndex, pQuestData->dwQuestIndex);
            stats.minHappenIndex = std::min(stats.minHappenIndex, pQuestData->dwQuestHappenIndex);
            stats.maxHappenIndex = std::max(stats.maxHappenIndex, pQuestData->dwQuestHappenIndex);
        } else {
            stats.invalidQuests++;
        }
    }
    
    // Handle case where no valid quests were found
    if (stats.validQuests == 0) {
        stats.minQuestIndex = 0;
        stats.minHappenIndex = 0;
    }
    
    return stats;
}

/**
 * Log quest statistics
 */
void LogQuestStatistics(const NPCQuestIndexTempData* pData, const std::string& context) {
    if (!pData) {
        Logger::Warning("LogQuestStatistics - No quest data provided for context: %s", context.c_str());
        return;
    }
    
    QuestStatistics stats = GetQuestStatistics(pData);
    
    Logger::Info("Quest Statistics for %s:", context.c_str());
    Logger::Info("  Total Quests: %d", stats.totalQuests);
    Logger::Info("  Valid Quests: %d", stats.validQuests);
    Logger::Info("  Invalid Quests: %d", stats.invalidQuests);
    
    if (stats.validQuests > 0) {
        Logger::Info("  Quest Index Range: %u - %u", stats.minQuestIndex, stats.maxQuestIndex);
        Logger::Info("  Happen Index Range: %u - %u", stats.minHappenIndex, stats.maxHappenIndex);
    }
}

} // namespace QuestUtils
