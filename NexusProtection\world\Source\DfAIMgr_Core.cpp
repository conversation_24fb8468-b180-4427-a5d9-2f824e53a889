/*
 * DfAIMgr_Core.cpp - Core AI Manager Implementation
 * Core functionality and utility methods for DfAIMgr
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/DfAIMgr.h"
#include "../Headers/CMonsterAI.h"
#include "../Headers/CMonster.h"
#include "../Headers/CCharacter.h"
#include "../Headers/CMonsterSkill.h"
#include "../Headers/UsStateTBL.h"
#include "../Headers/Us_HFSM.h"
#include "../Headers/SF_Timer.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cmath>
#include <sstream>
#include <iomanip>

// External dependencies
extern uint32_t GetLoopTime();

/**
 * Get wisdom target based on case type
 * Refactored from GetWisdomTargetDfAIMgrSAPEAVCCharacterHPEAVCMonste_140152570.c
 */
CCharacter* DfAIMgr::GetWisdomTarget(AITargetCaseType targetCaseType, 
                                    CMonsterAI* pAI, CMonster* pMonster) {
    try {
        LogDebug("DfAIMgr::GetWisdomTarget - Getting wisdom target, type: " + 
                AIUtils::TargetCaseTypeToString(targetCaseType));
        
        switch (targetCaseType) {
            case AITargetCaseType::AttackTarget:
                // Return current attack target (equivalent to original case 0)
                return CMonster::GetAttackTarget(pMonster);
                
            case AITargetCaseType::Self:
                // Return monster as character (equivalent to original case 1)
                return static_cast<CCharacter*>(pMonster);
                
            case AITargetCaseType::AssistMonster:
                // Return assist monster (equivalent to original case 2)
                if (pAI && pAI->m_pAsistMonster) {
                    return static_cast<CCharacter*>(pAI->m_pAsistMonster);
                }
                LogDebug("GetWisdomTarget - No assist monster available");
                return nullptr;
                
            default:
                LogDebug("GetWisdomTarget - Invalid target case type");
                return nullptr;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("DfAIMgr::GetWisdomTarget - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Check special function delay time
 */
bool DfAIMgr::CheckSPFDelayTime(CMonsterAI* pAI, int attackType, uint32_t currentTime) {
    try {
        LogDebug("DfAIMgr::CheckSPFDelayTime - Checking delay time for attack type: %d", attackType);
        
        if (!pAI) {
            Logger::Error("CheckSPFDelayTime - Monster AI is null");
            return false;
        }
        
        // Get object from HFSM (equivalent to original Us_HFSM::GetObjectA call)
        void* hfsmObject = Us_HFSM::GetObjectA(reinterpret_cast<Us_HFSM*>(&pAI->vfptr));
        if (!hfsmObject) {
            LogDebug("CheckSPFDelayTime - No HFSM object available");
            return false;
        }
        
        // Get timer for attack type (equivalent to original CMonsterAI::GetTimer call)
        SF_Timer* timer = CMonsterAI::GetTimer(pAI, attackType);
        if (!timer) {
            LogDebug("CheckSPFDelayTime - No timer available for attack type: %d", attackType);
            return false;
        }
        
        // Check timer (equivalent to original SF_Timer::CheckTime call)
        uint32_t loopTime = (currentTime != 0) ? currentTime : GetLoopTime();
        bool timeReady = SF_Timer::CheckTime(timer, loopTime);
        
        LogDebug("CheckSPFDelayTime - Timer check result: %s", timeReady ? "ready" : "not ready");
        return timeReady;
        
    } catch (const std::exception& e) {
        Logger::Error("DfAIMgr::CheckSPFDelayTime - Exception: %s", e.what());
        return false;
    }
}

/**
 * Initialize HFSM (Hierarchical Finite State Machine)
 */
bool DfAIMgr::OnDFInitHFSM(UsStateTBL* pStateTBL, Us_HFSM* pHFSM) {
    try {
        LogDebug("DfAIMgr::OnDFInitHFSM - Initializing HFSM");
        
        if (!pHFSM) {
            Logger::Error("OnDFInitHFSM - HFSM is null");
            return false;
        }
        
        // Cast HFSM to MonsterAI (equivalent to original cast)
        CMonsterAI* monsterAI = static_cast<CMonsterAI*>(pHFSM);
        
        // Initialize timer 0 with value 0xBB8 (3000ms)
        SF_Timer* timer0 = CMonsterAI::GetTimer(monsterAI, 0);
        if (timer0) {
            SF_Timer::Set(timer0, 0xBB8u);
            LogDebug("OnDFInitHFSM - Timer 0 set to 3000ms");
        } else {
            Logger::Warning("OnDFInitHFSM - Timer 0 not available");
        }
        
        // Initialize timer 1 with value 0x7D0 (2000ms)
        SF_Timer* timer1 = CMonsterAI::GetTimer(monsterAI, 1);
        if (timer1) {
            SF_Timer::Set(timer1, 0x7D0u);
            LogDebug("OnDFInitHFSM - Timer 1 set to 2000ms");
        } else {
            Logger::Warning("OnDFInitHFSM - Timer 1 not available");
        }
        
        LogDebug("OnDFInitHFSM - HFSM initialization completed successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("DfAIMgr::OnDFInitHFSM - Exception: %s", e.what());
        return false;
    }
}

/**
 * Set AI attack parameters
 */
void DfAIMgr::SetAttackParams(const AIAttackParams& params) {
    s_attackParams = params;
    LogDebug("DfAIMgr::SetAttackParams - Attack parameters updated");
}

/**
 * Get current AI attack parameters
 */
const AIAttackParams& DfAIMgr::GetAttackParams() {
    return s_attackParams;
}

/**
 * Enable/disable AI debugging
 */
void DfAIMgr::SetDebugging(bool enable) {
    s_debuggingEnabled = enable;
    Logger::Info("DfAIMgr::SetDebugging - AI debugging %s", enable ? "enabled" : "disabled");
}

/**
 * Get AI statistics
 */
std::unordered_map<std::string, uint64_t> DfAIMgr::GetStatistics() {
    std::unordered_map<std::string, uint64_t> stats;
    
    stats["total_attack_checks"] = s_totalAttackChecks;
    stats["successful_attacks"] = s_successfulAttacks;
    stats["failed_attacks"] = s_failedAttacks;
    stats["targets_not_found"] = s_targetsNotFound;
    stats["out_of_range_attacks"] = s_outOfRangeAttacks;
    stats["skills_not_ready"] = s_skillsNotReady;
    
    // Calculate derived statistics
    if (s_totalAttackChecks > 0) {
        stats["success_rate_percent"] = (s_successfulAttacks * 100) / s_totalAttackChecks;
        stats["failure_rate_percent"] = (s_failedAttacks * 100) / s_totalAttackChecks;
    }
    
    return stats;
}

/**
 * Update statistics
 */
void DfAIMgr::UpdateStatistics(AIAttackResult result) {
    switch (result) {
        case AIAttackResult::AttackExecuted:
            s_successfulAttacks++;
            break;
        case AIAttackResult::NoAttack:
        case AIAttackResult::InvalidParameters:
            s_failedAttacks++;
            break;
        case AIAttackResult::TargetNotFound:
            s_targetsNotFound++;
            break;
        case AIAttackResult::OutOfRange:
            s_outOfRangeAttacks++;
            break;
        case AIAttackResult::SkillNotReady:
            s_skillsNotReady++;
            break;
    }
}

/**
 * Log AI operation
 */
void DfAIMgr::LogAIOperation(const std::string& operation, CMonster* pMonster, AIAttackResult result) {
    if (!s_debuggingEnabled) {
        return;
    }
    
    std::string monsterInfo = pMonster ? "Monster[Valid]" : "Monster[NULL]";
    std::string resultStr = AIUtils::AttackResultToString(result);
    
    Logger::Info("AI Operation: %s - %s - Result: %s", 
                operation.c_str(), monsterInfo.c_str(), resultStr.c_str());
}

/**
 * Log debug information
 */
void DfAIMgr::LogDebug(const std::string& message, const AIAttackContext* context) {
    if (!s_debuggingEnabled) {
        return;
    }
    
    if (context) {
        std::string contextStr = AIUtils::FormatAttackContext(*context);
        Logger::Debug("AI Debug: %s - Context: %s", message.c_str(), contextStr.c_str());
    } else {
        Logger::Debug("AI Debug: %s", message.c_str());
    }
}

// AI Utility Functions Implementation
namespace AIUtils {

/**
 * Convert AIAttackResult to string
 */
std::string AttackResultToString(AIAttackResult result) {
    switch (result) {
        case AIAttackResult::NoAttack: return "NoAttack";
        case AIAttackResult::AttackExecuted: return "AttackExecuted";
        case AIAttackResult::TargetNotFound: return "TargetNotFound";
        case AIAttackResult::OutOfRange: return "OutOfRange";
        case AIAttackResult::SkillNotReady: return "SkillNotReady";
        case AIAttackResult::InvalidParameters: return "InvalidParameters";
        default: return "Unknown";
    }
}

/**
 * Convert AITargetCaseType to string
 */
std::string TargetCaseTypeToString(AITargetCaseType caseType) {
    switch (caseType) {
        case AITargetCaseType::AttackTarget: return "AttackTarget";
        case AITargetCaseType::Self: return "Self";
        case AITargetCaseType::AssistMonster: return "AssistMonster";
        case AITargetCaseType::Invalid: return "Invalid";
        default: return "Unknown";
    }
}

/**
 * Calculate 3D distance between positions
 */
float Calculate3DDistance(const float pos1[3], const float pos2[3]) {
    if (!pos1 || !pos2) {
        return -1.0f;
    }
    
    float dx = pos2[0] - pos1[0];
    float dy = pos2[1] - pos1[1];
    float dz = pos2[2] - pos1[2];
    
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Calculate height difference between positions
 */
float CalculateHeightDifference(const float pos1[3], const float pos2[3]) {
    if (!pos1 || !pos2) {
        return -1.0f;
    }
    
    return std::abs(pos1[1] - pos2[1]);
}

/**
 * Validate position array
 */
bool IsValidPosition(const float position[3]) {
    if (!position) {
        return false;
    }
    
    for (int i = 0; i < 3; ++i) {
        if (std::isnan(position[i]) || std::isinf(position[i])) {
            return false;
        }
    }
    
    return true;
}

/**
 * Format attack context for logging
 */
std::string FormatAttackContext(const AIAttackContext& context) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2);
    oss << "Target:" << (context.target ? "Valid" : "NULL")
        << " Dist:" << context.targetDistance
        << " Height:" << context.heightDifference
        << " AdjDist:" << context.adjustedDistance
        << " Moving:" << (context.targetIsMoving ? "Yes" : "No")
        << " InRange:" << (context.inAttackRange ? "Yes" : "No");
    
    return oss.str();
}

/**
 * Format skill evaluation for logging
 */
std::string FormatSkillEvaluation(const AISkillEvaluation& evaluation) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2);
    oss << "Skill:" << (evaluation.skill ? "Valid" : "NULL")
        << " Delay:" << evaluation.skillDelay
        << " TimeSince:" << evaluation.timeSinceLastUse
        << " AttackDist:" << evaluation.attackDistance
        << " Ready:" << (evaluation.isReady ? "Yes" : "No")
        << " Conditions:" << (evaluation.meetsConditions ? "Met" : "NotMet");
    
    return oss.str();
}

} // namespace AIUtils
