/*
 * CRFWorldDatabase.cpp - World Database Management System
 * Refactored from 0CRFWorldDatabaseQEAAXZ_140489680.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CRFWorldDatabase.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>

/**
 * CRFWorldDatabase constructor
 */
CRFWorldDatabase::CRFWorldDatabase() 
    : CRFNewDatabase()
    , m_nCharacterOperations(0)
    , m_nQuestOperations(0)
    , m_nGuildOperations(0)
    , m_nInventoryOperations(0) {
    
    try {
        Log("CRFWorldDatabase initialized successfully");
        
    } catch (const std::exception& e) {
        ErrFmtLog("CRFWorldDatabase constructor failed: %s", e.what());
    }
}

/**
 * CRFWorldDatabase destructor
 */
CRFWorldDatabase::~CRFWorldDatabase() {
    try {
        Log("CRFWorldDatabase destructor called");
        
        // Clear caches
        std::lock_guard<std::mutex> lock(m_cacheMutex);
        m_characterCache.clear();
        m_questCache.clear();
        
    } catch (const std::exception& e) {
        ErrFmtLog("CRFWorldDatabase destructor error: %s", e.what());
    }
}

/**
 * Create character record
 */
bool CRFWorldDatabase::CreateCharacter(const CharacterDBData& characterData) {
    try {
        if (!ValidateCharacterData(characterData)) {
            ErrFmtLog("Invalid character data for creation");
            UpdateWorldStats(EWorldDBOperation::CHARACTER_CREATE, false);
            return false;
        }
        
        std::string query = BuildCharacterInsertQuery(characterData);
        bool success = ExecUpdateQuery(query, true);
        
        if (success) {
            // Cache the character data
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            m_characterCache[characterData.dwSerial] = characterData;
            Log("Character created successfully: " + characterData.szName);
        }
        
        UpdateWorldStats(EWorldDBOperation::CHARACTER_CREATE, success);
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("CreateCharacter failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::CHARACTER_CREATE, false);
        return false;
    }
}

/**
 * Update character data
 */
bool CRFWorldDatabase::UpdateCharacter(const CharacterDBData& characterData) {
    try {
        if (!ValidateCharacterData(characterData)) {
            ErrFmtLog("Invalid character data for update");
            UpdateWorldStats(EWorldDBOperation::CHARACTER_UPDATE, false);
            return false;
        }
        
        std::string query = BuildCharacterUpdateQuery(characterData);
        bool success = ExecUpdateQuery(query, true);
        
        if (success) {
            // Update cache
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            m_characterCache[characterData.dwSerial] = characterData;
            Log("Character updated successfully: " + characterData.szName);
        }
        
        UpdateWorldStats(EWorldDBOperation::CHARACTER_UPDATE, success);
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("UpdateCharacter failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::CHARACTER_UPDATE, false);
        return false;
    }
}

/**
 * Delete character record
 */
bool CRFWorldDatabase::DeleteCharacter(uint32_t dwSerial) {
    try {
        std::ostringstream query;
        query << "DELETE FROM tbl_character WHERE serial = " << dwSerial;
        
        bool success = ExecUpdateQuery(query.str(), true);
        
        if (success) {
            // Remove from cache
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            m_characterCache.erase(dwSerial);
            m_questCache.erase(dwSerial);
            Log("Character deleted successfully: " + std::to_string(dwSerial));
        }
        
        UpdateWorldStats(EWorldDBOperation::CHARACTER_DELETE, success);
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("DeleteCharacter failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::CHARACTER_DELETE, false);
        return false;
    }
}

/**
 * Load character data
 */
bool CRFWorldDatabase::LoadCharacter(uint32_t dwSerial, CharacterDBData& characterData) {
    try {
        // Check cache first
        {
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            auto it = m_characterCache.find(dwSerial);
            if (it != m_characterCache.end()) {
                characterData = it->second;
                return true;
            }
        }
        
        // Load from database
        std::ostringstream query;
        query << "SELECT serial, name, level, exp, class, hp, mp, pos_x, pos_y, pos_z, map_index "
              << "FROM tbl_character WHERE serial = " << dwSerial;
        
        DBQueryResult result = ExecSelectQuery(query.str());
        
        if (result.bSuccess && !result.resultData.empty()) {
            bool parseSuccess = ParseCharacterData(result, characterData);
            
            if (parseSuccess) {
                // Cache the loaded data
                std::lock_guard<std::mutex> lock(m_cacheMutex);
                m_characterCache[dwSerial] = characterData;
                Log("Character loaded successfully: " + std::to_string(dwSerial));
            }
            
            return parseSuccess;
        }
        
        ErrFmtLog("Character not found: %u", dwSerial);
        return false;
        
    } catch (const std::exception& e) {
        ErrFmtLog("LoadCharacter failed: %s", e.what());
        return false;
    }
}

/**
 * Insert quest record
 */
bool CRFWorldDatabase::Insert_Quest(uint32_t dwSerial) {
    try {
        std::ostringstream query;
        query << "{ CALL pInsert_Quest( " << dwSerial << " ) }";
        
        bool success = ExecUpdateQuery(query.str(), true);
        UpdateWorldStats(EWorldDBOperation::QUEST_UPDATE, success);
        
        if (success) {
            Log("Quest record inserted for character: " + std::to_string(dwSerial));
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("Insert_Quest failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::QUEST_UPDATE, false);
        return false;
    }
}

/**
 * Insert buddy record
 */
bool CRFWorldDatabase::Insert_Buddy(uint32_t dwSerial) {
    try {
        std::ostringstream query;
        query << "{ CALL pInsert_Buddy( " << dwSerial << " ) }";
        
        bool success = ExecUpdateQuery(query.str(), true);
        UpdateWorldStats(EWorldDBOperation::BUDDY_UPDATE, success);
        
        if (success) {
            Log("Buddy record inserted for character: " + std::to_string(dwSerial));
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("Insert_Buddy failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::BUDDY_UPDATE, false);
        return false;
    }
}

/**
 * Update post data
 */
bool CRFWorldDatabase::Update_Post(const std::string& szPostQuery) {
    try {
        if (szPostQuery.empty()) {
            ErrFmtLog("Empty post query provided");
            return false;
        }
        
        std::string sanitizedQuery = SanitizeString(szPostQuery);
        bool success = ExecUpdateQuery(sanitizedQuery, true);
        UpdateWorldStats(EWorldDBOperation::POST_UPDATE, success);
        
        if (success) {
            Log("Post updated successfully");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("Update_Post failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::POST_UPDATE, false);
        return false;
    }
}

/**
 * Create auto-mine table
 */
bool CRFWorldDatabase::create_automine_table() {
    try {
        // Check if table already exists
        if (TableExist("[dbo].[tbl_automine_inven]")) {
            Log("Auto-mine table already exists");
            return true;
        }
        
        // Create the table using stored procedure
        std::string query = "{ CALL pcreate_automine }";
        bool success = ExecUpdateQuery(query, true);
        
        if (success) {
            Log("Auto-mine table created successfully");
        } else {
            ErrFmtLog("Failed to create auto-mine table");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("create_automine_table failed: %s", e.what());
        return false;
    }
}

/**
 * Create amine personal inventory
 */
bool CRFWorldDatabase::create_amine_personal() {
    try {
        std::string query = "{ call pcreate_aminepersonal_inven }";
        bool success = ExecUpdateQuery(query, true);
        
        if (success) {
            Log("Amine personal inventory created successfully");
        } else {
            ErrFmtLog("Failed to create amine personal inventory");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("create_amine_personal failed: %s", e.what());
        return false;
    }
}

/**
 * Process character rebirth
 */
bool CRFWorldDatabase::Rebirth_Base(uint32_t dwCharacterSerial, const std::string& pwszName) {
    try {
        std::string sanitizedName = SanitizeString(pwszName);
        
        std::ostringstream query;
        query << "{ CALL pRebirth_Base( " << dwCharacterSerial << ", '" << sanitizedName << "' ) }";
        
        bool success = ExecUpdateQuery(query.str(), true);
        UpdateWorldStats(EWorldDBOperation::REBIRTH_UPDATE, success);
        
        if (success) {
            Log("Character rebirth processed: " + sanitizedName);
            
            // Invalidate character cache
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            m_characterCache.erase(dwCharacterSerial);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("Rebirth_Base failed: %s", e.what());
        UpdateWorldStats(EWorldDBOperation::REBIRTH_UPDATE, false);
        return false;
    }
}

/**
 * Insert boss cry record
 */
bool CRFWorldDatabase::Insert_BossCryRecord(uint32_t dwSerial) {
    try {
        std::ostringstream query;
        query << "Insert into tbl_CryMsg( Serial ) Values ( " << dwSerial << " )";
        
        bool success = ExecUpdateQuery(query.str(), true);
        
        if (success) {
            Log("Boss cry record inserted for character: " + std::to_string(dwSerial));
        }
        
        return success;
        
    } catch (const std::exception& e) {
        ErrFmtLog("Insert_BossCryRecord failed: %s", e.what());
        return false;
    }
}

/**
 * Execute world-specific stored procedure
 */
DBQueryResult CRFWorldDatabase::ExecWorldProcedure(const std::string& procedureName,
                                                 const std::vector<std::string>& parameters) {
    try {
        return ExecStoredProcedure(procedureName, parameters);

    } catch (const std::exception& e) {
        DBQueryResult result;
        result.bSuccess = false;
        result.errorCode = EDBErrorCode::QUERY_FAILED;
        result.errorMessage = "ExecWorldProcedure failed: " + std::string(e.what());
        ErrFmtLog("ExecWorldProcedure failed: %s", e.what());
        return result;
    }
}

/**
 * Get world database statistics
 */
std::string CRFWorldDatabase::GetWorldDBStats() const {
    try {
        std::ostringstream stats;
        stats << GetConnectionStats() << "\n";
        stats << "World Database Statistics:\n";
        stats << "  Character Operations: " << m_nCharacterOperations.load() << "\n";
        stats << "  Quest Operations: " << m_nQuestOperations.load() << "\n";
        stats << "  Guild Operations: " << m_nGuildOperations.load() << "\n";
        stats << "  Inventory Operations: " << m_nInventoryOperations.load() << "\n";

        std::lock_guard<std::mutex> lock(m_cacheMutex);
        stats << "  Cached Characters: " << m_characterCache.size() << "\n";
        stats << "  Cached Quest Data: " << m_questCache.size();

        return stats.str();

    } catch (const std::exception& e) {
        return "Error retrieving world database statistics: " + std::string(e.what());
    }
}

/**
 * Build character insert query
 */
std::string CRFWorldDatabase::BuildCharacterInsertQuery(const CharacterDBData& characterData) const {
    try {
        std::ostringstream query;
        query << "INSERT INTO tbl_character (serial, name, level, exp, class, hp, mp, pos_x, pos_y, pos_z, map_index) VALUES (";
        query << characterData.dwSerial << ", ";
        query << "'" << SanitizeString(characterData.szName) << "', ";
        query << characterData.dwLevel << ", ";
        query << characterData.dwExp << ", ";
        query << characterData.dwClass << ", ";
        query << characterData.dwHP << ", ";
        query << characterData.dwMP << ", ";
        query << std::fixed << std::setprecision(2) << characterData.fPosX << ", ";
        query << std::fixed << std::setprecision(2) << characterData.fPosY << ", ";
        query << std::fixed << std::setprecision(2) << characterData.fPosZ << ", ";
        query << characterData.dwMapIndex << ")";

        return query.str();

    } catch (const std::exception& e) {
        ErrFmtLog("BuildCharacterInsertQuery failed: %s", e.what());
        return "";
    }
}

/**
 * Build character update query
 */
std::string CRFWorldDatabase::BuildCharacterUpdateQuery(const CharacterDBData& characterData) const {
    try {
        std::ostringstream query;
        query << "UPDATE tbl_character SET ";
        query << "name = '" << SanitizeString(characterData.szName) << "', ";
        query << "level = " << characterData.dwLevel << ", ";
        query << "exp = " << characterData.dwExp << ", ";
        query << "class = " << characterData.dwClass << ", ";
        query << "hp = " << characterData.dwHP << ", ";
        query << "mp = " << characterData.dwMP << ", ";
        query << "pos_x = " << std::fixed << std::setprecision(2) << characterData.fPosX << ", ";
        query << "pos_y = " << std::fixed << std::setprecision(2) << characterData.fPosY << ", ";
        query << "pos_z = " << std::fixed << std::setprecision(2) << characterData.fPosZ << ", ";
        query << "map_index = " << characterData.dwMapIndex;
        query << " WHERE serial = " << characterData.dwSerial;

        return query.str();

    } catch (const std::exception& e) {
        ErrFmtLog("BuildCharacterUpdateQuery failed: %s", e.what());
        return "";
    }
}

/**
 * Parse character data from query result
 */
bool CRFWorldDatabase::ParseCharacterData(const DBQueryResult& result, CharacterDBData& characterData) const {
    try {
        if (result.resultData.empty() || result.resultData[0].size() < 11) {
            return false;
        }

        const auto& row = result.resultData[0];

        characterData.dwSerial = static_cast<uint32_t>(std::stoul(row[0]));
        characterData.szName = row[1];
        characterData.dwLevel = static_cast<uint32_t>(std::stoul(row[2]));
        characterData.dwExp = static_cast<uint32_t>(std::stoul(row[3]));
        characterData.dwClass = static_cast<uint32_t>(std::stoul(row[4]));
        characterData.dwHP = static_cast<uint32_t>(std::stoul(row[5]));
        characterData.dwMP = static_cast<uint32_t>(std::stoul(row[6]));
        characterData.fPosX = std::stof(row[7]);
        characterData.fPosY = std::stof(row[8]);
        characterData.fPosZ = std::stof(row[9]);
        characterData.dwMapIndex = static_cast<uint32_t>(std::stoul(row[10]));

        return true;

    } catch (const std::exception& e) {
        ErrFmtLog("ParseCharacterData failed: %s", e.what());
        return false;
    }
}

/**
 * Update operation statistics
 */
void CRFWorldDatabase::UpdateWorldStats(EWorldDBOperation operation, bool success) {
    try {
        switch (operation) {
            case EWorldDBOperation::CHARACTER_CREATE:
            case EWorldDBOperation::CHARACTER_UPDATE:
            case EWorldDBOperation::CHARACTER_DELETE:
                m_nCharacterOperations++;
                break;

            case EWorldDBOperation::QUEST_UPDATE:
                m_nQuestOperations++;
                break;

            case EWorldDBOperation::GUILD_UPDATE:
                m_nGuildOperations++;
                break;

            case EWorldDBOperation::INVENTORY_UPDATE:
            case EWorldDBOperation::AUTOMINE_UPDATE:
                m_nInventoryOperations++;
                break;

            default:
                break;
        }

        // Update base class statistics
        UpdateStats(success);

    } catch (const std::exception& e) {
        ErrFmtLog("UpdateWorldStats failed: %s", e.what());
    }
}

/**
 * Validate character data
 */
bool CRFWorldDatabase::ValidateCharacterData(const CharacterDBData& characterData) const {
    try {
        if (characterData.dwSerial == 0) {
            return false;
        }

        if (characterData.szName.empty() || characterData.szName.length() > 50) {
            return false;
        }

        if (characterData.dwLevel == 0 || characterData.dwLevel > 1000) {
            return false;
        }

        if (characterData.dwClass > 10) {  // Assuming max 10 classes
            return false;
        }

        if (characterData.dwHP == 0 || characterData.dwMP == 0) {
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        ErrFmtLog("ValidateCharacterData failed: %s", e.what());
        return false;
    }
}

/**
 * Sanitize string for SQL query
 */
std::string CRFWorldDatabase::SanitizeString(const std::string& input) const {
    try {
        std::string sanitized = input;

        // Replace single quotes with double quotes to prevent SQL injection
        std::regex singleQuote("'");
        sanitized = std::regex_replace(sanitized, singleQuote, "''");

        // Remove or escape other potentially dangerous characters
        std::regex semicolon(";");
        sanitized = std::regex_replace(sanitized, semicolon, "");

        std::regex doubleDash("--");
        sanitized = std::regex_replace(sanitized, doubleDash, "");

        return sanitized;

    } catch (const std::exception& e) {
        ErrFmtLog("SanitizeString failed: %s", e.what());
        return input;  // Return original if sanitization fails
    }
}
