/**
 * @file CMainThread_NetworkInit_Legacy.cpp
 * @brief Legacy compatibility wrapper for CMainThread::NetworkInit
 * 
 * This file provides the exact original function signature for backward compatibility
 * with existing code that calls the original decompiled function.
 * 
 * Original function: NetworkInitCMainThreadAEAA_NXZ_1401EB330.c
 * Address: 0x1401EB330
 * Signature: char __fastcall CMainThread::NetworkInit(CMainThread *this)
 */

#include "../Headers/CMainThread_NetworkInit.h"
#include "../Headers/CMainThread.h"
#include <cstdint>
#include <iostream>
#include <cstring>

// External function declarations (to be properly linked)
extern uint64_t _security_cookie;
extern void MyMessageBox(const char* title, const char* format, ...);

/**
 * @brief Legacy NetworkInit function with exact original signature
 * This maintains 100% compatibility with the original decompiled function
 * 
 * @param this_ptr Pointer to CMainThread instance (original 'this' parameter)
 * @return char (1 for success, 0 for failure)
 */
extern "C" char __fastcall CMainThread_NetworkInit_Original(CMainThread* this_ptr) {
    // Security cookie setup (equivalent to original stack protection)
    uint64_t stackBuffer[252]; // Equivalent to original v4 buffer (252 * 4 bytes)
    uint64_t securityCookie = reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie;
    
    // Initialize stack buffer (equivalent to original initialization loop)
    for (size_t i = 0; i < 252; ++i) {
        reinterpret_cast<uint32_t*>(stackBuffer)[i] = 0xCCCCCCCC; // -858993460 in original
    }
    
    // Call the modern implementation
    char result = 0;
    if (this_ptr) {
        result = CMainThreadNetworkInit::NetworkInit_Legacy(this_ptr);
    }
    
    // Verify security cookie (equivalent to original stack protection check)
    if ((reinterpret_cast<uint64_t>(stackBuffer) ^ _security_cookie) != securityCookie) {
        // Stack corruption detected - this would trigger security handler in original
        MyMessageBox("Security Error", "Stack corruption detected in CMainThread::NetworkInit");
        return 0;
    }
    
    return result;
}

/**
 * @brief Alternative legacy wrapper that can be used as a drop-in replacement
 * This provides the original calling convention and behavior
 */
char CMainThread::NetworkInit_OriginalBehavior() {
    try {
        // Replicate original variable setup equivalent to the original stack variables
        _NET_TYPE_PARAM networkParams[4];
        
        // Initialize all parameters (equivalent to original vector constructor iterator)
        for (auto& param : networkParams) {
            param = _NET_TYPE_PARAM();
        }
        
        // Configure Client Line (equivalent to original __t setup)
        auto& clientParam = networkParams[0];
        strcpy_s(clientParam.m_szModuleName, "ClientLine");
        clientParam.m_wSocketMaxNum = 2532;
        clientParam.m_byRecvThreadNum = 1;
        clientParam.m_byRecvSleepTime = 1;
        clientParam.m_dwSendBufferSize = 1000000;
        clientParam.m_dwRecvBufferSize = 1000000;
        clientParam.m_bServer = true;
        clientParam.m_bRealSockCheck = false;
        clientParam.m_bSystemLogFile = true;
        clientParam.m_bSvrToS = true;
        clientParam.m_bOddMsgWriteLog = true;
        clientParam.m_bAnSyncConnect = true;
        
        // Check release service mode (equivalent to original IsReleaseServiceMode check)
        if (CMainThread_IsReleaseServiceMode(this)) {
            clientParam.m_byRecvSleepTime = 8; // Original v10 = 8
        }
        
        // Configure Account Line (equivalent to original Dest setup)
        auto& accountParam = networkParams[1];
        strcpy_s(accountParam.m_szModuleName, "AccountLine");
        accountParam.m_wSocketMaxNum = 2;
        accountParam.m_byRecvThreadNum = 1;
        accountParam.m_byRecvSleepTime = 1;
        accountParam.m_dwSendBufferSize = 1000000;
        accountParam.m_dwRecvBufferSize = 1000000;
        accountParam.m_bServer = true;
        accountParam.m_bRealSockCheck = false;
        accountParam.m_bSystemLogFile = true;
        accountParam.m_bSvrToS = true;
        accountParam.m_bOddMsgWriteLog = true;
        accountParam.m_bAnSyncConnect = true;
        
        // Configure Web Line (equivalent to original v32 setup)
        auto& webParam = networkParams[2];
        strcpy_s(webParam.m_szModuleName, "WebLine");
        webParam.m_wSocketMaxNum = 1;
        webParam.m_byRecvThreadNum = 1;
        webParam.m_byRecvSleepTime = 1;
        webParam.m_dwSendBufferSize = 1000000;
        webParam.m_dwRecvBufferSize = 1000000;
        webParam.m_bServer = true;
        webParam.m_bRealSockCheck = false;
        webParam.m_bSystemLogFile = true;
        webParam.m_bSvrToS = true;
        webParam.m_bOddMsgWriteLog = true;
        webParam.m_bAnSyncConnect = true;
        
        // Configure Billing Line (equivalent to original v56 setup)
        auto& billingParam = networkParams[3];
        strcpy_s(billingParam.m_szModuleName, "BillingLine");
        billingParam.m_wSocketMaxNum = 1;
        billingParam.m_byRecvThreadNum = 1;
        billingParam.m_byRecvSleepTime = 1;
        billingParam.m_dwSendBufferSize = 1000000;
        billingParam.m_dwRecvBufferSize = 1000000;
        billingParam.m_bServer = true;
        billingParam.m_bRealSockCheck = false;
        billingParam.m_bSystemLogFile = true;
        billingParam.m_bSvrToS = true;
        billingParam.m_bOddMsgWriteLog = true;
        billingParam.m_bAnSyncConnect = true;
        
        // Call CNetWorking::SetNetSystem (equivalent to original call)
        if (CNetWorking_SetNetSystem(&g_Network, 4, networkParams, "GameServer", "..\\ZoneServerLog\\NetLog")) {
            // Add passable packets (equivalent to original AddPassablePacket call)
            CMainThread_AddPassablePacket(this);
            return 1;
        } else {
            return 0;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NetworkInit_OriginalBehavior: " << e.what() << std::endl;
        return 0;
    }
}

/**
 * @brief Exact replication of original network parameter setup
 * This function replicates the exact original behavior for maximum compatibility
 */
char CMainThread::NetworkInit_ExactOriginal() {
    try {
        // Exact replication of original variable declarations and initialization
        _NET_TYPE_PARAM __t, Dest, v32, v56;
        
        // Initialize __t (ClientLine) - equivalent to original __t setup
        __t = _NET_TYPE_PARAM();
        __t.m_wSocketMaxNum = 2532;
        __t.m_byRecvThreadNum = 1;
        __t.m_byRecvSleepTime = 1;
        __t.m_dwSendBufferSize = 1000000;
        __t.m_dwRecvBufferSize = 1000000;
        __t.m_bServer = true;
        __t.m_bRealSockCheck = false;
        __t.m_bSystemLogFile = true;
        __t.m_bSvrToS = true;
        __t.m_bOddMsgWriteLog = true;
        __t.m_bAnSyncConnect = true;
        
        if (CMainThread_IsReleaseServiceMode(this)) {
            __t.m_byRecvSleepTime = 8;
        }
        
        sprintf_s(__t.m_szModuleName, "ClientLine");
        
        // Initialize Dest (AccountLine) - equivalent to original Dest setup
        Dest = _NET_TYPE_PARAM();
        sprintf_s(Dest.m_szModuleName, "AccountLine");
        Dest.m_wSocketMaxNum = 2;
        Dest.m_byRecvThreadNum = 1;
        Dest.m_byRecvSleepTime = 1;
        Dest.m_dwSendBufferSize = 1000000;
        Dest.m_dwRecvBufferSize = 1000000;
        Dest.m_bServer = true;
        Dest.m_bRealSockCheck = false;
        Dest.m_bSystemLogFile = true;
        Dest.m_bSvrToS = true;
        Dest.m_bOddMsgWriteLog = true;
        Dest.m_bAnSyncConnect = true;
        
        // Initialize v32 (WebLine) - equivalent to original v32 setup
        v32 = _NET_TYPE_PARAM();
        sprintf_s(v32.m_szModuleName, "WebLine");
        v32.m_wSocketMaxNum = 1;
        v32.m_byRecvThreadNum = 1;
        v32.m_byRecvSleepTime = 1;
        v32.m_dwSendBufferSize = 1000000;
        v32.m_dwRecvBufferSize = 1000000;
        v32.m_bServer = true;
        v32.m_bRealSockCheck = false;
        v32.m_bSystemLogFile = true;
        v32.m_bSvrToS = true;
        v32.m_bOddMsgWriteLog = true;
        v32.m_bAnSyncConnect = true;
        
        // Initialize v56 (BillingLine) - equivalent to original v56 setup
        v56 = _NET_TYPE_PARAM();
        sprintf_s(v56.m_szModuleName, "BillingLine");
        v56.m_wSocketMaxNum = 1;
        v56.m_byRecvThreadNum = 1;
        v56.m_byRecvSleepTime = 1;
        v56.m_dwSendBufferSize = 1000000;
        v56.m_dwRecvBufferSize = 1000000;
        v56.m_bServer = true;
        v56.m_bRealSockCheck = false;
        v56.m_bSystemLogFile = true;
        v56.m_bSvrToS = true;
        v56.m_bOddMsgWriteLog = true;
        v56.m_bAnSyncConnect = true;
        
        // Create parameter array
        _NET_TYPE_PARAM params[4] = { __t, Dest, v32, v56 };
        
        // Call SetNetSystem exactly as in original
        if (CNetWorking_SetNetSystem(&g_Network, 4, params, "GameServer", "..\\ZoneServerLog\\NetLog")) {
            CMainThread_AddPassablePacket(this);
            return 1;
        } else {
            return 0;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NetworkInit_ExactOriginal: " << e.what() << std::endl;
        return 0;
    }
}
