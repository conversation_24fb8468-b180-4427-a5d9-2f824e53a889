/**
 * @file CMainThread_gm_MainThreadControl_Legacy.cpp
 * @brief Legacy compatibility and string conversion functions
 * 
 * Contains legacy compatibility functions and string conversion utilities
 * for the main thread control system.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_gm_MainThreadControl.h"
#include "CMainThread.h"
#include <iostream>
#include <format>
#include <thread>

/**
 * @brief Set world service state
 * @param mainThread Pointer to CMainThread instance
 * @param enabled Whether to enable world service
 */
void CMainThreadControl::SetWorldServiceState(CMainThread* mainThread, bool enabled) {
    if (!mainThread) {
        return;
    }
    
    try {
        // Set the world service flag
        mainThread->m_bWorldService = enabled;
        
        std::cout << "[INFO] World service " << (enabled ? "enabled" : "disabled") << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to set world service state: " << e.what() << std::endl;
    }
}

/**
 * @brief Set world open state
 * @param mainThread Pointer to CMainThread instance
 * @param open Whether to open the world
 */
void CMainThreadControl::SetWorldOpenState(CMainThread* mainThread, bool open) {
    if (!mainThread) {
        return;
    }
    
    try {
        // Set the world open flag
        mainThread->m_bWorldOpen = open;
        
        std::cout << "[INFO] World " << (open ? "opened" : "closed") << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to set world open state: " << e.what() << std::endl;
    }
}

/**
 * @brief Initialize thread flags
 * @param mainThread Pointer to CMainThread instance
 */
void CMainThreadControl::InitializeThreadFlags(CMainThread* mainThread) {
    if (!mainThread) {
        return;
    }
    
    try {
        // Initialize all thread control flags
        mainThread->m_bWorldService = false;
        mainThread->m_bWorldOpen = false;
        mainThread->m_bRuleThread = false;
        mainThread->m_bDQSThread = false;
        
        std::cout << "[INFO] Thread flags initialized" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to initialize thread flags: " << e.what() << std::endl;
    }
}

/**
 * @brief Validate command
 * @param command The command to validate
 * @return true if valid, false otherwise
 */
bool CMainThreadControl::ValidateCommand(ThreadControlCommand command) const {
    return static_cast<uint32_t>(command) < static_cast<uint32_t>(ThreadControlCommand::MAX_COMMANDS);
}

/**
 * @brief Validate state
 * @param requiredState The required state
 * @return true if current state matches required state
 */
bool CMainThreadControl::ValidateState(ThreadState requiredState) const {
    return GetCurrentState() == requiredState;
}

/**
 * @brief Monitoring thread function
 */
void CMainThreadControl::MonitoringThreadFunction() {
    try {
        std::cout << "[INFO] Thread control monitoring started" << std::endl;
        
        while (m_monitoringEnabled.load() && !m_shutdownRequested.load()) {
            try {
                // Check thread health
                bool healthy = IsHealthy();
                ThreadState state = GetCurrentState();
                
                // Log status periodically
                static auto lastStatusLog = std::chrono::steady_clock::now();
                auto now = std::chrono::steady_clock::now();
                auto timeSinceLastLog = std::chrono::duration_cast<std::chrono::minutes>(now - lastStatusLog);
                
                if (timeSinceLastLog >= std::chrono::minutes(5)) {
                    std::cout << "[MONITOR] Thread status: " << ThreadStateToString(state) 
                              << " (Health: " << (healthy ? "OK" : "FAIL") << ")" << std::endl;
                    lastStatusLog = now;
                }
                
                // Check for error states
                if (state == ThreadState::Error) {
                    std::cout << "[MONITOR] Error state detected, attempting recovery..." << std::endl;
                    // Could implement automatic recovery here
                }
                
                // Sleep for monitoring interval
                std::this_thread::sleep_for(m_controlConfig.statusUpdateInterval);
                
            } catch (const std::exception& e) {
                std::cerr << "[MONITOR] Exception in monitoring thread: " << e.what() << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        
        std::cout << "[INFO] Thread control monitoring stopped" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Fatal exception in monitoring thread: " << e.what() << std::endl;
    }
}

/**
 * @brief Network control functions (stubs for future implementation)
 */
ThreadControlResult CMainThreadControl::StartNetworkingInternal() {
    std::cout << "[INFO] Network services started" << std::endl;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::StopNetworkingInternal() {
    std::cout << "[INFO] Network services stopped" << std::endl;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::ResetConnectionsInternal() {
    std::cout << "[INFO] Network connections reset" << std::endl;
    return ThreadControlResult::Success;
}

/**
 * @brief Data control functions (stubs for future implementation)
 */
ThreadControlResult CMainThreadControl::ReloadDataInternal() {
    std::cout << "[INFO] Data reloaded" << std::endl;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::ValidateDataInternal() {
    std::cout << "[INFO] Data validated" << std::endl;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::BackupDataInternal() {
    std::cout << "[INFO] Data backed up" << std::endl;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::ForceRestartInternal() {
    std::cout << "[WARNING] Force restart initiated" << std::endl;
    return ShutdownControl();
}

ThreadControlResult CMainThreadControl::WaitForCompletionInternal() {
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::SignalReadyInternal() {
    ChangeState(ThreadState::Ready);
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::SynchronizeInternal() {
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::SetThreadPriority(uint32_t priority) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_controlConfig.threadPriority = priority;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::SetThreadAffinity(uint64_t affinity) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_controlConfig.threadAffinity = affinity;
    return ThreadControlResult::Success;
}

ThreadControlResult CMainThreadControl::SetCommandTimeout(std::chrono::milliseconds timeout) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_controlConfig.commandTimeout = timeout;
    return ThreadControlResult::Success;
}

/**
 * @brief Convert ThreadControlCommand enum to string for logging
 * @param command The command
 * @return String representation of the command
 */
std::string ThreadControlCommandToString(ThreadControlCommand command) {
    switch (command) {
        case ThreadControlCommand::Initialize: return "Initialize";
        case ThreadControlCommand::Start: return "Start";
        case ThreadControlCommand::Stop: return "Stop";
        case ThreadControlCommand::Pause: return "Pause";
        case ThreadControlCommand::Resume: return "Resume";
        case ThreadControlCommand::Shutdown: return "Shutdown";
        case ThreadControlCommand::GetStatus: return "GetStatus";
        case ThreadControlCommand::GetStatistics: return "GetStatistics";
        case ThreadControlCommand::CheckHealth: return "CheckHealth";
        case ThreadControlCommand::SetPriority: return "SetPriority";
        case ThreadControlCommand::SetAffinity: return "SetAffinity";
        case ThreadControlCommand::SetTimeout: return "SetTimeout";
        case ThreadControlCommand::WaitForCompletion: return "WaitForCompletion";
        case ThreadControlCommand::SignalReady: return "SignalReady";
        case ThreadControlCommand::Synchronize: return "Synchronize";
        case ThreadControlCommand::StartNetworking: return "StartNetworking";
        case ThreadControlCommand::StopNetworking: return "StopNetworking";
        case ThreadControlCommand::ResetConnections: return "ResetConnections";
        case ThreadControlCommand::ReloadData: return "ReloadData";
        case ThreadControlCommand::ValidateData: return "ValidateData";
        case ThreadControlCommand::BackupData: return "BackupData";
        case ThreadControlCommand::EnableWorldService: return "EnableWorldService";
        case ThreadControlCommand::DisableWorldService: return "DisableWorldService";
        case ThreadControlCommand::OpenWorld: return "OpenWorld";
        case ThreadControlCommand::CloseWorld: return "CloseWorld";
        case ThreadControlCommand::StartMonitoring: return "StartMonitoring";
        case ThreadControlCommand::StopMonitoring: return "StopMonitoring";
        case ThreadControlCommand::ReportStatus: return "ReportStatus";
        case ThreadControlCommand::EmergencyStop: return "EmergencyStop";
        case ThreadControlCommand::ForceRestart: return "ForceRestart";
        case ThreadControlCommand::SafeShutdown: return "SafeShutdown";
        default: return "Unknown";
    }
}

/**
 * @brief Convert ThreadControlResult enum to string for logging
 * @param result The result
 * @return String representation of the result
 */
std::string ThreadControlResultToString(ThreadControlResult result) {
    switch (result) {
        case ThreadControlResult::Success: return "Success";
        case ThreadControlResult::Failure: return "Failure";
        case ThreadControlResult::InvalidCommand: return "InvalidCommand";
        case ThreadControlResult::InvalidState: return "InvalidState";
        case ThreadControlResult::Timeout: return "Timeout";
        case ThreadControlResult::AccessDenied: return "AccessDenied";
        case ThreadControlResult::SystemError: return "SystemError";
        case ThreadControlResult::NetworkError: return "NetworkError";
        case ThreadControlResult::DataError: return "DataError";
        case ThreadControlResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief Convert ThreadState enum to string for logging
 * @param state The state
 * @return String representation of the state
 */
std::string ThreadStateToString(ThreadState state) {
    switch (state) {
        case ThreadState::Uninitialized: return "Uninitialized";
        case ThreadState::Initializing: return "Initializing";
        case ThreadState::Ready: return "Ready";
        case ThreadState::Running: return "Running";
        case ThreadState::Paused: return "Paused";
        case ThreadState::Stopping: return "Stopping";
        case ThreadState::Stopped: return "Stopped";
        case ThreadState::Error: return "Error";
        case ThreadState::ShuttingDown: return "ShuttingDown";
        case ThreadState::Shutdown: return "Shutdown";
        default: return "Unknown";
    }
}
