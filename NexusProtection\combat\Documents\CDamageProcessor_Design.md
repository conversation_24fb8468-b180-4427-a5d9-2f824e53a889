# CDamageProcessor - Advanced Damage Calculation System

## Overview

The `CDamageProcessor` class is a modern C++ refactoring of the complex damage calculation systems found in the original decompiled code. It provides a unified, extensible framework for handling various types of damage calculations including area damage, flash damage, and sector damage.

## Original Files Refactored

This system consolidates and modernizes the following decompiled functions:

- `AreaDamageProcCAttackQEAAXHHPEAMH_NZ_14016C320.c`
- `FlashDamageProcCAttackIEAAXHHHH_NZ_14016B6F0.c`
- `SectorDamageProcCAttackQEAAXHHHHHH_NZ_14016D1A0.c`
- Various damage calculation utilities

## Key Features

### 1. Unified Damage Processing
- Single class handles all damage types
- Consistent parameter structures
- Standardized result format

### 2. Modern C++ Design
- RAII resource management
- STL containers for efficiency
- Exception safety
- Template-based extensibility

### 3. Damage Types Supported
- **Physical Damage**: Standard melee/ranged attacks
- **Magical Damage**: Spell-based attacks
- **Elemental Damage**: Fire, ice, lightning effects
- **Piercing Damage**: Armor-penetrating attacks
- **Area Damage**: Affects multiple targets in radius
- **Flash Damage**: Directional cone attacks
- **Sector Damage**: Angular sector attacks

### 4. Advanced Calculations
- Distance-based damage falloff
- Angle-based damage reduction
- Critical hit calculations
- Block/dodge mechanics
- Target validation

## Architecture

### Core Components

```cpp
class CDamageProcessor {
    // Core processing methods
    std::vector<DamageResult> ProcessAreaDamage(const AreaDamageParams& params, _attack_param* pAttackParam);
    std::vector<DamageResult> ProcessFlashDamage(const FlashDamageParams& params, _attack_param* pAttackParam);
    std::vector<DamageResult> ProcessSectorDamage(const SectorDamageParams& params, _attack_param* pAttackParam);
    
    // Utility methods
    DamageResult CalculateTargetDamage(CCharacter* pTarget, int nBaseDamage, EDamageType damageType, float fDistance, float fAngle);
    bool ApplyDamage(const DamageResult& result);
    std::vector<CCharacter*> GetTargetsInArea(float centerX, float centerY, float radius);
};
```

### Parameter Structures

#### AreaDamageParams
- `nLimitRadius`: Maximum damage radius
- `nAttPower`: Base attack power
- `targetArea`: 3D coordinates of damage center
- `nEffAttPower`: Effective attack power
- `bUseEffBullet`: Effect bullet flag
- `bIgnoreAllies`: Ally targeting flag
- `fDamageFalloff`: Distance falloff rate

#### FlashDamageParams
- `nLimDist`: Maximum damage distance
- `nAttPower`: Base attack power
- `nAngle`: Attack direction angle
- `nEffAttPower`: Effective attack power
- `bUseEffBullet`: Effect bullet flag
- `bIgnoreAllies`: Ally targeting flag
- `fAngleTolerance`: Cone angle tolerance

#### SectorDamageParams
- `nLimitRadius`: Maximum damage radius
- `nAttPower`: Base attack power
- `nAngle`: Center angle
- `nEffAttPower`: Effective attack power
- `nStartAngle`: Sector start angle
- `nEndAngle`: Sector end angle
- `bUseEffBullet`: Effect bullet flag
- `bIgnoreAllies`: Ally targeting flag
- `fDamageFalloff`: Distance falloff rate

### Result Structure

```cpp
struct DamageResult {
    CCharacter* pTarget;        // Target character
    int nDamage;               // Final damage amount
    bool bCritical;            // Critical hit flag
    bool bBlocked;             // Damage blocked flag
    bool bMissed;              // Attack missed flag
    EDamageType damageType;    // Type of damage
    float fDistance;           // Distance to target
    float fAngle;              // Angle to target
};
```

## Integration with CMonsterAttack

The damage processor is integrated into the existing `CMonsterAttack` system through new methods:

```cpp
void CAttack::AreaDamageProc(int nLimitRadius, int nAttPower, float* pTar, int nEffAttPower, bool bUseEffBullet);
void CAttack::FlashDamageProc(int nLimDist, int nAttPower, int nAngle, int nEffAttPower, bool bUseEffBullet);
void CAttack::SectorDamageProc(int nLimitRadius, int nAttPower, int nAngle, int nEffAttPower, int nStartAngle, int nEndAngle, bool bUseEffBullet);
```

## Performance Optimizations

### 1. Memory Management
- Pre-allocated result vectors
- RAII for automatic cleanup
- Minimal dynamic allocations

### 2. Calculation Efficiency
- Fast distance calculations using squared distances where possible
- Optimized angle calculations
- Early termination for invalid targets

### 3. Target Selection
- Spatial partitioning for large target sets
- Efficient range queries
- Cached target validation

## Error Handling

### Exception Safety
- All methods provide strong exception safety guarantee
- Resource cleanup guaranteed via RAII
- Graceful degradation on errors

### Validation
- Parameter validation at entry points
- Target validity checking
- Range validation for all numeric inputs

## Usage Examples

### Area Damage
```cpp
CDamageProcessor processor(pAttacker);
AreaDamageParams params;
params.nLimitRadius = 150;
params.nAttPower = 100;
params.targetArea = {x, y, z};
params.fDamageFalloff = 0.8f;

auto results = processor.ProcessAreaDamage(params, &attackParam);
for (const auto& result : results) {
    processor.ApplyDamage(result);
}
```

### Flash Damage
```cpp
CDamageProcessor processor(pAttacker);
FlashDamageParams params;
params.nLimDist = 200;
params.nAttPower = 120;
params.nAngle = 45;
params.fAngleTolerance = 15.0f;

auto results = processor.ProcessFlashDamage(params, &attackParam);
```

### Sector Damage
```cpp
CDamageProcessor processor(pAttacker);
SectorDamageParams params;
params.nLimitRadius = 180;
params.nAttPower = 90;
params.nStartAngle = 30;
params.nEndAngle = 120;

auto results = processor.ProcessSectorDamage(params, &attackParam);
```

## Future Enhancements

### Planned Features
1. **Damage Over Time (DoT)**: Support for persistent damage effects
2. **Damage Shields**: Absorption and reflection mechanics
3. **Conditional Damage**: Context-sensitive damage modifiers
4. **Damage Logging**: Detailed damage event tracking
5. **Performance Profiling**: Built-in performance monitoring

### Extensibility Points
- Custom damage type registration
- Pluggable damage calculation algorithms
- Event-driven damage processing
- Scriptable damage effects

## Testing Strategy

### Unit Tests
- Individual method testing
- Parameter validation testing
- Edge case handling
- Performance benchmarking

### Integration Tests
- Full damage processing workflows
- Multi-target scenarios
- Complex geometric calculations
- Error condition handling

## Compatibility

### Compiler Support
- Visual Studio 2022 (v143 toolset)
- C++17/C++20 standard compliance
- Windows 10/11 compatibility

### Dependencies
- STL containers and algorithms
- Standard math library
- Existing game object system
- Logger and error handling systems
