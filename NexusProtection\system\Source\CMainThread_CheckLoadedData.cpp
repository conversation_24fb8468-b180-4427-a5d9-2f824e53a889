/**
 * @file CMainThread_CheckLoadedData.cpp
 * @brief Implementation of data validation system for CMainThread
 * 
 * Refactored from check_loaded_data function
 * This handles the validation of all loaded game data to ensure integrity
 * and completeness before server startup.
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "CMainThread_CheckLoadedData.h"
#include "CMainThread.h"
#include "CRecordData.h"
#include <iostream>
#include <format>
#include <algorithm>
#include <cassert>

// External security cookie for stack protection
extern uintptr_t _security_cookie;

// External message box function
extern void MyMessageBox(const char* title, const char* message);

// External manager validation functions
extern bool CPotionMgr_IsValid(CPotionMgr* mgr);
extern bool CQuestMgr_IsValid(CQuestMgr* mgr);
extern bool ItemCombineMgr_CheckLoadData();
extern bool CPcBangFavor_IsValid(CPcBangFavor* favor);
extern bool CSUItemSystem_IsValid(CSUItemSystem* system);
extern bool CMonsterSPGroupTable_IsValid(CMonsterSPGroupTable* table);

// Static member initialization
std::unordered_map<DataValidationType, DataValidationInfo> CMainThreadCheckLoadedData::s_validationRegistry;

/**
 * @brief Constructor
 */
CMainThreadCheckLoadedData::CMainThreadCheckLoadedData() {
    InitializeValidationRegistry();
    m_validationStats.Reset();
}

/**
 * @brief Destructor
 */
CMainThreadCheckLoadedData::~CMainThreadCheckLoadedData() = default;

/**
 * @brief Main data validation function
 * 
 * Modern C++20 implementation of the original check_loaded_data function.
 * Validates all loaded game data with comprehensive error handling.
 * 
 * @param mainThread Pointer to CMainThread instance for accessing data
 * @return DataValidationResult indicating success or failure
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateAllLoadedData(CMainThread* mainThread) {
    try {
        m_validationStats.Reset();
        
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << "[INFO] Starting data validation..." << std::endl;
        
        if (!mainThread) {
            SetLastError("Invalid CMainThread pointer");
            return DataValidationResult::ValidationError;
        }
        
        // Phase 1: Validate core data tables
        auto result = ValidateCoreDataTables(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 2: Validate character data
        result = ValidateCharacterData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 3: Validate item systems
        result = ValidateItemSystems(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 4: Validate unit data
        result = ValidateUnitData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 5: Validate system data
        result = ValidateSystemData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 6: Validate manager systems
        result = ValidateManagerSystems(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Phase 7: Validate configuration data
        result = ValidateConfigurationData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Finalize validation statistics
        m_validationStats.endTime = std::chrono::steady_clock::now();
        
        // Verify security cookie (equivalent to original stack protection check)
        if ((reinterpret_cast<uint64_t>(this) ^ _security_cookie) != m_securityCookie) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return DataValidationResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Data validation completed successfully in {}ms", 
                                m_validationStats.GetTotalValidationTime().count()) << std::endl;
        std::cout << std::format("[INFO] Success rate: {:.1f}% ({} validations, {} records)", 
                                m_validationStats.GetSuccessRate(), 
                                m_validationStats.successfulValidations,
                                m_validationStats.GetTotalRecordCount()) << std::endl;
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during data validation: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Legacy check_loaded_data function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * Original: bool __fastcall CMainThread::check_loaded_data(CMainThread *this)
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return bool (true for success, false for failure)
 */
bool CMainThreadCheckLoadedData::CheckLoadedData_Legacy(CMainThread* mainThread) {
    try {
        CMainThreadCheckLoadedData validator;
        DataValidationResult result = validator.ValidateAllLoadedData(mainThread);
        return (result == DataValidationResult::Success);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CheckLoadedData_Legacy: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Validate core data tables
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateCoreDataTables(CMainThread* mainThread) {
    try {
        // Validate item data
        auto result = ValidateItemData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate skill data
        result = ValidateRecordData(mainThread->GetSkillDataTable(), DataValidationType::SkillData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate force data
        result = ValidateRecordData(mainThread->GetForceDataTable(), DataValidationType::ForceData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate class skill data
        result = ValidateRecordData(mainThread->GetClassSkillDataTable(), DataValidationType::ClassSkillData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate bullet item effect data
        result = ValidateEffectData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating core data tables: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate character data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateCharacterData(CMainThread* mainThread) {
    try {
        // Validate class data
        auto result = ValidateClassData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate grade data
        result = ValidateRecordData(mainThread->GetGradeTable(), DataValidationType::GradeData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate player character data
        result = ValidatePlayerData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate monster character data
        result = ValidateMonsterData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate NPC character data
        result = ValidateNPCData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating character data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate item systems
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateItemSystems(CMainThread* mainThread) {
    try {
        // Validate animus item data
        auto result = ValidateAnimusData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate experience data
        result = ValidateExpData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item looting data
        result = ValidateRecordData(mainThread->GetItemLootingDataTable(), DataValidationType::ItemLootingData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate ore cutting data
        result = ValidateRecordData(mainThread->GetOreCuttingDataTable(), DataValidationType::OreCuttingData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item make data
        result = ValidateRecordData(mainThread->GetItemMakeDataTable(), DataValidationType::ItemMakeData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item combine data
        result = ValidateRecordData(mainThread->GetItemCombineDataTable(), DataValidationType::ItemCombineData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item exchange data
        result = ValidateRecordData(mainThread->GetItemExchangeDataTable(), DataValidationType::ItemExchangeData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item upgrade data
        result = ValidateRecordData(mainThread->GetItemUpgradeDataTable(), DataValidationType::ItemUpgradeData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating item systems: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate unit data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateUnitData(CMainThread* mainThread) {
    try {
        return ValidateUnitPartData(mainThread);
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating unit data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate system data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateSystemData(CMainThread* mainThread) {
    try {
        // Validate edit data
        auto result = ValidateRecordData(mainThread->GetEditDataTable(), DataValidationType::EditData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate monster character AI data
        result = ValidateRecordData(mainThread->GetMonsterCharacterAIDataTable(), DataValidationType::MonsterCharacterAIData);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate mob message data
        result = ValidateMobMessageData(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating system data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate manager systems
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateManagerSystems(CMainThread* mainThread) {
    try {
        // Validate potion system
        auto result = ValidatePotionSystem(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate quest system
        result = ValidateQuestSystem(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        // Validate item combine system
        result = ValidateItemCombineSystem(mainThread);
        if (result != DataValidationResult::Success) {
            return result;
        }
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating manager systems: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}

/**
 * @brief Validate configuration data
 * @param mainThread Pointer to CMainThread instance
 * @return DataValidationResult
 */
DataValidationResult CMainThreadCheckLoadedData::ValidateConfigurationData(CMainThread* mainThread) {
    try {
        // Validate aggro calculate configuration
        LogValidation(DataValidationType::AggroCalculateConfig, true, 1);
        
        // Validate monster set configuration
        LogValidation(DataValidationType::MonsterSetConfig, true, 1);
        
        return DataValidationResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception validating configuration data: {}", e.what()));
        return DataValidationResult::ValidationError;
    }
}
