/*
 * CQuestMgr_CheckNPCQuestList.cpp - NPC Quest List Checking Implementation
 * Refactored from CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.c
 * Compatible with Visual Studio 2022 (v143 toolset)
 */

#include "../Headers/CQuestMgr.h"
#include "../Headers/QuestStructures.h"
#include "../Headers/CRecordData.h"
#include "../Headers/CPlayer.h"
#include "../Headers/CPlayerDB.h"
#include "../../common/Headers/Logger.h"
#include "../../common/Headers/ErrorHandling.h"

#include <memory>
#include <stdexcept>
#include <algorithm>
#include <cassert>
#include <cstring>

// External dependencies (to be properly linked)
extern int strcmp_0(const char* str1, const char* str2);

/**
 * Check NPC quest list for available quests
 * Refactored from original CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.c
 * 
 * @param pszEventCode Event code to check for quests
 * @param byRaceCode Race code for race-specific quest filtering
 * @param pQuestIndexData Output structure to store found quest indices
 */
void CQuestMgr::CheckNPCQuestList(const char* pszEventCode, uint8_t byRaceCode, 
                                 NPCQuestIndexTempData* pQuestIndexData) {
    try {
        Logger::Debug("CQuestMgr::CheckNPCQuestList - Starting quest list check for event: %s, race: %d", 
                     pszEventCode ? pszEventCode : "NULL", byRaceCode);
        
        // Validate input parameters
        if (!ValidateInputParameters(pszEventCode, pQuestIndexData)) {
            Logger::Error("CQuestMgr::CheckNPCQuestList - Invalid input parameters");
            return;
        }
        
        // Initialize output data
        pQuestIndexData->Init();
        
        // Get initial quest happen event record
        auto* pEventRecord = GetQuestHappenEventRecord(pszEventCode);
        if (!pEventRecord) {
            Logger::Debug("CQuestMgr::CheckNPCQuestList - No event record found for: %s", pszEventCode);
            return;
        }
        
        // Process quest events
        ProcessQuestEvents(pEventRecord, pszEventCode, byRaceCode, pQuestIndexData);
        
        Logger::Debug("CQuestMgr::CheckNPCQuestList - Found %d available quests", 
                     pQuestIndexData->GetQuestCount());
        
        LogQuestOperation("CheckNPCQuestList", pszEventCode, true);
        
    } catch (const std::exception& e) {
        Logger::Error("CQuestMgr::CheckNPCQuestList - Exception occurred: %s", e.what());
        if (pQuestIndexData) {
            pQuestIndexData->Init(); // Clear any partial data
        }
        LogQuestOperation("CheckNPCQuestList", pszEventCode ? pszEventCode : "", false);
    } catch (...) {
        Logger::Error("CQuestMgr::CheckNPCQuestList - Unknown exception occurred");
        if (pQuestIndexData) {
            pQuestIndexData->Init(); // Clear any partial data
        }
        LogQuestOperation("CheckNPCQuestList", pszEventCode ? pszEventCode : "", false);
    }
}

/**
 * Get quest happen event record
 */
_base_fld* CQuestMgr::GetQuestHappenEventRecord(const char* pszEventCode) {
    if (!s_tblQuestHappenEvent) {
        Logger::Error("GetQuestHappenEventRecord - Quest happen event table not initialized");
        return nullptr;
    }
    
    try {
        // Get record from quest happen event table
        auto* pRecord = CRecordData::GetRecord(s_tblQuestHappenEvent + 1, pszEventCode);
        if (!pRecord) {
            Logger::Debug("GetQuestHappenEventRecord - No record found for event: %s", pszEventCode);
            return nullptr;
        }
        
        Logger::Debug("GetQuestHappenEventRecord - Found record for event: %s, index: %u", 
                     pszEventCode, pRecord->m_dwIndex);
        return pRecord;
        
    } catch (const std::exception& e) {
        Logger::Error("GetQuestHappenEventRecord - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Process quest events for the given event code
 */
void CQuestMgr::ProcessQuestEvents(const _base_fld* pInitialRecord, const char* pszEventCode, 
                                  uint8_t byRaceCode, NPCQuestIndexTempData* pQuestIndexData) {
    if (!pInitialRecord || !pszEventCode || !pQuestIndexData) {
        Logger::Error("ProcessQuestEvents - Invalid parameters");
        return;
    }
    
    uint32_t currentIndex = pInitialRecord->m_dwIndex;
    int questCount = 0;
    
    // Process up to MAX_QUEST_ITERATIONS quest events
    for (int iteration = 0; iteration < QuestConstants::MAX_QUEST_ITERATIONS; ++iteration) {
        // Get current event record
        auto* pEventRecord = CRecordData::GetRecord(s_tblQuestHappenEvent + 1, currentIndex);
        if (!pEventRecord) {
            Logger::Debug("ProcessQuestEvents - No more event records at index: %u", currentIndex);
            break;
        }
        
        // Verify event code matches
        if (strcmp_0(pszEventCode, pEventRecord->m_strCode) != 0) {
            Logger::Debug("ProcessQuestEvents - Event code mismatch at index: %u", currentIndex);
            break;
        }
        
        // Process this event
        questCount = ProcessSingleQuestEvent(pEventRecord, byRaceCode, pQuestIndexData, questCount);
        
        // Check if we've reached the maximum quest count
        if (pQuestIndexData->IsFull()) {
            Logger::Debug("ProcessQuestEvents - Quest index data is full, stopping");
            break;
        }
        
        // Move to next event
        currentIndex++;
    }
    
    Logger::Debug("ProcessQuestEvents - Processed %d iterations, found %d quests", 
                 iteration, questCount);
}

/**
 * Process a single quest event
 */
int CQuestMgr::ProcessSingleQuestEvent(const _base_fld* pEventRecord, uint8_t byRaceCode,
                                      NPCQuestIndexTempData* pQuestIndexData, int questCount) {
    if (!pEventRecord || !pQuestIndexData) {
        Logger::Error("ProcessSingleQuestEvent - Invalid parameters");
        return questCount;
    }
    
    try {
        // Calculate race-specific data pointer
        const char* pRaceData = CalculateRaceDataPointer(pEventRecord, byRaceCode);
        if (!pRaceData) {
            Logger::Debug("ProcessSingleQuestEvent - No race data for race: %d", byRaceCode);
            return questCount;
        }
        
        // Check if this race data is used
        if (!IsRaceDataUsed(pRaceData)) {
            Logger::Debug("ProcessSingleQuestEvent - Race data not used for race: %d", byRaceCode);
            return questCount;
        }
        
        // Validate event conditions
        if (!ValidateEventConditions(pRaceData)) {
            Logger::Debug("ProcessSingleQuestEvent - Event conditions not met");
            return questCount;
        }
        
        // Get quest record
        auto* pQuestRecord = GetQuestRecord(pRaceData);
        if (!pQuestRecord) {
            Logger::Debug("ProcessSingleQuestEvent - No quest record found");
            return questCount;
        }
        
        // Validate quest prerequisites
        if (!ValidateQuestPrerequisites(pQuestRecord)) {
            Logger::Debug("ProcessSingleQuestEvent - Quest prerequisites not met");
            return questCount;
        }
        
        // Check quest completion and progress status
        if (!CheckQuestCompletionStatus(pQuestRecord)) {
            Logger::Debug("ProcessSingleQuestEvent - Quest completion status check failed");
            return questCount;
        }
        
        // Add quest to index data
        if (AddQuestToIndexData(pQuestRecord, pEventRecord->m_dwIndex, pQuestIndexData)) {
            questCount++;
            Logger::Debug("ProcessSingleQuestEvent - Added quest index: %u, happen index: %u", 
                         pQuestRecord->m_dwIndex, pEventRecord->m_dwIndex);
        }
        
        return questCount;
        
    } catch (const std::exception& e) {
        Logger::Error("ProcessSingleQuestEvent - Exception: %s", e.what());
        return questCount;
    }
}

/**
 * Calculate race-specific data pointer
 */
const char* CQuestMgr::CalculateRaceDataPointer(const _base_fld* pEventRecord, uint8_t byRaceCode) {
    if (!pEventRecord) {
        return nullptr;
    }
    
    try {
        // Calculate offset: &pEventRecord[1].m_strCode[704 * byRaceCode]
        const char* basePtr = reinterpret_cast<const char*>(pEventRecord + 1);
        size_t raceOffset = QuestConstants::RACE_DATA_SIZE * static_cast<size_t>(byRaceCode);
        
        // Add offset to m_strCode field
        const char* raceDataPtr = basePtr + offsetof(_base_fld, m_strCode) + raceOffset;
        
        Logger::Debug("CalculateRaceDataPointer - Race: %d, Offset: %zu, Ptr: %p", 
                     byRaceCode, raceOffset, raceDataPtr);
        
        return raceDataPtr;
        
    } catch (const std::exception& e) {
        Logger::Error("CalculateRaceDataPointer - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Check if race data is used
 */
bool CQuestMgr::IsRaceDataUsed(const char* pRaceData) {
    if (!pRaceData) {
        return false;
    }
    
    // Check first DWORD of race data - if 0, not used
    const uint32_t* pUseFlag = reinterpret_cast<const uint32_t*>(pRaceData);
    bool isUsed = (*pUseFlag != 0);
    
    Logger::Debug("IsRaceDataUsed - Use flag: %u, Used: %s", *pUseFlag, isUsed ? "Yes" : "No");
    return isUsed;
}

/**
 * Validate event conditions
 */
bool CQuestMgr::ValidateEventConditions(const char* pRaceData) {
    if (!pRaceData) {
        Logger::Error("ValidateEventConditions - Invalid race data pointer");
        return false;
    }
    
    try {
        // Check up to MAX_CONDITIONS_PER_EVENT conditions
        for (int condIndex = 0; condIndex < QuestConstants::MAX_CONDITIONS_PER_EVENT; ++condIndex) {
            const QuestConditionNode* pCondition = QuestUtils::CalculateConditionNodeAddress(
                pRaceData, condIndex);
            
            if (!pCondition) {
                Logger::Error("ValidateEventConditions - Failed to calculate condition address for index: %d", 
                             condIndex);
                return false;
            }
            
            // If condition type is -1, we've reached the end of conditions
            if (pCondition->m_nCondType == QuestConditionType::None) {
                Logger::Debug("ValidateEventConditions - Reached end of conditions at index: %d", condIndex);
                break;
            }
            
            // Check this condition
            if (!CheckCondition(pCondition)) {
                Logger::Debug("ValidateEventConditions - Condition %d failed: %s", 
                             condIndex, QuestUtils::FormatConditionForLog(pCondition).c_str());
                return false;
            }
            
            Logger::Debug("ValidateEventConditions - Condition %d passed: %s", 
                         condIndex, QuestUtils::FormatConditionForLog(pCondition).c_str());
        }
        
        Logger::Debug("ValidateEventConditions - All conditions validated successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("ValidateEventConditions - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get quest record from race data
 */
_base_fld* CQuestMgr::GetQuestRecord(const char* pRaceData) {
    if (!pRaceData) {
        Logger::Error("GetQuestRecord - Invalid race data pointer");
        return nullptr;
    }
    
    if (!s_tblQuest) {
        Logger::Error("GetQuestRecord - Quest table not initialized");
        return nullptr;
    }
    
    try {
        // Quest code is at offset 384 from race data start
        const char* pQuestCode = pRaceData + 384;
        
        auto* pQuestRecord = CRecordData::GetRecord(s_tblQuest, pQuestCode);
        if (!pQuestRecord) {
            Logger::Debug("GetQuestRecord - No quest record found for code at offset 384");
            return nullptr;
        }
        
        Logger::Debug("GetQuestRecord - Found quest record: %s, index: %u", 
                     pQuestRecord->m_strCode, pQuestRecord->m_dwIndex);
        return pQuestRecord;
        
    } catch (const std::exception& e) {
        Logger::Error("GetQuestRecord - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Add quest to index data
 */
bool CQuestMgr::AddQuestToIndexData(const _base_fld* pQuestRecord, uint32_t happenIndex,
                                   NPCQuestIndexTempData* pQuestIndexData) {
    if (!pQuestRecord || !pQuestIndexData) {
        Logger::Error("AddQuestToIndexData - Invalid parameters");
        return false;
    }
    
    if (pQuestIndexData->IsFull()) {
        Logger::Warning("AddQuestToIndexData - Quest index data is full");
        return false;
    }
    
    bool success = pQuestIndexData->AddQuest(pQuestRecord->m_dwIndex, happenIndex);
    if (success) {
        Logger::Debug("AddQuestToIndexData - Successfully added quest: %u, happen: %u", 
                     pQuestRecord->m_dwIndex, happenIndex);
    } else {
        Logger::Error("AddQuestToIndexData - Failed to add quest: %u, happen: %u", 
                     pQuestRecord->m_dwIndex, happenIndex);
    }
    
    return success;
}

/**
 * Check level requirements for quest
 */
bool CQuestMgr::CheckLevelRequirements(const _base_fld* pQuestRecord) {
    if (!pQuestRecord || !m_pMaster) {
        Logger::Error("CheckLevelRequirements - Invalid parameters");
        return false;
    }

    try {
        // Get required level from quest record
        // Level requirement is at offset: pQuestRecord[1].m_dwIndex
        const _base_fld* pQuestData = pQuestRecord + 1;
        uint32_t requiredLevel = pQuestData->m_dwIndex;

        // If required level is -1 (0xFFFFFFFF), no level requirement
        if (requiredLevel == static_cast<uint32_t>(-1)) {
            Logger::Debug("CheckLevelRequirements - No level requirement for quest");
            return true;
        }

        // Get player's current level
        int playerLevel = CPlayerDB::GetLevel(&m_pMaster->m_Param);

        // Check if player level meets requirement
        bool levelMet = (playerLevel >= static_cast<int>(requiredLevel));

        Logger::Debug("CheckLevelRequirements - Player level: %d, Required: %u, Met: %s",
                     playerLevel, requiredLevel, levelMet ? "Yes" : "No");

        return levelMet;

    } catch (const std::exception& e) {
        Logger::Error("CheckLevelRequirements - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check quest completion status
 */
bool CQuestMgr::CheckQuestCompletionStatus(const _base_fld* pQuestRecord) {
    if (!pQuestRecord) {
        Logger::Error("CheckQuestCompletionStatus - Invalid quest record");
        return false;
    }

    try {
        const char* questCode = pQuestRecord->m_strCode;

        // Get quest repeat type from record
        // Repeat type is at offset: pQuestRecord[1].m_strCode[4]
        const _base_fld* pQuestData = pQuestRecord + 1;
        int questRepeatType = *reinterpret_cast<const int*>(&pQuestData->m_strCode[4]);

        Logger::Debug("CheckQuestCompletionStatus - Quest: %s, Repeat type: %d",
                     questCode, questRepeatType);

        // Check if quest is already completed
        if (IsCompleteNpcQuest(questCode, questRepeatType)) {
            Logger::Debug("CheckQuestCompletionStatus - Quest already completed: %s", questCode);
            return false;
        }

        // Check if quest is already in progress
        if (IsProcNpcQuest(questCode)) {
            Logger::Debug("CheckQuestCompletionStatus - Quest already in progress: %s", questCode);
            return false;
        }

        // For repeatable quests, check additional conditions
        if (questRepeatType == static_cast<int>(QuestRepeatType::Daily) ||
            questRepeatType == static_cast<int>(QuestRepeatType::Weekly) ||
            questRepeatType == static_cast<int>(QuestRepeatType::Monthly)) {

            if (!CheckRepeatableQuestConditions(pQuestRecord)) {
                Logger::Debug("CheckQuestCompletionStatus - Repeatable quest conditions not met: %s", questCode);
                return false;
            }
        }

        Logger::Debug("CheckQuestCompletionStatus - Quest can be started: %s", questCode);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CheckQuestCompletionStatus - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check repeatable quest conditions
 */
bool CQuestMgr::CheckRepeatableQuestConditions(const _base_fld* pQuestRecord) {
    if (!pQuestRecord) {
        Logger::Error("CheckRepeatableQuestConditions - Invalid quest record");
        return false;
    }

    try {
        const char* questCode = pQuestRecord->m_strCode;

        // Get link index from quest record
        // Link index is at offset: pQuestRecord[27].m_strCode[24]
        const _base_fld* pLinkData = pQuestRecord + 27;
        uint32_t linkIndex = *reinterpret_cast<const uint32_t*>(&pLinkData->m_strCode[24]);

        Logger::Debug("CheckRepeatableQuestConditions - Quest: %s, Link index: %u",
                     questCode, linkIndex);

        // Check if linked quest is in progress
        if (!IsProcLinkNpcQuest(questCode, linkIndex)) {
            Logger::Debug("CheckRepeatableQuestConditions - Linked quest not in progress");
            return false;
        }

        // Check if repeat is possible
        if (!IsPossibleRepeatNpcQuest(questCode, linkIndex)) {
            Logger::Debug("CheckRepeatableQuestConditions - Repeat not possible");
            return false;
        }

        Logger::Debug("CheckRepeatableQuestConditions - Repeatable quest conditions met");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CheckRepeatableQuestConditions - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate quest prerequisites
 */
bool CQuestMgr::ValidateQuestPrerequisites(const _base_fld* pQuestRecord) {
    if (!pQuestRecord) {
        Logger::Error("ValidateQuestPrerequisites - Invalid quest record");
        return false;
    }

    try {
        // Check level requirements
        if (!CheckLevelRequirements(pQuestRecord)) {
            Logger::Debug("ValidateQuestPrerequisites - Level requirements not met");
            return false;
        }

        // Additional prerequisite checks can be added here
        // For example: class requirements, item requirements, etc.

        Logger::Debug("ValidateQuestPrerequisites - All prerequisites validated");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("ValidateQuestPrerequisites - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate input parameters
 */
bool CQuestMgr::ValidateInputParameters(const char* pszEventCode,
                                       const NPCQuestIndexTempData* pQuestIndexData) const {
    if (!pszEventCode) {
        Logger::Error("ValidateInputParameters - Event code is null");
        return false;
    }

    if (!pQuestIndexData) {
        Logger::Error("ValidateInputParameters - Quest index data is null");
        return false;
    }

    if (!QuestUtils::IsValidEventCode(pszEventCode)) {
        Logger::Error("ValidateInputParameters - Invalid event code format: %s", pszEventCode);
        return false;
    }

    if (!m_pMaster) {
        Logger::Error("ValidateInputParameters - Master player is null");
        return false;
    }

    if (!m_pQuestData) {
        Logger::Error("ValidateInputParameters - Quest data is null");
        return false;
    }

    Logger::Debug("ValidateInputParameters - All parameters validated successfully");
    return true;
}
