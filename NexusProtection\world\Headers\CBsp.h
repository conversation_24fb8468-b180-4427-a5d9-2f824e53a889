/*
 * CBsp.h - BSP (Binary Space Partitioning) System
 * Refactored for Visual Studio 2022 compatibility
 * Original: LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 */

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>

// Forward declarations
class CEntity;
class CParticle;
class CMergeFileManager;

/**
 * Entity list structure for BSP entities
 */
struct _ENTITY_LIST {
    char Name[256];             // Entity name/path
    uint32_t ShaderID;          // Shader identifier
    uint32_t Flag;              // Entity flags
    bool IsParticle;            // Whether this is a particle entity
    bool IsFileExist;           // Whether the file exists and loaded successfully
    uint16_t wPadding;          // Padding for alignment
    
    _ENTITY_LIST() 
        : ShaderID(0), Flag(0), IsParticle(false), IsFileExist(false), wPadding(0) {
        memset(Name, 0, sizeof(Name));
    }
};

/**
 * Map entities list structure for positioned entities
 */
struct _MAP_ENTITIES_LIST {
    uint16_t ID;                // Entity ID reference
    uint16_t wPadding1;         // Padding
    float Scale;                // Entity scale
    std::array<float, 3> Pos;   // Position [x, y, z]
    float RotX;                 // X rotation
    float RotY;                 // Y rotation
    std::array<int16_t, 3> BBMin; // Bounding box minimum
    std::array<int16_t, 3> BBMax; // Bounding box maximum
    float AddFrame;             // Additional frame offset
    CParticle* Particle;        // Particle instance (if applicable)
    
    _MAP_ENTITIES_LIST() 
        : ID(0), wPadding1(0), Scale(1.0f), RotX(0.0f), RotY(0.0f)
        , AddFrame(0.0f), Particle(nullptr) {
        Pos.fill(0.0f);
        BBMin.fill(0);
        BBMax.fill(0);
    }
};

/**
 * Read map entities list structure for loading
 */
struct _READ_MAP_ENTITIES_LIST {
    uint16_t ID;                // Entity ID
    float Scale;                // Entity scale
    std::array<float, 3> Pos;   // Position [x, y, z]
    float RotX;                 // X rotation
    float RotY;                 // Y rotation
    std::array<int16_t, 3> BBMin; // Bounding box minimum
    std::array<int16_t, 3> BBMax; // Bounding box maximum
    
    _READ_MAP_ENTITIES_LIST() 
        : ID(0), Scale(1.0f), RotX(0.0f), RotY(0.0f) {
        Pos.fill(0.0f);
        BBMin.fill(0);
        BBMax.fill(0);
    }
};

/**
 * BSP entity loading result
 */
enum class BSPLoadResult : int32_t {
    Success = 0,                // Loading successful
    InvalidParameters = -1,     // Invalid input parameters
    MemoryAllocationFailed = -2, // Memory allocation failed
    FileNotFound = -3,          // Entity file not found
    LoadingFailed = -4,         // Entity loading failed
    ParticleLoadFailed = -5     // Particle loading failed
};

/**
 * BSP entity loading statistics
 */
struct BSPLoadStatistics {
    uint32_t totalEntities;     // Total entities to load
    uint32_t loadedEntities;    // Successfully loaded entities
    uint32_t failedEntities;    // Failed to load entities
    uint32_t particleEntities;  // Particle entities
    uint32_t regularEntities;   // Regular entities
    uint32_t memoryAllocated;   // Total memory allocated (bytes)
    
    BSPLoadStatistics() 
        : totalEntities(0), loadedEntities(0), failedEntities(0)
        , particleEntities(0), regularEntities(0), memoryAllocated(0) {}
    
    void Reset() {
        totalEntities = 0;
        loadedEntities = 0;
        failedEntities = 0;
        particleEntities = 0;
        regularEntities = 0;
        memoryAllocated = 0;
    }
};

/**
 * BSP (Binary Space Partitioning) class for entity management
 */
class CBsp {
public:
    // Constants
    static constexpr size_t ENTITY_SIZE = 244;
    static constexpr size_t PARTICLE_SIZE = 1168;
    static constexpr size_t TOTAL_ENTITY_SIZE = ENTITY_SIZE + PARTICLE_SIZE;
    static constexpr size_t MAX_ENTITY_NAME_LENGTH = 256;
    static constexpr float DEFAULT_FRAME_VARIANCE = 0.25f;
    
    /**
     * Constructor
     */
    CBsp();
    
    /**
     * Destructor
     */
    ~CBsp();
    
    /**
     * Load entities from entity list
     * Refactored from LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
     *
     * @param pEntityList Pointer to read map entities list
     * @return BSPLoadResult indicating the result of loading
     */
    BSPLoadResult LoadEntities(const _READ_MAP_ENTITIES_LIST* pEntityList);

    /**
     * Legacy C-style LoadEntities function for compatibility
     * Maintains original function signature for existing code
     *
     * @param pEntityList Pointer to read map entities list (legacy format)
     */
    void LoadEntities_Legacy(struct _READ_MAP_ENTITIES_LIST* pEntityList);
    
    /**
     * Frame move for map entities
     * @return true if frame move successful
     */
    bool FrameMoveMapEntities();
    
    /**
     * Draw map entities for rendering
     * @return true if drawing successful
     */
    bool DrawMapEntitiesRender();
    
    /**
     * Initialize BSP system
     * @return true if initialization successful
     */
    bool Initialize();
    
    /**
     * Cleanup BSP system
     */
    void Cleanup();
    
    /**
     * Get entity by index
     * @param index Entity index
     * @return Pointer to entity or nullptr if invalid
     */
    CEntity* GetEntity(uint32_t index) const;
    
    /**
     * Get particle by index
     * @param index Particle index
     * @return Pointer to particle or nullptr if invalid
     */
    CParticle* GetParticle(uint32_t index) const;
    
    /**
     * Get map entity by index
     * @param index Map entity index
     * @return Pointer to map entity or nullptr if invalid
     */
    _MAP_ENTITIES_LIST* GetMapEntity(uint32_t index) const;
    
    /**
     * Get loading statistics
     * @return Current loading statistics
     */
    const BSPLoadStatistics& GetStatistics() const { return m_statistics; }
    
    /**
     * Set debugging mode
     * @param enable Whether to enable debugging
     */
    void SetDebugging(bool enable) { m_debuggingEnabled = enable; }
    
    /**
     * Get entity count
     * @return Number of entities
     */
    uint32_t GetEntityCount() const { return mEntityListNum; }
    
    /**
     * Get map entity count
     * @return Number of map entities
     */
    uint32_t GetMapEntityCount() const { return mMapEntitiesListNum; }

    /**
     * Get entity statistics summary
     * @return Formatted statistics string
     */
    std::string GetStatisticsSummary() const;

    /**
     * Validate BSP system state
     * @return true if system state is valid
     */
    bool ValidateSystemState() const;

    /**
     * Get memory usage information
     * @return Total memory usage in bytes
     */
    uint32_t GetMemoryUsage() const;

    /**
     * Reset BSP system
     * @return true if reset successful
     */
    bool Reset();

public:
    // Member variables (public for compatibility with original code)
    CEntity* mEntity;                           // Entity array
    CParticle* mParticle;                       // Particle array
    _ENTITY_LIST* mEntityList;                  // Entity list
    _MAP_ENTITIES_LIST* mMapEntitiesList;       // Map entities list
    char* mEntityCache;                         // Entity cache
    CMergeFileManager mMapEntityMFM;            // Merge file manager
    uint32_t mEntityListNum;                    // Number of entities in list
    uint32_t mMapEntitiesListNum;               // Number of map entities
    uint32_t mTotalAllocSize;                   // Total allocated size

private:
    BSPLoadStatistics m_statistics;             // Loading statistics
    bool m_debuggingEnabled;                    // Debug mode flag
    bool m_initialized;                         // Initialization flag
    
    /**
     * Allocate memory for entities
     * @return true if allocation successful
     */
    bool AllocateEntityMemory();
    
    /**
     * Load single entity
     * @param index Entity index
     * @param entityPath Entity file path
     * @return true if loading successful
     */
    bool LoadSingleEntity(uint32_t index, const std::string& entityPath);
    
    /**
     * Load single particle
     * @param index Particle index
     * @param particlePath Particle file path
     * @return true if loading successful
     */
    bool LoadSingleParticle(uint32_t index, const std::string& particlePath);
    
    /**
     * Process map entities from read list
     * @param pEntityList Pointer to read map entities list
     * @return true if processing successful
     */
    bool ProcessMapEntities(const _READ_MAP_ENTITIES_LIST* pEntityList);
    
    /**
     * Build entity file path
     * @param entityName Entity name
     * @param basePath Base path for entities
     * @return Complete file path
     */
    std::string BuildEntityPath(const char* entityName, const char* basePath) const;

    /**
     * Load individual entities
     * @return BSPLoadResult indicating the result
     */
    BSPLoadResult LoadIndividualEntities();

    /**
     * Create particle instance for map entity
     * @param mapIndex Map entity index
     * @return true if creation successful
     */
    bool CreateParticleInstance(uint32_t mapIndex);

    /**
     * Clear map entity data
     * @param mapIndex Map entity index
     */
    void ClearMapEntity(uint32_t mapIndex);
    
    /**
     * Generate random frame offset
     * @return Random frame offset value
     */
    float GenerateRandomFrameOffset() const;
    
    /**
     * Validate entity list
     * @param pEntityList Pointer to entity list
     * @return true if valid
     */
    bool ValidateEntityList(const _READ_MAP_ENTITIES_LIST* pEntityList) const;
    
    /**
     * Update statistics
     * @param entityType Type of entity processed
     * @param success Whether processing was successful
     */
    void UpdateStatistics(bool isParticle, bool success);
    
    /**
     * Log BSP operation
     * @param operation Operation description
     * @param result Operation result
     */
    void LogBSPOperation(const std::string& operation, BSPLoadResult result) const;
    
    /**
     * Log debug information
     * @param message Debug message
     */
    void LogDebug(const std::string& message) const;
};

/**
 * BSP utility functions
 */
namespace BSPUtils {
    /**
     * Convert BSPLoadResult to string
     * @param result Load result
     * @return String representation
     */
    std::string LoadResultToString(BSPLoadResult result);
    
    /**
     * Validate entity name
     * @param entityName Entity name to validate
     * @return true if valid
     */
    bool IsValidEntityName(const char* entityName);
    
    /**
     * Get entity file extension
     * @param entityName Entity name
     * @return File extension or empty string
     */
    std::string GetEntityExtension(const char* entityName);
    
    /**
     * Format entity path for logging
     * @param entityPath Entity path
     * @return Formatted path string
     */
    std::string FormatEntityPath(const std::string& entityPath);
}

// External function declarations (to be properly linked)
extern void* Dmalloc(size_t size);
extern void memset_0(void* ptr, int value, size_t size);
extern void memcpy_0(void* dest, const void* src, size_t size);
extern void SetMergeFileManager(CMergeFileManager* mfm);
extern void Warning(const char* filename, const char* message);
extern uint32_t dword_184A797AC;
extern const char byte_184A790F0[];
