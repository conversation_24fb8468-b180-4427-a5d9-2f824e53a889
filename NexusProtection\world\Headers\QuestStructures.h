/*
 * QuestStructures.h - Quest System Data Structures
 * Refactored for Visual Studio 2022 compatibility
 * Original: Various quest-related structures from decompiled source
 */

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <array>
#include <chrono>

// Forward declarations
class CPlayer;
class CQuestMgr;
struct _base_fld;

/**
 * Quest happen event types
 */
enum class QuestHappenType : uint32_t {
    None = 0,
    NPC = 1,
    Item = 2,
    Monster = 3,
    Area = 4,
    Time = 5,
    Level = 6,
    Custom = 99
};

/**
 * Quest condition types
 */
enum class QuestConditionType : int32_t {
    None = -1,
    Level = 0,
    Class = 1,
    Item = 2,
    Quest = 3,
    Skill = 4,
    Stat = 5,
    Time = 6,
    Custom = 99
};

/**
 * Quest repeat types
 */
enum class QuestRepeatType : int32_t {
    None = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Unlimited = 4
};

/**
 * Quest status types
 */
enum class QuestStatus : uint8_t {
    NotStarted = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Abandoned = 4,
    Repeatable = 5
};

/**
 * Quest condition node structure
 */
struct QuestConditionNode {
    QuestConditionType m_nCondType;    // Condition type
    int32_t m_nParam1;                 // First parameter
    int32_t m_nParam2;                 // Second parameter
    int32_t m_nParam3;                 // Third parameter
    char m_strParam[32];               // String parameter
    bool m_bNegate;                    // Negate condition result
    
    QuestConditionNode() 
        : m_nCondType(QuestConditionType::None)
        , m_nParam1(0), m_nParam2(0), m_nParam3(0)
        , m_bNegate(false) {
        memset(m_strParam, 0, sizeof(m_strParam));
    }
    
    // Validation
    bool IsValid() const {
        return m_nCondType != QuestConditionType::None;
    }
    
    // String utilities
    void SetStringParam(const char* param) {
        if (param) {
            strncpy_s(m_strParam, sizeof(m_strParam), param, _TRUNCATE);
        }
    }
    
    std::string GetStringParam() const {
        return std::string(m_strParam);
    }
};

/**
 * Quest happen event node structure
 */
struct QuestHappenEventNode {
    bool m_bUse;                                    // Whether this event is active
    char m_strLinkQuest[32];                        // Linked quest code
    bool m_bSelectQuestManual;                      // Manual quest selection
    std::array<QuestConditionNode, 5> m_CondNode;  // Condition nodes
    uint32_t m_dwFlags;                             // Event flags
    uint32_t m_dwPriority;                          // Event priority
    
    QuestHappenEventNode() 
        : m_bUse(false), m_bSelectQuestManual(false)
        , m_dwFlags(0), m_dwPriority(0) {
        memset(m_strLinkQuest, 0, sizeof(m_strLinkQuest));
    }
    
    // Validation
    bool IsValid() const {
        return m_bUse && strlen(m_strLinkQuest) > 0;
    }
    
    // Condition checking
    bool HasValidConditions() const {
        for (const auto& cond : m_CondNode) {
            if (cond.IsValid()) {
                return true;
            }
        }
        return false;
    }
    
    size_t GetConditionCount() const {
        size_t count = 0;
        for (const auto& cond : m_CondNode) {
            if (cond.IsValid()) {
                count++;
            }
        }
        return count;
    }
};

/**
 * Quest happen event container
 */
struct QuestHappenEventContainer {
    QuestHappenEventNode* m_pEvent;     // Pointer to event node
    QuestHappenType m_QtHpType;         // Happen type
    uint32_t m_nIndexInType;            // Index in type
    uint8_t m_nRaceCode;                // Race code
    bool m_bActive;                     // Whether container is active
    std::chrono::system_clock::time_point m_timeCreated; // Creation time
    
    QuestHappenEventContainer() 
        : m_pEvent(nullptr)
        , m_QtHpType(QuestHappenType::None)
        , m_nIndexInType(0), m_nRaceCode(0)
        , m_bActive(false)
        , m_timeCreated(std::chrono::system_clock::now()) {}
    
    // Initialization
    void Init() {
        m_pEvent = nullptr;
        m_QtHpType = QuestHappenType::None;
        m_nIndexInType = 0;
        m_nRaceCode = 0;
        m_bActive = false;
        m_timeCreated = std::chrono::system_clock::now();
    }
    
    // Set event data
    void Set(QuestHappenEventNode* pEvent, QuestHappenType type, 
             uint32_t indexInType, uint8_t raceCode) {
        m_pEvent = pEvent;
        m_QtHpType = type;
        m_nIndexInType = indexInType;
        m_nRaceCode = raceCode;
        m_bActive = (pEvent != nullptr);
        m_timeCreated = std::chrono::system_clock::now();
    }
    
    // Check if set
    bool IsSet() const {
        return m_bActive && m_pEvent != nullptr;
    }
    
    // Validation
    bool IsValid() const {
        return IsSet() && m_pEvent->IsValid();
    }
    
    // Age checking
    std::chrono::seconds GetAge() const {
        auto now = std::chrono::system_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(now - m_timeCreated);
    }
};

/**
 * NPC Quest Index Data structure
 */
struct NPCQuestIndexData {
    uint32_t dwQuestIndex;          // Quest index
    uint32_t dwQuestHappenIndex;    // Quest happen index
    
    NPCQuestIndexData() : dwQuestIndex(0), dwQuestHappenIndex(0) {}
    
    NPCQuestIndexData(uint32_t questIdx, uint32_t happenIdx) 
        : dwQuestIndex(questIdx), dwQuestHappenIndex(happenIdx) {}
    
    bool IsValid() const {
        return dwQuestIndex > 0 && dwQuestHappenIndex > 0;
    }
};

/**
 * NPC Quest Index Temp Data structure
 */
struct NPCQuestIndexTempData {
    static constexpr size_t MAX_QUEST_COUNT = 30;
    
    std::array<NPCQuestIndexData, MAX_QUEST_COUNT> IndexData;
    int32_t nQuestNum;              // Number of valid quests
    
    NPCQuestIndexTempData() : nQuestNum(0) {}
    
    // Initialize
    void Init() {
        nQuestNum = 0;
        for (auto& data : IndexData) {
            data = NPCQuestIndexData();
        }
    }
    
    // Add quest
    bool AddQuest(uint32_t questIndex, uint32_t happenIndex) {
        if (nQuestNum >= MAX_QUEST_COUNT) {
            return false;
        }
        
        IndexData[nQuestNum] = NPCQuestIndexData(questIndex, happenIndex);
        nQuestNum++;
        return true;
    }
    
    // Get quest count
    int32_t GetQuestCount() const {
        return nQuestNum;
    }
    
    // Get quest data
    const NPCQuestIndexData* GetQuestData(int32_t index) const {
        if (index >= 0 && index < nQuestNum) {
            return &IndexData[index];
        }
        return nullptr;
    }
    
    // Validation
    bool IsValid() const {
        return nQuestNum >= 0 && nQuestNum <= MAX_QUEST_COUNT;
    }
    
    // Clear all data
    void Clear() {
        Init();
    }
    
    // Check if full
    bool IsFull() const {
        return nQuestNum >= MAX_QUEST_COUNT;
    }
};

/**
 * Quest database list structure
 */
struct QuestDBList {
    uint8_t byQuestType;            // Quest type
    uint16_t wIndex;                // Quest index
    std::array<uint16_t, 3> wNum;   // Quest numbers
    uint32_t dwPassSec;             // Passed seconds
    
    QuestDBList() 
        : byQuestType(255), wIndex(65535), dwPassSec(0) {
        wNum.fill(65535);
    }
    
    void Init() {
        byQuestType = 255;
        wIndex = 65535;
        wNum.fill(65535);
        dwPassSec = 0;
    }
    
    bool IsValid() const {
        return byQuestType != 255 && wIndex != 65535;
    }
};

/**
 * Quest history entry
 */
struct QuestHistoryEntry {
    char szQuestCode[13];           // Quest code (12 chars + null terminator)
    
    QuestHistoryEntry() {
        memset(szQuestCode, 0, sizeof(szQuestCode));
        szQuestCode[12] = 255;  // Mark as unused
    }
    
    void SetQuestCode(const char* code) {
        if (code) {
            strncpy_s(szQuestCode, sizeof(szQuestCode) - 1, code, _TRUNCATE);
            szQuestCode[12] = 0;  // Mark as used
        }
    }
    
    bool IsUsed() const {
        return szQuestCode[12] != 255;
    }
    
    void Clear() {
        memset(szQuestCode, 0, sizeof(szQuestCode));
        szQuestCode[12] = 255;  // Mark as unused
    }
    
    std::string GetQuestCode() const {
        if (IsUsed()) {
            return std::string(szQuestCode, 12);
        }
        return std::string();
    }
};

/**
 * Quest database base structure
 */
struct QuestDBBase {
    static constexpr size_t MAX_QUEST_LIST = 30;
    static constexpr size_t MAX_QUEST_HISTORY = 70;
    
    std::array<QuestDBList, MAX_QUEST_LIST> m_List;
    std::array<QuestHistoryEntry, MAX_QUEST_HISTORY> m_History;
    
    QuestDBBase() {
        Init();
    }
    
    void Init() {
        for (auto& list : m_List) {
            list.Init();
        }
        for (auto& history : m_History) {
            history.Clear();
        }
    }
    
    // Quest list management
    QuestDBList* GetQuestList(size_t index) {
        if (index < MAX_QUEST_LIST) {
            return &m_List[index];
        }
        return nullptr;
    }
    
    // History management
    bool AddToHistory(const char* questCode) {
        for (auto& entry : m_History) {
            if (!entry.IsUsed()) {
                entry.SetQuestCode(questCode);
                return true;
            }
        }
        return false; // History full
    }
    
    bool IsInHistory(const char* questCode) const {
        if (!questCode) return false;
        
        for (const auto& entry : m_History) {
            if (entry.IsUsed() && strncmp(entry.szQuestCode, questCode, 7) == 0) {
                return true;
            }
        }
        return false;
    }
};

/**
 * Quest constants
 */
namespace QuestConstants {
    constexpr int MAX_QUEST_ITERATIONS = 30;
    constexpr int MAX_CONDITIONS_PER_EVENT = 5;
    constexpr int MAX_TEMP_HAPPEN_EVENTS = 3;
    constexpr int MAX_QUEST_CODE_LENGTH = 32;
    constexpr int MAX_EVENT_CODE_LENGTH = 32;
    constexpr size_t RACE_DATA_SIZE = 704;
    constexpr size_t CONDITION_NODE_SIZE = 72;
    constexpr size_t CONDITION_NODE_OFFSET = 24;
}

#endif // QUESTSTRUCTURES_H
